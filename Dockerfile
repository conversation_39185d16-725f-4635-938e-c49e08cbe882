FROM registry.cn-beijing.aliyuncs.com/quake/java8:v1.0

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

# 配置环境变量
ENV MEM_OPT "-XX:+UseG1GC -Xmx512m"

# 创建目录
RUN mkdir -p /data/server/bin/
RUN mkdir -p /data/logs/

# 拷贝文件
COPY target/birthday-0.0.1-SNAPSHOT.jar /data/server/bin/server.jar

RUN echo 'java $MEM_OPT -Dfile.encoding=UTF-8 \
           -jar -Duser.timezone=Asia/Shanghai \
           /data/server/bin/server.jar' > /data/server/bin/start.sh

# 设置脚本权限
RUN chmod +x /data/server/bin/start.sh
CMD /data/server/bin/start.sh
