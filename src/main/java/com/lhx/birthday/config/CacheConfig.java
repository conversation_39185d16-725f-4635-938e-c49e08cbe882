package com.lhx.birthday.config;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CacheConfig {

    private TimedCache<String, Object> longCache = CacheUtil.newTimedCache(600 * 1000); // 缓存有效期为600秒

    private TimedCache<String, Object> shortCache = CacheUtil.newTimedCache(10 * 1000); // 缓存有效期为10秒

    private TimedCache<String, Object> tokenCache = CacheUtil.newTimedCache(60 * 1000 * 60 * 24 * 20); // 缓存有效期为600秒

    public Object getLongCachedData(String cacheKey) {
        return longCache.get(cacheKey);
    }

    public void putLongCachedData(String cacheKey, Object data) {
        longCache.put(cacheKey, data);
    }

    public Object getShortCachedData(String cacheKey) {
        return shortCache.get(cacheKey);
    }

    public void putShortCachedData(String cacheKey, Object data) {
        shortCache.put(cacheKey, data);
    }

    public Object getTokenCachedData(String cacheKey) {
        return tokenCache.get(cacheKey);
    }

    public void putTokenCachedData(String cacheKey, Object data) {
        tokenCache.put(cacheKey, data);
    }

}
