package com.lhx.birthday.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Redis压缩配置类
 * 用于配置Redis数据压缩的相关参数
 */
@Configuration
@ConfigurationProperties(prefix = "redis.compress")
public class RedisCompressConfig {

    /**
     * 是否启用压缩
     */
    private boolean enabled = true;

    /**
     * 压缩阈值（字节），大于此值的数据会被压缩
     * 默认8KB
     */
    private int threshold = 8192;

    /**
     * 压缩级别（1-9），1为最快压缩，9为最高压缩率
     * 默认为6，平衡速度和压缩率
     */
    private int level = 6;

    /**
     * 是否在日志中记录压缩统计信息
     */
    private boolean logStats = false;

    /**
     * 批量操作时的批次大小
     */
    private int batchSize = 50;

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public int getThreshold() {
        return threshold;
    }

    public void setThreshold(int threshold) {
        this.threshold = threshold;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        if (level < 1) {
            this.level = 1;
        } else if (level > 9) {
            this.level = 9;
        } else {
            this.level = level;
        }
    }

    public boolean isLogStats() {
        return logStats;
    }

    public void setLogStats(boolean logStats) {
        this.logStats = logStats;
    }

    public int getBatchSize() {
        return batchSize;
    }

    public void setBatchSize(int batchSize) {
        if (batchSize < 10) {
            this.batchSize = 10;
        } else {
            this.batchSize = batchSize;
        }
    }
}