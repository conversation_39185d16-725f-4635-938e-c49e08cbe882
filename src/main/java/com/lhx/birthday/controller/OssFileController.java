package com.lhx.birthday.controller;

import com.lhx.birthday.request.oss.OssAddRequest;
import com.lhx.birthday.response.StsResponse;
import com.lhx.birthday.response.UploadResponse;
import com.lhx.birthday.service.IOssFileService;
import com.lhx.birthday.service.IUserInfoService;
import com.lhx.birthday.util.CommonUtil;
import com.lhx.birthday.vo.Result;
import com.lhx.birthday.vo.UploadVO;
import com.lhx.birthday.vo.UserInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Objects;

@RequestMapping("/oss")
@RestController
@Slf4j
@Api(description = "app接口 - oss上传", tags = "OssFileController")
public class OssFileController {

    @Autowired
    private IOssFileService ossFileService;

    @Autowired
    private CommonUtil commonUtil;

    @Autowired
    private IUserInfoService userInfoService;

    /**
     * oss文件上传
     * @param multipartFile
     * @return
     */
    @ApiOperation("上传文件至oss")
    @ResponseBody
    @PostMapping("/upload")
    public Result<UploadResponse> upload(@RequestParam("file") MultipartFile multipartFile) {
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        Result<UploadResponse> result = new Result();
        try {
            UploadVO upload = ossFileService.upload(multipartFile,userInfoVO);
            UploadResponse uploadResponse = UploadResponse.builder()
                    .uploadInfo(upload)
                    .build();
            return Result.OK("上传成功！",uploadResponse);
        } catch (Exception ex) {
            log.info(ex.getMessage(), ex);
            result.error500("上传失败");
        }
        return result;
    }

    @ApiOperation("保存附件")
    @ResponseBody
    @PostMapping("/add")
    public Result<UploadResponse> add(@RequestBody OssAddRequest ossAddRequest) {
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        UploadVO upload = ossFileService.add(ossAddRequest,userInfoVO);
        UploadResponse uploadResponse = UploadResponse.builder()
                .uploadInfo(upload)
                .build();
        return Result.OK(uploadResponse);
    }

    /**
     * 获取sts凭证
     * @return
     */
    @ApiOperation("获取sts凭证")
    @ResponseBody
    @PostMapping("/sts")
    public Result sts() {
        return Result.OK(ossFileService.sts());
    }

}
