package com.lhx.birthday.controller;

import com.lhx.birthday.request.operate.CdkeyExchangeRequest;
import com.lhx.birthday.request.operate.CdkeyGenerateRequest;
import com.lhx.birthday.request.operate.ExpireRequest;
import com.lhx.birthday.request.operate.RegisterRequest;
import com.lhx.birthday.response.market.IntegralProductResponse;
import com.lhx.birthday.response.operate.CdkeyGenerateResponse;
import com.lhx.birthday.service.ICdkeyService;
import com.lhx.birthday.vo.Result;
import com.lhx.birthday.vo.market.PointVipVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/operate")
@RestController
@Slf4j
@Api(description = "app接口 - 运营相关", tags = "OperateController")
public class OperateController {

    @Autowired
    private ICdkeyService cdkeyService;

    /**
     * 生成激活码
     * @return
     */
    @ApiOperation("生成激活码")
    @PostMapping("/generate")
    public Result<CdkeyGenerateResponse> generate(@RequestBody CdkeyGenerateRequest cdkeyGenerateRequest) {
        return cdkeyService.generate(cdkeyGenerateRequest);
    }

    /**
     * 兑换激活码
     * @param cdkeyExchangeRequest
     * @return
     */
    @ApiOperation("兑换激活码")
    @PostMapping("/exchange")
    public Result exchange(@RequestBody CdkeyExchangeRequest cdkeyExchangeRequest) {
        return cdkeyService.exchange(cdkeyExchangeRequest);
    }

    /**
     * 会员过期
     * @param expireRequest
     * @return
     */
    @ApiOperation("会员过期")
    @PostMapping("/expire")
    public Result expire(@RequestBody ExpireRequest expireRequest) {
        return cdkeyService.expire(expireRequest);
    }

    /**
     * 注册会员
     * @param registerRequest
     * @return
     */
    @ApiOperation("注册会员")
    @PostMapping("/register")
    public Result register(@RequestBody RegisterRequest registerRequest) {
        return cdkeyService.register(registerRequest);
    }

}
