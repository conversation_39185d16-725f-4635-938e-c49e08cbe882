package com.lhx.birthday.controller.pay;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileWriter;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.SM2;
import cn.hutool.http.ContentType;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ijpay.core.IJPayHttpResponse;
import com.ijpay.core.enums.AuthTypeEnum;
import com.ijpay.core.enums.RequestMethodEnum;
import com.ijpay.core.kit.AesUtil;
import com.ijpay.core.kit.HttpKit;
import com.ijpay.core.kit.PayKit;
import com.ijpay.core.kit.WxPayKit;
import com.ijpay.core.utils.DateTimeZoneUtil;
import com.ijpay.wxpay.WxPayApi;
import com.ijpay.wxpay.WxPayApiConfigKit;
import com.ijpay.wxpay.enums.WxDomainEnum;
import com.ijpay.wxpay.enums.v3.*;
import com.ijpay.wxpay.model.v3.*;
import com.lhx.birthday.entity.TvMemberRechargeType;
import com.lhx.birthday.entity.UserInfo;
import com.lhx.birthday.entity.pay.PayOrder;
import com.lhx.birthday.entity.pay.WxPayV3Bean;
import com.lhx.birthday.mapper.PayOrderMapper;
import com.lhx.birthday.mapper.UserInfoMapper;
import com.lhx.birthday.request.pay.AppPayRequest;
import com.lhx.birthday.request.pay.NativePayRequest;
import com.lhx.birthday.request.pay.QueryRequest;
import com.lhx.birthday.request.pay.RefundRequest;
import com.lhx.birthday.service.ITvMemberRechargeTypeService;
import com.lhx.birthday.service.IUserInfoService;
import com.lhx.birthday.util.CommonUtil;
import com.lhx.birthday.util.FeishuBotClient;
import com.lhx.birthday.vo.Result;
import com.lhx.birthday.vo.UserInfoVO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.signers.PlainDSAEncoding;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.cert.X509Certificate;
import java.time.LocalDateTime;
import java.util.*;

import static com.lhx.birthday.constant.SettingConstant.IS_VIP;
import static com.lhx.birthday.constant.SettingConstant.NOT_VIP;

/**
 * <p>IJPay 让支付触手可及，封装了微信支付、支付宝支付、银联支付常用的支付方式以及各种常用的接口。</p>
 *
 * <p>不依赖任何第三方 mvc 框架，仅仅作为工具使用简单快速完成支付模块的开发，可轻松嵌入到任何系统里。 </p>
 *
 * <p>IJPay 交流群: 723992875、864988890</p>
 *
 * <p>Node.js 版: <a href="https://gitee.com/javen205/TNWX">https://gitee.com/javen205/TNWX</a></p>
 *
 * <p>微信支付 v3 接口示例</p>
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/wxPay")
@Slf4j
public class WxPayV3Controller {
	private final static int OK = 200;

	@Autowired
	private ITvMemberRechargeTypeService tvMemberRechargeTypeService;

	@Autowired
	private PayOrderMapper payOrderMapper;

	@Autowired
	private IUserInfoService userInfoService;

	@Resource
	private CommonUtil commonUtil;

	@Resource
	private WxPayV3Bean wxPayV3Bean;

	@Autowired
	private UserInfoMapper userInfoMapper;

	private String  webhook = "fb2bc745-1209-4e88-a106-3836702c8785";

	String serialNo;

	@ApiOperation("APP下单")
	@PostMapping("/appPay")
	public Result appPay(@RequestBody AppPayRequest appPayRequest) {
		// 获取用户id
		String customerId = commonUtil.getOperatorId();
		UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
		if(Objects.isNull(userInfoVO)){
			return Result.error("error");
		}
		// 查询对应商品信息
		TvMemberRechargeType tvMemberRechargeType = tvMemberRechargeTypeService.getByProductId(appPayRequest.getCode());
		if(Objects.isNull(tvMemberRechargeType)){
			return Result.error("系统异常");
		}
		try {
			int amount = tvMemberRechargeType.getPriceDiscountAndroid().multiply(new BigDecimal("100")).intValue();
			String timeExpire = DateTimeZoneUtil.dateToTimeZone(System.currentTimeMillis() + 1000 * 60 * 30);
			// 生成订单号
			String outTradeNo = PayKit.generateStr();

			UnifiedOrderModel unifiedOrderModel = new UnifiedOrderModel()
					.setAppid(wxPayV3Bean.getAppId())
					.setMchid(wxPayV3Bean.getMchId())
					.setDescription(tvMemberRechargeType.getName())
					.setOut_trade_no(outTradeNo)
					.setTime_expire(timeExpire)
					.setAttach(tvMemberRechargeType.getName())
					.setNotify_url(wxPayV3Bean.getDomain().concat("/wxPay/payNotify"))
//					.setAmount(new Amount().setTotal(1));
					.setAmount(new Amount().setTotal(amount));

			log.info("统一下单参数 {}", JSONUtil.toJsonStr(unifiedOrderModel));
			IJPayHttpResponse response = WxPayApi.v3(
					RequestMethodEnum.POST,
					WxDomainEnum.CHINA.toString(),
					BasePayApiEnum.APP_PAY.toString(),
					wxPayV3Bean.getMchId(),
					getSerialNumber(),
					null,
					wxPayV3Bean.getKeyPath(),
					JSONUtil.toJsonStr(unifiedOrderModel),
					AuthTypeEnum.RSA.getCode()
			);
			log.info("统一下单响应 {}", response);
			// 根据证书序列号查询对应的证书来验证签名结果
			if (response.getStatus() == OK) {
				String body = response.getBody();
				JSONObject jsonObject = JSONUtil.parseObj(body);
				String prepayId = jsonObject.getStr("prepay_id");
				Map<String, String> map = WxPayKit.appCreateSign(wxPayV3Bean.getAppId(), wxPayV3Bean.getMchId(), prepayId, wxPayV3Bean.getKeyPath());
				log.info("唤起支付参数:{}", map);
				JSON parse = JSONUtil.parse(map);
				// 保存订单信息
				payOrderMapper.saveAndFlush(PayOrder.builder()
						.userId(userInfoVO.getUserId())
						.amount(amount)
						.orderId(outTradeNo)
						.body(tvMemberRechargeType.getName())
						.timeExpire(timeExpire)
						.productId(appPayRequest.getCode())
						.createTime(LocalDateTime.now())
						.payStatus("NOTPAY")
						.payType("WECHAT")
						.build());
				parse.putByPath("outRefundNo",outTradeNo);
				return Result.ok(parse);
			}
			return Result.ok(JSONUtil.parse(response));
		} catch (Exception e) {
			e.printStackTrace();
			log.error("系统异常", e);
			return Result.error("系统异常");
		}
	}

	@ApiOperation("Native下单")
	@PostMapping("/nativePay")
	public Result nativePay(@RequestBody NativePayRequest nativePayRequest) {
		// 获取用户id
		String customerId = commonUtil.getOperatorId();
		UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
		if(Objects.isNull(userInfoVO)){
			return Result.error("error");
		}
		// 查询对应商品信息
		TvMemberRechargeType tvMemberRechargeType = tvMemberRechargeTypeService.getByProductId(nativePayRequest.getCode());
		if(Objects.isNull(tvMemberRechargeType)){
			return Result.error("系统异常");
		}
		try {
			String outTradeNo = PayKit.generateStr();
			int amount = tvMemberRechargeType.getPriceDiscountAndroid().multiply(new BigDecimal("100")).intValue();
			String timeExpire = DateTimeZoneUtil.dateToTimeZone(System.currentTimeMillis() + 1000 * 60 * 3);
			UnifiedOrderModel unifiedOrderModel = new UnifiedOrderModel()
					.setAppid(wxPayV3Bean.getAppId())
					.setMchid(wxPayV3Bean.getMchId())
					.setDescription(tvMemberRechargeType.getName())
					.setOut_trade_no(outTradeNo)
					.setTime_expire(timeExpire)
					.setAttach(tvMemberRechargeType.getName())
					.setNotify_url(wxPayV3Bean.getDomain().concat("/wxPay/payNotify"))
					.setAmount(new Amount().setTotal(amount));
//					.setAmount(new Amount().setTotal(1));

			log.info("统一下单参数 {}", JSONUtil.toJsonStr(unifiedOrderModel));
			IJPayHttpResponse response = WxPayApi.v3(
					RequestMethodEnum.POST,
					WxDomainEnum.CHINA.toString(),
					BasePayApiEnum.NATIVE_PAY.toString(),
					wxPayV3Bean.getMchId(),
					getSerialNumber(),
					null,
					wxPayV3Bean.getKeyPath(),
					JSONUtil.toJsonStr(unifiedOrderModel),
					AuthTypeEnum.RSA.getCode()
			);
			log.info("统一下单响应 {}", response);
			// 保存订单信息
			payOrderMapper.saveAndFlush(PayOrder.builder()
					.userId(userInfoVO.getUserId())
					.amount(amount)
					.orderId(outTradeNo)
					.body(tvMemberRechargeType.getName())
					.timeExpire(timeExpire)
					.productId(nativePayRequest.getCode())
					.createTime(LocalDateTime.now())
					.payStatus("NOTPAY")
					.payType("WECHAT")
					.build());
			JSON json = JSONUtil.parse(response.getBody());
			json.putByPath("outRefundNo",outTradeNo);
			return Result.ok(json);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("系统异常", e);
			return Result.error("系统异常");
		}
	}

	@ApiOperation("订单查询")
	@PostMapping("/query")
	public Result query(@RequestBody QueryRequest queryRequest) {
		try {
			Map<String, String> params = new HashMap<>(16);
			params.put("mchid", wxPayV3Bean.getMchId());

			log.info("统一下单参数 {}", JSONUtil.toJsonStr(params));
			IJPayHttpResponse response = WxPayApi.v3(
					RequestMethodEnum.GET,
					WxDomainEnum.CHINA.toString(),
					String.format(BasePayApiEnum.ORDER_QUERY_BY_OUT_TRADE_NO.toString(), queryRequest.getOutTradeNo()),
					wxPayV3Bean.getMchId(),
					getSerialNumber(),
					null,
					wxPayV3Bean.getKeyPath(),
					params,
					AuthTypeEnum.RSA.getCode()
			);
			log.info("查询响应 {}", response);
			if (response.getStatus() == OK) {
				return Result.ok(JSONUtil.parse(response.getBody()));
			}
			return Result.ok(JSONUtil.parse(response));
		} catch (Exception e) {
			e.printStackTrace();
			log.error("系统异常", e);
			return Result.error("系统异常");
		}
	}

	@ApiOperation("退款")
	@PostMapping("/refund")
	public Result refund(@RequestBody RefundRequest refundRequest) {
		try {
			String outRefundNo = refundRequest.getOutTradeNo();
			PayOrder payOrder = payOrderMapper.findByOrderId(outRefundNo);
			if(Objects.isNull(payOrder)){
				return Result.error("系统异常");
			}
			log.info("商户退款单号: {}", outRefundNo);

			int amount = (int) payOrder.getAmount();

			List<RefundGoodsDetail> list = new ArrayList<>();
			RefundGoodsDetail refundGoodsDetail = new RefundGoodsDetail()
					.setMerchant_goods_id("123")
					.setGoods_name("测试")
					.setUnit_price(amount)
					.setRefund_amount(amount)
					.setRefund_quantity(amount);
			list.add(refundGoodsDetail);

			RefundModel refundModel = new RefundModel()
					.setOut_refund_no(outRefundNo)
					.setReason("正常退款")
					.setNotify_url(wxPayV3Bean.getDomain().concat("/wxPay/refundNotify"))
					.setAmount(new RefundAmount().setRefund(amount).setTotal(amount).setCurrency("CNY"))
					.setGoods_detail(list);

			if (StrUtil.isNotEmpty(refundRequest.getTransactionId())) {
				refundModel.setTransaction_id(refundRequest.getTransactionId());
			}
			if (StrUtil.isNotEmpty(refundRequest.getOutTradeNo())) {
				refundModel.setOut_trade_no(refundRequest.getOutTradeNo());
			}
			log.info("退款参数 {}", JSONUtil.toJsonStr(refundModel));
			IJPayHttpResponse response = WxPayApi.v3(
					RequestMethodEnum.POST,
					WxDomainEnum.CHINA.toString(),
					BasePayApiEnum.REFUND.toString(),
					wxPayV3Bean.getMchId(),
					getSerialNumber(),
					null,
					wxPayV3Bean.getKeyPath(),
					JSONUtil.toJsonStr(refundModel)
			);
			// 根据证书序列号查询对应的证书来验证签名结果
			log.info("退款响应 {}", response);

			return Result.ok(JSONUtil.parse(response.getBody()));
		} catch (Exception e) {
			log.error("系统异常", e);
			return Result.error(e.getMessage());
		}
	}

	@ApiOperation("支付通知")
	@RequestMapping(value = "/payNotify", method = {org.springframework.web.bind.annotation.RequestMethod.POST, org.springframework.web.bind.annotation.RequestMethod.GET})
	public void payNotify(HttpServletRequest request, HttpServletResponse response) {
		Map<String, String> map = new HashMap<>(12);
		try {
			String timestamp = request.getHeader("Wechatpay-Timestamp");
			String nonce = request.getHeader("Wechatpay-Nonce");
			String serialNo = request.getHeader("Wechatpay-Serial");
			String signature = request.getHeader("Wechatpay-Signature");

			log.info("timestamp:{} nonce:{} serialNo:{} signature:{}", timestamp, nonce, serialNo, signature);
			String result = HttpKit.readData(request);
			log.info("支付通知密文 {}", result);

			// 需要通过证书序列号查找对应的证书，verifyNotify 中有验证证书的序列号
			String plainText = WxPayKit.verifyNotify(serialNo, result, signature, nonce, timestamp,
					wxPayV3Bean.getApiKey3(), wxPayV3Bean.getPlatformCertPath());

			log.info("支付通知明文 {}", plainText);

			if (StrUtil.isNotEmpty(plainText)) {
				JSON json = JSONUtil.parse(plainText);
				String outTradeNo = (String) json.getByPath("out_trade_no");
				PayOrder payOrder = payOrderMapper.findByOrderId(outTradeNo);
				if(Objects.nonNull(payOrder)){
					payOrder.setTransactionId(json.getByPath("transaction_id", String.class));
					payOrder.setPayStatus(json.getByPath("trade_state", String.class));
					payOrder.setPayNotifyTime(LocalDateTime.now());
					payOrder.setPayNotifyData(json.toString());
					payOrderMapper.saveAndFlush(payOrder);
					// 根据购买商品下发会员信息
					String productId = payOrder.getProductId();
					TvMemberRechargeType tvMemberRechargeType = tvMemberRechargeTypeService.getByProductId(productId);
					if(Objects.nonNull(tvMemberRechargeType)){
						Integer expire = tvMemberRechargeType.getExpire();
						UserInfo userInfo = userInfoMapper.findById(payOrder.getUserId()).get();
						userInfo.setVipProductId(payOrder.getProductId());
						if(userInfo.getVip().equals(IS_VIP)){
							userInfo.setVipExpiryDate(userInfo.getVipExpiryDate().plusDays(expire));
						}else{
							userInfo.setVipExpiryDate(LocalDateTime.now().plusDays(expire));
						}
						userInfoService.updateVipExpireDate(userInfo.getUserId(), userInfo.getVipExpiryDate() ,userInfo.getVipProductId(),IS_VIP);
						try {
							FeishuBotClient.sendMessage(webhook, "星愿助手用户:"+ payOrder.getUserId()+"新购会员:" + payOrder.getProductId()+"【安卓】");
						} catch (IOException e) {
							e.printStackTrace();
						}
					}
				}

				response.setStatus(200);
				map.put("code", "SUCCESS");
				map.put("message", "SUCCESS");
			} else {
				response.setStatus(500);
				map.put("code", "ERROR");
				map.put("message", "签名错误");
			}
			response.setHeader("Content-type", ContentType.JSON.toString());
			response.getOutputStream().write(JSONUtil.toJsonStr(map).getBytes(StandardCharsets.UTF_8));
			response.flushBuffer();
		} catch (Exception e) {
			log.error("系统异常", e);
		}
	}

	@ApiOperation("退款通知")
	@RequestMapping(value = "/refundNotify", method = {RequestMethod.POST, RequestMethod.GET})
	public void refundNotify(HttpServletRequest request, HttpServletResponse response) {
		Map<String, String> map = new HashMap<>(12);
		try {
			String timestamp = request.getHeader("Wechatpay-Timestamp");
			String nonce = request.getHeader("Wechatpay-Nonce");
			String serialNo = request.getHeader("Wechatpay-Serial");
			String signature = request.getHeader("Wechatpay-Signature");

			log.info("timestamp:{} nonce:{} serialNo:{} signature:{}", timestamp, nonce, serialNo, signature);
			String result = HttpKit.readData(request);
			log.info("退款通知密文 {}", result);

			// 需要通过证书序列号查找对应的证书，verifyNotify 中有验证证书的序列号
			String plainText = WxPayKit.verifyNotify(serialNo, result, signature, nonce, timestamp,
					wxPayV3Bean.getApiKey3(), wxPayV3Bean.getPlatformCertPath());

			log.info("退款通知明文 {}", plainText);

			if (StrUtil.isNotEmpty(plainText)) {
				JSON json = JSONUtil.parse(plainText);
				String outTradeNo = (String) json.getByPath("out_trade_no");
				PayOrder payOrder = payOrderMapper.findByOrderId(outTradeNo);
				if(Objects.nonNull(payOrder)){
					payOrder.setRefundId(json.getByPath("refund_id", String.class));
					payOrder.setRefundStatus(json.getByPath("refund_status", String.class));
					payOrder.setRefundNotifyTime(LocalDateTime.now());
					payOrder.setRefundNotifyData(json.toString());
					payOrderMapper.saveAndFlush(payOrder);
					// 根据购买会员移除会员信息
					UserInfo userInfo = userInfoMapper.findById(payOrder.getUserId()).get();
					userInfo.setVip(NOT_VIP);
					userInfoMapper.save(userInfo);
				}
				response.setStatus(200);
				map.put("code", "SUCCESS");
				map.put("message", "SUCCESS");
			} else {
				response.setStatus(500);
				map.put("code", "ERROR");
				map.put("message", "签名错误");
			}
			response.setHeader("Content-type", ContentType.JSON.toString());
			response.getOutputStream().write(JSONUtil.toJsonStr(map).getBytes(StandardCharsets.UTF_8));
			response.flushBuffer();
		} catch (Exception e) {
			log.error("系统异常", e);
		}
	}

	private String getSerialNumber() {
		if (StrUtil.isEmpty(serialNo)) {
			// 获取证书序列号
			X509Certificate certificate = PayKit.getCertificate(wxPayV3Bean.getCertPath());
			if (null != certificate) {
				serialNo = certificate.getSerialNumber().toString(16).toUpperCase();
				// 提前两天检查证书是否有效
				boolean isValid = PayKit.checkCertificateIsValid(certificate, wxPayV3Bean.getMchId(), -2);
				log.info("证书是否可用 {} 证书有效期为 {}", isValid, DateUtil.format(certificate.getNotAfter(), DatePattern.NORM_DATETIME_PATTERN));
			}
		}
		System.out.println("serialNo:" + serialNo);
		return serialNo;
	}

}
