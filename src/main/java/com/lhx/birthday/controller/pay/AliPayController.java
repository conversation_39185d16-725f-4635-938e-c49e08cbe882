package com.lhx.birthday.controller.pay;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.domain.*;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.response.AlipayFundAuthOrderFreezeResponse;
import com.alipay.api.response.AlipayFundCouponOrderAgreementPayResponse;
import com.alipay.api.response.AlipayTradeCreateResponse;
import com.ijpay.alipay.AliPayApi;
import com.ijpay.alipay.AliPayApiConfig;
import com.ijpay.alipay.AliPayApiConfigKit;
import com.ijpay.core.utils.DateTimeZoneUtil;
import com.lhx.birthday.entity.TvMemberRechargeType;
import com.lhx.birthday.entity.UserInfo;
import com.lhx.birthday.entity.pay.AliPayBean;
import com.lhx.birthday.entity.pay.PayOrder;
import com.lhx.birthday.mapper.PayOrderMapper;
import com.lhx.birthday.mapper.UserInfoMapper;
import com.lhx.birthday.request.pay.AppPayRequest;
import com.lhx.birthday.request.pay.RefundRequest;
import com.lhx.birthday.service.ITvMemberRechargeTypeService;
import com.lhx.birthday.service.IUserInfoService;
import com.lhx.birthday.util.CommonUtil;
import com.lhx.birthday.util.FeishuBotClient;
import com.lhx.birthday.util.StringUtils;
import com.lhx.birthday.vo.Result;
import com.lhx.birthday.vo.UserInfoVO;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.lhx.birthday.constant.SettingConstant.IS_VIP;

/**
 * <p>IJPay 让支付触手可及，封装了微信支付、支付宝支付、银联支付常用的支付方式以及各种常用的接口。</p>
 *
 * <p>不依赖任何第三方 mvc 框架，仅仅作为工具使用简单快速完成支付模块的开发，可轻松嵌入到任何系统里。 </p>
 *
 * <p>IJPay 交流群: 723992875、864988890</p>
 *
 * <p>Node.js 版: <a href="https://gitee.com/javen205/TNWX">https://gitee.com/javen205/TNWX</a></p>
 *
 * <p>支付宝支付 Demo</p>
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/aliPay")
public class AliPayController {
    private static final Logger log = LoggerFactory.getLogger(AliPayController.class);

    @Resource
    private AliPayBean aliPayBean;

    @Autowired
    private IUserInfoService userInfoService;

    @Resource
    private CommonUtil commonUtil;

    @Autowired
    private ITvMemberRechargeTypeService tvMemberRechargeTypeService;

    @Autowired
    private PayOrderMapper payOrderMapper;

    @Autowired
    private UserInfoMapper userInfoMapper;

    /**
     * 普通公钥模式
     */
    private final static String NOTIFY_URL = "/aliPay/notify_url";

    private final static String RETURN_URL = "/aliPay/return_url";

    // 飞书机器人webhook
    private String webhook = "fb2bc745-1209-4e88-a106-3836702c8785";

     /**
      * 证书模式
      */
//    private final static String NOTIFY_URL = "/aliPay/cert_notify_url";
//    private final static String RETURN_URL = "/aliPay/return_url";

    /**
     * 证书模式
     */
//    private final static String RETURN_URL = "/aliPay/cert_return_url";

    /**
     * app支付
     */
    @ApiOperation("APP下单")
    @PostMapping("/appPay")
    public Result appPay(@RequestBody AppPayRequest appPayRequest) {
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        // 查询对应商品信息
        TvMemberRechargeType tvMemberRechargeType = tvMemberRechargeTypeService.getByProductId(appPayRequest.getCode());
        if(Objects.isNull(tvMemberRechargeType)){
            return Result.error("系统异常");
        }
        try {
            // 生成订单号
            String outTradeNo = StringUtils.getOutTradeNo();
            String totalAmount = tvMemberRechargeType.getPriceDiscountAndroid().toString();

            AlipayTradeAppPayModel model = new AlipayTradeAppPayModel();
            model.setBody(tvMemberRechargeType.getName());
            model.setSubject(tvMemberRechargeType.getName());
            model.setOutTradeNo(outTradeNo);
            model.setTimeoutExpress("30m");
            model.setTotalAmount(totalAmount);
//            model.setPassbackParams("callback params");
            model.setProductCode("QUICK_MSECURITY_PAY");
            String orderInfo = AliPayApi.appPayToResponse(model, aliPayBean.getDomain() + NOTIFY_URL).getBody();
            // 保存订单信息
            int amount = tvMemberRechargeType.getPriceDiscountAndroid().multiply(new BigDecimal("100")).intValue();
            String timeExpire = DateTimeZoneUtil.dateToTimeZone(System.currentTimeMillis() + 1000 * 60 * 30);
            payOrderMapper.saveAndFlush(PayOrder.builder()
                    .userId(userInfoVO.getUserId())
                    .amount(amount)
                    .orderId(outTradeNo)
                    .body(tvMemberRechargeType.getName())
                    .timeExpire(timeExpire)
                    .productId(appPayRequest.getCode())
                    .createTime(LocalDateTime.now())
                    .payStatus("NOTPAY")
                    .payType("ALIPAY")
                    .build());
            return Result.ok(orderInfo);
        } catch (AlipayApiException e) {
            e.printStackTrace();
            return Result.error("system error:" + e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("system error:" + e.getMessage());
        }
    }

    /**
     * 扫码支付
     */
    @ApiOperation("扫码支付")
    @PostMapping(value = "/tradePreCreatePay")
    public Result tradePreCreatePay(@RequestBody AppPayRequest appPayRequest) {
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        // 查询对应商品信息
        TvMemberRechargeType tvMemberRechargeType = tvMemberRechargeTypeService.getByProductId(appPayRequest.getCode());
        if(Objects.isNull(tvMemberRechargeType)){
            return Result.error("系统异常");
        }
        // 生成订单号
        String outTradeNo = StringUtils.getOutTradeNo();

        String subject = tvMemberRechargeType.getName();
        String totalAmount = tvMemberRechargeType.getPriceAndroid().toString();
        String storeId = "123";
        String notifyUrl = aliPayBean.getDomain() + NOTIFY_URL;
//        String notifyUrl = aliPayBean.getDomain() + "/aliPay/cert_notify_url";

        AlipayTradePrecreateModel model = new AlipayTradePrecreateModel();
        model.setSubject(subject);
        model.setTotalAmount(totalAmount);
        model.setStoreId(storeId);
        model.setTimeoutExpress("30m");
        model.setOutTradeNo(outTradeNo);
        try {
            String resultStr = AliPayApi.tradePrecreatePayToResponse(model, notifyUrl).getBody();
            JSONObject jsonObject = JSONObject.parseObject(resultStr);
            // 保存订单信息
            int amount = tvMemberRechargeType.getPriceAndroid().multiply(new BigDecimal("100")).intValue();
            String timeExpire = DateTimeZoneUtil.dateToTimeZone(System.currentTimeMillis() + 1000 * 60 * 30);
            payOrderMapper.saveAndFlush(PayOrder.builder()
                    .userId(userInfoVO.getUserId())
                    .amount(amount)
                    .orderId(outTradeNo)
                    .body(tvMemberRechargeType.getName())
                    .timeExpire(timeExpire)
                    .productId(appPayRequest.getCode())
                    .createTime(LocalDateTime.now())
                    .payStatus("NOTPAY")
                    .payType("ALIPAY")
                    .build());
            return Result.ok(jsonObject.getJSONObject("alipay_trade_precreate_response").getString("qr_code"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 退款
     */
    @ApiOperation("退款")
    @PostMapping(value = "/tradeRefund")
    public Result tradeRefund(@RequestBody RefundRequest refundRequest) {
        try {
            PayOrder payOrder = payOrderMapper.findByOrderId(refundRequest.getOutTradeNo());
            if(Objects.isNull(payOrder)){
                return Result.error("系统异常");
            }
            // 查询对应商品信息
            TvMemberRechargeType tvMemberRechargeType = tvMemberRechargeTypeService.getByProductId(payOrder.getProductId());
            if(Objects.isNull(tvMemberRechargeType)){
                return Result.error("系统异常");
            }
            AlipayTradeRefundModel model = new AlipayTradeRefundModel();
            if (StringUtils.isNotEmpty(refundRequest.getOutTradeNo())) {
                model.setOutTradeNo(refundRequest.getOutTradeNo());
            }
//            if (StringUtils.isNotEmpty(tradeNo)) {
//                model.setTradeNo(tradeNo);
//            }
            model.setRefundAmount(tvMemberRechargeType.getPriceAndroid().toString());
            model.setRefundReason("正常退款");
            return Result.ok(AliPayApi.tradeRefundToResponse(model).getBody());
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "/return_url")
    @ResponseBody
    public String returnUrl(HttpServletRequest request) {
        try {
            // 获取支付宝GET过来反馈信息
            Map<String, String> map = AliPayApi.toMap(request);
            for (Map.Entry<String, String> entry : map.entrySet()) {
                log.info(entry.getKey() + " = " + entry.getValue());
            }

            boolean verifyResult = AlipaySignature.rsaCheckV1(map, aliPayBean.getPublicKey(), "UTF-8",
                    "RSA2");

            if (verifyResult) {
                // TODO 请在这里加上商户的业务逻辑程序代码
                log.info("return_url 验证成功");

                return "success";
            } else {
                log.info("return_url 验证失败");
                // TODO
                return "failure";
            }
        } catch (AlipayApiException e) {
            e.printStackTrace();
            return "failure";
        }
    }

    @RequestMapping(value = "/cert_return_url")
    @ResponseBody
    public String certReturnUrl(HttpServletRequest request) {
        try {
            // 获取支付宝GET过来反馈信息
            Map<String, String> map = AliPayApi.toMap(request);
            for (Map.Entry<String, String> entry : map.entrySet()) {
                log.info(entry.getKey() + " = " + entry.getValue());
            }

            boolean verifyResult = AlipaySignature.rsaCertCheckV1(map, aliPayBean.getAliPayCertPath(), "UTF-8",
                    "RSA2");

            if (verifyResult) {
                // TODO 请在这里加上商户的业务逻辑程序代码
                log.info("certReturnUrl 验证成功");

                return "success";
            } else {
                log.info("certReturnUrl 验证失败");
                // TODO
                return "failure";
            }
        } catch (AlipayApiException e) {
            e.printStackTrace();
            return "failure";
        }
    }


    @RequestMapping(value = "/notify_url")
    @ResponseBody
    public String notifyUrl(HttpServletRequest request) {
        try {
            // 获取支付宝POST过来反馈信息
            Map<String, String> params = AliPayApi.toMap(request);

            for (Map.Entry<String, String> entry : params.entrySet()) {
                log.info(entry.getKey() + " = " + entry.getValue());
            }

            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(params));
            String outTradeNo = jsonObject.getString("out_trade_no");
            PayOrder payOrder = payOrderMapper.findByOrderId(outTradeNo);
            if(Objects.nonNull(payOrder) && !payOrder.getPayStatus().equals("TRADE_SUCCESS")){
                payOrder.setTransactionId(jsonObject.getString("trade_no"));
                payOrder.setPayStatus(jsonObject.getString("trade_status"));
                payOrder.setPayNotifyTime(LocalDateTime.now());
                payOrder.setPayNotifyData(jsonObject.toString());
                payOrderMapper.saveAndFlush(payOrder);
                // 根据购买商品下发会员信息
                String productId = payOrder.getProductId();
                TvMemberRechargeType tvMemberRechargeType = tvMemberRechargeTypeService.getByProductId(productId);
                if(Objects.nonNull(tvMemberRechargeType)){
                    Integer expire = tvMemberRechargeType.getExpire();
                    UserInfo userInfo = userInfoMapper.findById(payOrder.getUserId()).get();
                    userInfo.setVipProductId(payOrder.getProductId());
                    if(userInfo.getVip().equals(IS_VIP)){
                        userInfo.setVipExpiryDate(userInfo.getVipExpiryDate().plusDays(expire));
                    }else{
                        userInfo.setVipExpiryDate(LocalDateTime.now().plusDays(expire));
                    }
                    userInfoService.updateVipExpireDate(userInfo.getUserId(), userInfo.getVipExpiryDate() ,userInfo.getVipProductId(),IS_VIP);
                    // 发送飞书通知
                    try {
                        FeishuBotClient.sendMessage(webhook, "星愿助手用户:"+ payOrder.getUserId()+"新购会员:" + payOrder.getProductId()+"【安卓】");
                    } catch (Exception e) {
                        log.error("飞书通知发送失败：", e);
                    }
                }
            }

            log.info("notify_url 处理成功");
            return "success";
        } catch (Exception e) {
            e.printStackTrace();
            return "failure";
        }
    }

}
