package com.lhx.birthday.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSONObject;
import com.lhx.birthday.constant.SystemConfigConstant;
import com.lhx.birthday.enums.RewardType;
import com.lhx.birthday.enums.RewardVipUnit;
import com.lhx.birthday.response.market.IntegralProductResponse;
import com.lhx.birthday.response.market.MarketSettingResponse;
import com.lhx.birthday.response.market.ProductMarketingResponse;
import com.lhx.birthday.service.IMarketSettingService;
import com.lhx.birthday.service.ISystemConfigService;
import com.lhx.birthday.service.impl.MarketSettingServiceImpl;
import com.lhx.birthday.service.impl.SystemConfigServiceImpl;
import com.lhx.birthday.util.IfconfigUtil;
import com.lhx.birthday.util.NumUtil;
import com.lhx.birthday.vo.Result;
import com.lhx.birthday.vo.market.MarketSettingVO;
import com.lhx.birthday.vo.market.PointVipVO;
import com.lhx.birthday.vo.systemConfig.RewardSettingVO;
import com.lhx.birthday.vo.systemConfig.SystemConfigVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

import static com.lhx.birthday.constant.SystemConfigConstant.MOBILE_CONFIG_KEY;
import static com.lhx.birthday.service.impl.MarketSettingServiceImpl.checkPctByIp;

@RequestMapping("/market")
@RestController
@Slf4j
@Api(description = "app接口 - 营销配置设置", tags = "MarketSettingController")
public class MarketSettingController {

    @Autowired
    private IMarketSettingService marketSettingService;

    @Autowired
    private SystemConfigServiceImpl systemConfigService;

    /**
     * 1.获取苹果审核状态
     * 2.5星好评福利
     */
    @ApiOperation("获取营销配置")
    @GetMapping("/setting")
    public Result<MarketSettingResponse> getSetting(@ApiParam(value = "客户端 0: ios 1:android", required = false)
                                                    @RequestParam(value = "device",required = false) Integer device,
                                                    HttpServletRequest request) {
        String configKey = SystemConfigConstant.MARKET_CONFIG_TYPE_IOS;
        if(Objects.nonNull(device) && device.equals(1)){
            configKey = SystemConfigConstant.MARKET_CONFIG_TYPE_ANDROID;
        }
        return Result.ok(MarketSettingResponse.builder()
                .marketSetting(marketSettingService.getMarketing(configKey,request))
                .build());
    }

    /**
     * 获取好评配置
     * @param device
     * @param request
     * @return
     */
    @ApiOperation("获取好评配置")
    @GetMapping("/starSetting")
    public Result getStartSetting(@ApiParam(value = "客户端 0: ios 1:android", required = false)
                                  @RequestParam(value = "device",required = false) Integer device,
                                  HttpServletRequest request) {
        String configKey = SystemConfigConstant.MARKET_CONFIG_STAR_IOS;
        if(Objects.nonNull(device) && device.equals(1)){
            configKey = SystemConfigConstant.MARKET_CONFIG_STAR_ANDROID;
        }
        SystemConfigVO systemConfig = systemConfigService.findByConfigKeyAndConfigType(MOBILE_CONFIG_KEY, configKey);
        JSONObject jsonObject = JSONObject.parseObject(systemConfig.getContext());
        if(jsonObject.getBoolean("show")){
            IfconfigUtil ifconfigUtil = new IfconfigUtil();
            String ipAddr = ifconfigUtil.getIpAddr(request);
            Double pct = jsonObject.getDouble("pct");
            boolean checkPctByIp = checkPctByIp(pct, ipAddr);
            if(!checkPctByIp){
                jsonObject.put("show",false);
            }
        }

        return Result.OK(jsonObject);
    }

    /**
     * 获取营销页订阅商品
     * @return
     */
    @ApiOperation("获取营销页订阅商品")
    @GetMapping("/product")
    public Result<ProductMarketingResponse> getProductMarketing(@ApiParam(value = "客户端 0: ios 1:android", required = false)
                                                                    @RequestParam(value = "device",required = false) Integer device) {
        String configKey = SystemConfigConstant.MARKET_CONFIG_TYPE_IOS;
        if(Objects.nonNull(device) && device.equals(1)){
            configKey = SystemConfigConstant.MARKET_CONFIG_TYPE_ANDROID;
        }
        return Result.ok(ProductMarketingResponse.builder()
                .productMarketing(marketSettingService.getProductMarketing(configKey))
                .build());
    }

    /**
     * 获取积分商品列表
     * @return
     */
    @ApiOperation("获取积分商品列表")
    @GetMapping("/integral-product-list")
    public Result<IntegralProductResponse> integralProductList(@ApiParam(value = "客户端 0: ios 1:android", required = false)
                                                                   @RequestParam(value = "device",required = false) Integer device) {
        if(Objects.isNull(device)){
            device = 0;
        }
        List<PointVipVO> pointVipList = marketSettingService.getPointVipList(device);
        return Result.ok(IntegralProductResponse.builder()
                .integralProducts(pointVipList)
                .build());
    }

}
