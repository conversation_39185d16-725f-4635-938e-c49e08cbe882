package com.lhx.birthday.controller;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dypnsapi20170525.Client;
import com.aliyun.dypnsapi20170525.models.GetMobileRequest;
import com.aliyun.dypnsapi20170525.models.GetMobileResponse;
import com.aliyun.dypnsapi20170525.models.GetMobileResponseBody;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.lhx.birthday.enums.WuyuanAdActionType;
import com.lhx.birthday.request.WuyuanAdRequest;
import com.lhx.birthday.request.userinfo.*;
import com.lhx.birthday.response.LoginResponse;
import com.lhx.birthday.service.IAliyunSmsService;
import com.lhx.birthday.service.IUserInfoService;
import com.lhx.birthday.service.IWuyuanAdService;
import com.lhx.birthday.service.CountryCodeService;
import com.lhx.birthday.service.impl.ICountryCodeServiceImpl;
import com.lhx.birthday.util.*;
import com.lhx.birthday.vo.Result;
import com.lhx.birthday.vo.UserInfoVO;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.impl.compression.CompressionCodecs;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR> lhx
 * @date 2023/10/30 16:13
 */
@RequestMapping("/user")
@RestController
@Slf4j
@Api(description = "app接口 - 用户登录", tags = "UserInfoController")
public class UserInfoController {

    @Autowired
    private IUserInfoService userInfoService;

    @Value("${jwt.secret-key}")
    private String jwtSecretKey;

    @Value("${ali.phone.access-key-secret}")
    private String accessKeySecret;

    @Value("${ali.phone.access-key-id}")
    private String accessKeyId;

    @Resource
    private IAliyunSmsService aliyunSmsService;

    @Resource
    private CommonUtil commonUtil;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private ICountryCodeServiceImpl countryCodeService;

    @Autowired
    private IWuyuanAdService wuyuanAdService;

//    @Autowired
//    private RedissonClient redissonClient;

    /**
     * 5分钟缓存
     */
    private TimedCache<String, String> verificationCodeCache = CacheUtil.newTimedCache(300000);

    /**
     * 游客登录
     * @param touristsLoginRequest
     * @return
     */
    @ApiOperation("游客登录")
    @PostMapping("/tourists/login")
    public Result<LoginResponse> touristsLogin(@RequestBody TouristsLoginRequest touristsLoginRequest,HttpServletRequest request) {
        String osType = request.getHeader("osType");
        touristsLoginRequest.setOsType(osType);
//        RLock rLock = redissonClient.getFairLock("userinfo:"+touristsLoginRequest.getUuid());
//        rLock.lock();
//        try {
            UserInfoVO userInfoVO = userInfoService.touristsLogin(touristsLoginRequest);
            Date date = new Date();
            String token = Jwts.builder().setSubject(touristsLoginRequest.getUuid())
                    .compressWith(CompressionCodecs.DEFLATE)
                    .signWith(SignatureAlgorithm.HS256, jwtSecretKey)
                    .setIssuedAt(date)
                    .claim("uuid", touristsLoginRequest.getUuid())
                    .claim("userId", userInfoVO.getUserId())
                    .claim("terminalToken", MD5Util.md5Hex(userInfoVO.getUserId()+date.getTime()+ RandomStringUtils.randomNumeric(4)))
                    .setExpiration(DateUtils.addYears(date, 50))
                    .compact();
            log.info("uuid[" + touristsLoginRequest.getUuid() + "]登录，token：" + token);
            LoginResponse loginResponse = LoginResponse.builder()
                    .token(token)
                    .uuid(touristsLoginRequest.getUuid())
                    .userInfo(userInfoVO)
                    .build();
            // 异步请求AD推广接口
            String appleAdToken = request.getHeader("appleAdToken");
            String osVersion = request.getHeader("osVersion");
            String ipx = request.getHeader("ipx");
            String idfa = request.getHeader("idfa");
            String imei = request.getHeader("imei");
            String oaid = request.getHeader("oaid");
            IfconfigUtil ifconfigUtil = new IfconfigUtil();
            String ipAddr = ifconfigUtil.getIpAddr(request);
            if(!StringUtils.isEmpty(ipx)){
                taskExecutor.execute(() -> {
                    wuyuanAdService.trigger(WuyuanAdRequest.builder()
                            .ipAddr(ipAddr)
                            .ipAddrV6(ipx)
                            .appleAdToken(appleAdToken)
                            .osVersion(osVersion)
                            .osType(osType)
                            .idfa(idfa)
                            .imei(imei)
                            .oaid(oaid)
                            .userId(String.valueOf(userInfoVO.getUserId()))
                            .wuyuanAdActionType(WuyuanAdActionType.ACTIVATE)
                            .build());
                });
            }
            return Result.OK(loginResponse);
    }

    /**
     * 阿里云 一键登录
     * @param authLoginRequest
     * @return
     */
    @ApiOperation("阿里云一键登录")
    @PostMapping("/phone/auth")
    public Result phoneAuth(@RequestBody AuthLoginRequest authLoginRequest, HttpServletRequest request) {
        Config config = new Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret);
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        UserInfoVO beforeUserInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(beforeUserInfoVO)){
            return Result.error("error");
        }
        try {
            config.endpoint = "dypnsapi.aliyuncs.com";
            Client client = new Client(config);
            GetMobileRequest getMobileRequest = new GetMobileRequest()
                    .setAccessToken(authLoginRequest.getAccessToken());
            RuntimeOptions runtime = new RuntimeOptions();
            GetMobileResponse rep = client. getMobileWithOptions(getMobileRequest, runtime);
            if (rep.getBody().getCode().equals("OK")) {
                GetMobileResponseBody.GetMobileResponseBodyGetMobileResultDTO getMobileResultDTO = rep.getBody().getMobileResultDTO;
                String phone = getMobileResultDTO.getMobile();
                UserLoginRequest userLoginRequest = new UserLoginRequest();
                userLoginRequest.setPhone(phone);
                UserInfoVO userInfoVO = userInfoService.login(userLoginRequest,beforeUserInfoVO);
                Date date = new Date();
                String token = Jwts.builder().setSubject(phone)
                        .compressWith(CompressionCodecs.DEFLATE)
                        .signWith(SignatureAlgorithm.HS256, jwtSecretKey)
                        .setIssuedAt(date)
                        .claim("userId", userInfoVO.getUserId())
                        .claim("phone", phone)
                        .claim("terminalToken", MD5Util.md5Hex(userInfoVO.getUserId()+date.getTime()+ RandomStringUtils.randomNumeric(4)))
                        .setExpiration(DateUtils.addYears(date, 50))
                        .compact();
                log.info("手机号" + phone + "登录，token：" + token);
                LoginResponse loginResponse = LoginResponse.builder()
                        .token(token)
                        .phone(phone)
                        .userInfo(userInfoVO)
                        .build();
                // 异步请求AD推广接口
                String appleAdToken = request.getHeader("appleAdToken");
                String osType = request.getHeader("osType");
                String osVersion = request.getHeader("osVersion");
                String ipx = request.getHeader("ipx");
                String idfa = request.getHeader("idfa");
                String imei = request.getHeader("imei");
                String oaid = request.getHeader("oaid");
                IfconfigUtil ifconfigUtil = new IfconfigUtil();
                String ipAddr = ifconfigUtil.getIpAddr(request);
                if(!StringUtils.isEmpty(ipx)){
                    taskExecutor.execute(() -> {
                        wuyuanAdService.trigger(WuyuanAdRequest.builder()
                                .ipAddr(ipAddr)
                                .ipAddrV6(ipx)
                                .appleAdToken(appleAdToken)
                                .osVersion(osVersion)
                                .osType(osType)
                                .idfa(idfa)
                                .imei(imei)
                                .oaid(oaid)
                                .userId(String.valueOf(userInfoVO.getUserId()))
                                .wuyuanAdActionType(WuyuanAdActionType.REGISTER)
                                .build());
                    });
                }
                return Result.OK(loginResponse);
            } else {
                return Result.error("系统异常");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("系统异常");
        }
    }

    @ApiOperation("手机号登录")
    @PostMapping("/login")
    public Result login(@RequestBody UserLoginRequest userLoginRequest, HttpServletRequest request) {
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        UserInfoVO beforeUserInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(beforeUserInfoVO)){
            return Result.error("error");
        }
        if (!codeCheck(userLoginRequest.getCode(), userLoginRequest.getPhone())) {
            return Result.error("验证码错误");
        }
        UserInfoVO userInfoVO = userInfoService.login(userLoginRequest,beforeUserInfoVO);
        Date date = new Date();
        String token = Jwts.builder().setSubject(userLoginRequest.getPhone())
                .compressWith(CompressionCodecs.DEFLATE)
                .signWith(SignatureAlgorithm.HS256, jwtSecretKey)
                .setIssuedAt(date)
                .claim("userId", userInfoVO.getUserId())
                .claim("phone", userLoginRequest.getPhone())
                .claim("terminalToken", MD5Util.md5Hex(userInfoVO.getUserId()+date.getTime()+ RandomStringUtils.randomNumeric(4)))
                .setExpiration(DateUtils.addYears(date, 50))
                .compact();
        log.info("手机号" + userLoginRequest.getPhone() + "登录，token：" + token);
        LoginResponse loginResponse = LoginResponse.builder()
                .token(token)
                .phone(userLoginRequest.getPhone())
                .userInfo(userInfoVO)
                .build();
        // 异步请求AD推广接口
        String appleAdToken = request.getHeader("appleAdToken");
        String osType = request.getHeader("osType");
        String osVersion = request.getHeader("osVersion");
        String ipx = request.getHeader("ipx");
        String idfa = request.getHeader("idfa");
        String imei = request.getHeader("imei");
        String oaid = request.getHeader("oaid");
        IfconfigUtil ifconfigUtil = new IfconfigUtil();
        String ipAddr = ifconfigUtil.getIpAddr(request);
        if(!StringUtils.isEmpty(ipx)){
            taskExecutor.execute(() -> {
                wuyuanAdService.trigger(WuyuanAdRequest.builder()
                        .ipAddr(ipAddr)
                        .ipAddrV6(ipx)
                        .appleAdToken(appleAdToken)
                        .osVersion(osVersion)
                        .osType(osType)
                        .idfa(idfa)
                        .imei(imei)
                        .oaid(oaid)
                        .userId(String.valueOf(userInfoVO.getUserId()))
                        .wuyuanAdActionType(WuyuanAdActionType.REGISTER)
                        .build());
            });
        }
        return Result.OK(loginResponse);
    }

    /**
     * 苹果登录
     * @param userLoginWithAppleRequest
     * @param request
     * @return
     */
    @ApiOperation("苹果登录")
    @PostMapping("/login_apple")
    public Result loginApple(@RequestBody UserLoginWithAppleRequest userLoginWithAppleRequest, HttpServletRequest request) {
        String customerId = commonUtil.getOperatorId();
        UserInfoVO beforeUserInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(beforeUserInfoVO)){
            return Result.error("error");
        }
        UserInfoVO userInfoVO = userInfoService.loginWithApple(userLoginWithAppleRequest,beforeUserInfoVO);
        Date date = new Date();
        String token = Jwts.builder().setSubject(userInfoVO.getAppleId())
                .compressWith(CompressionCodecs.DEFLATE)
                .signWith(SignatureAlgorithm.HS256, jwtSecretKey)
                .setIssuedAt(date)
                .claim("userId", userInfoVO.getUserId())
                .claim("appleId", userInfoVO.getAppleId())
                .claim("terminalToken", MD5Util.md5Hex(userInfoVO.getUserId()+date.getTime()+ RandomStringUtils.randomNumeric(4)))
                .setExpiration(DateUtils.addYears(date, 50))
                .compact();
        log.info("苹果Id:" + userInfoVO.getAppleId() + "登录，token：" + token);
        LoginResponse loginResponse = LoginResponse.builder()
                .token(token)
                .appleId(userInfoVO.getAppleId())
                .build();
        // 异步请求AD推广接口
        String appleAdToken = request.getHeader("appleAdToken");
        String osType = request.getHeader("osType");
        String osVersion = request.getHeader("osVersion");
        String ipx = request.getHeader("ipx");
        String idfa = request.getHeader("idfa");
        String imei = request.getHeader("imei");
        String oaid = request.getHeader("oaid");
        IfconfigUtil ifconfigUtil = new IfconfigUtil();
        String ipAddr = ifconfigUtil.getIpAddr(request);
        if(!StringUtils.isEmpty(ipx)){
            taskExecutor.execute(() -> {
                wuyuanAdService.trigger(WuyuanAdRequest.builder()
                        .ipAddr(ipAddr)
                        .ipAddrV6(ipx)
                        .appleAdToken(appleAdToken)
                        .osVersion(osVersion)
                        .osType(osType)
                        .idfa(idfa)
                        .imei(imei)
                        .oaid(oaid)
                        .userId(String.valueOf(userInfoVO.getUserId()))
                        .wuyuanAdActionType(WuyuanAdActionType.REGISTER)
                        .build());
            });
        }
        return Result.OK(loginResponse);
    }


    @ApiOperation("获取用户信息")
    @GetMapping("/getUserInfo")
    public Result<LoginResponse> getUserInfo(HttpServletRequest request) {
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        // 校验过期时间
        userInfoVO.setVipExpiryDate(keepTheLargerOne(userInfoVO.getCdKeyExpiryDate(),userInfoVO.getVipExpiryDate()));
        LoginResponse loginResponse = LoginResponse.builder()
                .userInfo(userInfoVO)
                .build();
        String osType = request.getHeader("osType");
        String systemName = request.getHeader("systemName");
        String channel = request.getHeader("channel");
        String version = request.getHeader("version");
        String deviceBrand = request.getHeader("deviceBrand");
//        log.info("getUserOsType:"+osType);
        taskExecutor.execute(() -> {
            if(Objects.isNull(userInfoVO.getCountryCodeId())){
                // 获取ip地址
                String ipAddr = IpUtils.getIpAddr(request);
                // 更新用户定位信息
                String geocode = "https://ipinfo.io/" + ipAddr + "?token=4611683bd70ffe";
                HttpRequest geoRequest = HttpRequest.get(geocode).timeout(5000);
                HttpResponse geoResponse = geoRequest.execute();

                JSONObject geoJsonObject = JSONObject.parseObject(geoResponse.body());
                log.info(geoJsonObject.toJSONString());
                if(Objects.isNull(geoJsonObject.getString("status"))){
                    String locStr = geoJsonObject.getString("loc");
                    String[] split = locStr.split(",");
                    Double lat = Double.valueOf(split[0]);
                    Double lon = Double.valueOf(split[1]);
                    countryCodeService.setUserRegion(lat, lon, userInfoVO.getUserId());
                }else {
                    countryCodeService.setIp(ipAddr, userInfoVO.getUserId());
                }
            }
            taskExecutor.execute(() -> {
                countryCodeService.setInfo(userInfoVO,osType,channel,version,deviceBrand,systemName);
            });
        });
        return Result.OK(loginResponse);
    }

    @ApiOperation(value = "发送验证码")
    @RequestMapping(value = "/verifyCode/send", method = RequestMethod.POST)
    public Result sendVerifyCode(@RequestBody SmsRequest sendSmsRequest) {
//        if(!ValidateUtil.isPhone(sendSmsRequest.getPhone())){
//            log.error("手机号码:{}格式错误", sendSmsRequest.getPhone());
//            return Result.error(CommonErrorCode.PARAMETER_ERROR,"格式错误");
//        }
        // 判断是否频繁发送验证码
        String lastSendTimeStr = verificationCodeCache.get(sendSmsRequest.getPhone());
        if (lastSendTimeStr != null) {
            long lastSendTime = Long.parseLong(lastSendTimeStr.split(":")[1]);
            if (System.currentTimeMillis() - lastSendTime < 60000) {
                return Result.error("操作频繁，请稍后重试！");
            }
        }

        // 生成验证码
        String verificationCode = generateVerificationCode();
//        JSONObject obj = new JSONObject();
//        obj.put("code", verificationCode);
//        try {
//            DySmsHelper.sendSms(sendSmsRequest.getPhone(), obj, DySmsEnum.LOGIN_TEMPLATE_CODE);
//        } catch (ClientException e) {
//            return Result.error(CommonErrorCode.SEND_SMS_ERROR,"系统网络异常！");
//        }
        // 发送验证码逻辑...
        Result result = aliyunSmsService.sendSms(sendSmsRequest, verificationCode);
        if(result.isSuccess()){
            // 将验证码放入缓存，有效期为5分钟
            verificationCodeCache.put(sendSmsRequest.getPhone(), verificationCode + ":" + System.currentTimeMillis());
        }

        return result;
    }

    /**
     * 注销用户
     * @return
     */
    @ApiOperation(value = "注销用户")
    @PostMapping("/cancelUser")
    public Result cancelUser() {
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        return Result.OK(userInfoService.cancelUser(userInfoVO.getUserId()));
    }

    /**
     * 修改用户信息
     * @param userUpdateRequest
     * @return
     */
    @ApiOperation(value = "修改用户信息")
    @RequestMapping(value = "/update", method = {RequestMethod.PUT,RequestMethod.POST})
    public Result update(@RequestBody UserUpdateRequest userUpdateRequest) {
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        userInfoService.updateUserInfo(userInfoVO.getUserId(), userUpdateRequest);
        return Result.OK();
    }

    public boolean codeCheck(String code, String phone) {
        if ((phone.equals("18618150419") || phone.equals("15011597976")) && code.equals("888888")) {
            return true;
        }
        String cachedCode = verificationCodeCache.get(phone);
        if (cachedCode == null) {
            return false;
        }
        if (!cachedCode.split(":")[0].equals(code)) {
            return false;
        }
        verificationCodeCache.remove(phone);
        return true;
    }

    private String generateVerificationCode() {
        // 生成随机的6位数字验证码
        return RandomUtil.randomNumbers(6);
    }

    public static LocalDateTime keepTheLargerOne(LocalDateTime dateTime1, LocalDateTime dateTime2) {
        if(Objects.isNull(dateTime1) && Objects.isNull(dateTime2)){
            return null;
        }
        if(Objects.isNull(dateTime1)){
            dateTime1 = LocalDateTime.MIN;
        }
        if(Objects.isNull(dateTime2)){
            dateTime2 = LocalDateTime.MIN;
        }
        return dateTime1.isAfter(dateTime2) ? dateTime1 : dateTime2;
    }
}
