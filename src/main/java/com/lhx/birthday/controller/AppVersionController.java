package com.lhx.birthday.controller;

import com.lhx.birthday.response.version.AppVersionResponse;
import com.lhx.birthday.service.IAppVersionService;
import com.lhx.birthday.service.IUserInfoService;
import com.lhx.birthday.util.CommonUtil;
import com.lhx.birthday.vo.Result;
import com.lhx.birthday.vo.UserInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR> lhx
 * @date 2023/10/30 16:13
 */
@RequestMapping("/version")
@RestController
@Slf4j
@Api(description = "app接口 - 版本模块", tags = "AppVersionController")
public class AppVersionController {

    @Autowired
    private IAppVersionService appVersionService;

    /**
     * 获取版本列表
     * @return
     */
    @GetMapping("/list")
    @ApiOperation("获取版本列表")
    public Result<AppVersionResponse> getList() {
        return Result.OK(appVersionService.getList());
    }

}
