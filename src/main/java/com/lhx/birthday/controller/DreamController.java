package com.lhx.birthday.controller;

import com.lhx.birthday.request.dream.DreamDetailRequest;
import com.lhx.birthday.request.dream.DreamRequest;
import com.lhx.birthday.response.dream.DreamDetailResponse;
import com.lhx.birthday.response.dream.DreamHotResponse;
import com.lhx.birthday.response.dream.DreamResponse;
import com.lhx.birthday.service.IDreamService;
import com.lhx.birthday.vo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@Slf4j
@RequestMapping("/dream")
@Api(description = "app接口 - 周公解梦模块", tags = "DreamController")
public class DreamController {

    @Autowired
    private IDreamService dreamService;

    /**
     * 梦境搜索
     * @param dreamRequest
     * @return
     */
    @ApiOperation("梦境搜索")
    @PostMapping("/search")
    public Result<DreamResponse> search(@RequestBody DreamRequest dreamRequest){
        DreamResponse dreamResponse = dreamService.search(dreamRequest);
        return Result.OK(dreamResponse);
    }

    /**
     * 梦境详情
     * @param dreamDetailRequest
     * @return
     */
    @ApiOperation("梦境详情")
    @PostMapping("/detail")
    public Result<DreamDetailResponse> detail(@RequestBody DreamDetailRequest dreamDetailRequest){
        DreamDetailResponse detailResponse = dreamService.detail(dreamDetailRequest);
        return Result.OK(detailResponse);
    }

    /**
     * 热门梦境
     * @return
     */
    @ApiOperation("热门梦境")
    @GetMapping("/hot")
    public Result<DreamHotResponse> hot(){
        return Result.OK(dreamService.dreamHotList());
    }

}
