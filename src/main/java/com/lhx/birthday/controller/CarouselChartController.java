package com.lhx.birthday.controller;

import com.lhx.birthday.enums.DefaultFlag;
import com.lhx.birthday.request.carouselchart.CarouselChartBaseRequest;
import com.lhx.birthday.request.carouselchart.CarouselChartRequest;
import com.lhx.birthday.response.carouselchart.CarouselChartResponse;
import com.lhx.birthday.service.ICarouselChartService;
import com.lhx.birthday.service.IUserInfoService;
import com.lhx.birthday.util.CommonUtil;
import com.lhx.birthday.vo.Result;
import com.lhx.birthday.vo.UserInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * <AUTHOR> lhx
 * @date 2023/10/30 16:13
 */
@RequestMapping("/carousel")
@RestController
@Slf4j
@Api(description = "app接口 - 轮播图模块", tags = "CarouselChartController")
public class CarouselChartController {

    @Autowired
    private ICarouselChartService carouselChartService;

    @Autowired
    private CommonUtil commonUtil;

    @Autowired
    private IUserInfoService userInfoService;

    /**
     * 获取反馈记录列表
     * @return
     */
    @PostMapping("/list")
    @ApiOperation("获取轮播图列表")
    public Result<CarouselChartResponse> getList(@RequestBody CarouselChartRequest carouselChartRequest, HttpServletRequest request) {
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        String channel = request.getHeader("channel");
        String versionString = request.getHeader("version");
        CarouselChartBaseRequest carouselChartBaseRequest = new CarouselChartBaseRequest();
        BeanUtils.copyProperties(carouselChartRequest, carouselChartBaseRequest);
        carouselChartBaseRequest.setVipState(DefaultFlag.fromValue(userInfoVO.getVip()));
        carouselChartBaseRequest.setVersion(convertVersionToInt(versionString));
        carouselChartBaseRequest.setChannel(channel);
        carouselChartBaseRequest.setLanguageType(userInfoVO.getLanguageType());
        return Result.OK(carouselChartService.getList(carouselChartBaseRequest));
    }

    private Integer convertVersionToInt(String version) {
        if (StringUtils.isEmpty(version)) {
            return null;
        }
        String[] parts = version.split("\\.");
        int result = 0;
        if (parts.length > 0) {
            result += Integer.parseInt(parts[0]) * 10000;
        }
        if (parts.length > 1) {
            result += Integer.parseInt(parts[1]) * 100;
        }
        if (parts.length > 2) {
            result += Integer.parseInt(parts[2]);
        }
        return result;
    }

}
