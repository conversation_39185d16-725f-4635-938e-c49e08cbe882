package com.lhx.birthday.controller;

import com.lhx.birthday.request.userinfo.FeedbackAddRequest;
import com.lhx.birthday.response.FeedbackResponse;
import com.lhx.birthday.service.IFeedbackService;
import com.lhx.birthday.service.IUserInfoService;
import com.lhx.birthday.util.CommonUtil;
import com.lhx.birthday.vo.FeedbackVO;
import com.lhx.birthday.vo.Result;
import com.lhx.birthday.vo.UserInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@RequestMapping("/feedback")
@RestController
@Slf4j
@Api(description = "app接口 - 反馈", tags = "FeedbackController")
public class FeedbackController {

    @Autowired
    private IFeedbackService feedbackService;

    @Autowired
    private IUserInfoService userInfoService;

    @Resource
    private CommonUtil commonUtil;

    /**
     * 添加反馈记录
     * @param feedbackAddRequest
     * @return
     */
    @PostMapping("/add")
    @ApiOperation("添加反馈记录")
    public Result addFeedback(@RequestBody FeedbackAddRequest feedbackAddRequest) {
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户信息
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        feedbackService.addFeedback(Long.parseLong(customerId), feedbackAddRequest);
        return Result.OK();
    }

    /**
     * 获取反馈记录列表
     * @return
     */
    @GetMapping("/list")
    @ApiOperation("获取反馈记录列表")
    public Result<FeedbackResponse> getList() {
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户信息
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        List<FeedbackVO> feedbackVOList = feedbackService.getList(Long.parseLong(customerId));
        FeedbackResponse feedbackResponse = FeedbackResponse.builder()
                .feedbackVOS(feedbackVOList)
                .build();
        return Result.OK(feedbackResponse);
    }

}
