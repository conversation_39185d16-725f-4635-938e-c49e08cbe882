package com.lhx.birthday.controller;

import com.lhx.birthday.request.profilegroup.*;
import com.lhx.birthday.response.profilegroup.ProfileGroupResponse;
import com.lhx.birthday.service.IProfileGroupService;
import com.lhx.birthday.service.IUserInfoService;
import com.lhx.birthday.util.CommonUtil;
import com.lhx.birthday.vo.Result;
import com.lhx.birthday.vo.UserInfoVO;
import com.lhx.birthday.vo.profile.ProfileGroupVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

import static com.lhx.birthday.constant.SettingConstant.*;

@RequestMapping("/profile/group")
@RestController
@Slf4j
@Api(description = "app接口 - 档案分组模块", tags = "ProfileGroupController")
public class ProfileGroupController {

    @Autowired
    private IProfileGroupService profileGroupService;

    @Autowired
    private IUserInfoService userInfoService;

    @Autowired
    private CommonUtil commonUtil;

    /**
     * 添加档案分组
     * @param profileGroupAddRequest
     * @return
     */
    @ApiOperation("添加档案分组")
    @PostMapping("/add")
    public Result add(@RequestBody ProfileGroupAddRequest profileGroupAddRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        // 监测emoji
        if(containsEmoji(profileGroupAddRequest.getGroupName())){
            return Result.error("分组名称输入不合法");
        }
        ProfileGroupBaseRequest profileGroupCheckBaseRequest = new ProfileGroupBaseRequest();
        profileGroupCheckBaseRequest.setUserId(Long.parseLong(customerId));
        List<ProfileGroupVO> profileGroupList = profileGroupService.getProfileList(profileGroupCheckBaseRequest);
        int profileGroupNum = profileGroupList.size();
        int maxProfileContact = userInfoVO.getVip().equals(IS_VIP) ? VIP_MAX_PROFILE_GROUP_CONTACT : USER_MAX_PROFILE_GROUP_CONTACT;
        if(profileGroupNum>=maxProfileContact){
            return Result.error("超过最大分组数量上限");
        }
        ProfileGroupBaseRequest profileGroupBaseRequest = new ProfileGroupBaseRequest();
        BeanUtils.copyProperties(profileGroupAddRequest, profileGroupBaseRequest);
        profileGroupBaseRequest.setUserId(Long.parseLong(customerId));
        profileGroupService.addProfileGroup(profileGroupBaseRequest);
        return Result.OK();
    }

    /**
     * 更新档案分组
     * @param profileGroupUpdateRequest
     * @return
     */
    @ApiOperation("更新档案分组")
    @PostMapping("/update")
    public Result update(@RequestBody ProfileGroupUpdateRequest profileGroupUpdateRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        // 监测emoji
        if(containsEmoji(profileGroupUpdateRequest.getGroupName())){
            return Result.error("分组名称输入不合法");
        }

        ProfileGroupBaseRequest profileGroupBaseRequest = new ProfileGroupBaseRequest();
        BeanUtils.copyProperties(profileGroupUpdateRequest, profileGroupBaseRequest);
        profileGroupBaseRequest.setUserId(Long.parseLong(customerId));
        profileGroupService.updateProfileGroup(profileGroupBaseRequest);
        return Result.OK();
    }

    /**
     * 更新排序
     * @param profileGroupUpdateSortRequest
     * @return
     */
    @ApiOperation("更新排序")
    @PostMapping("/updateSort")
    public Result updateSort(@RequestBody ProfileGroupUpdateSortRequest profileGroupUpdateSortRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        profileGroupService.updateSort(profileGroupUpdateSortRequest);
        return Result.OK();
    }


    /**
     * 删除档案分组
     * @param profileGroupDelRequest
     * @return
     */
    @ApiOperation("删除档案分组")
    @PostMapping("/del")
    public Result del(@RequestBody ProfileGroupDelRequest profileGroupDelRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        profileGroupService.delById(profileGroupDelRequest.getId());
        return Result.OK();
    }

    /**
     * 档案分组列表
     * @return
     */
    @ApiOperation("档案分组列表")
    @RequestMapping(value = "/getList", method = {RequestMethod.GET,RequestMethod.POST})
    public Result<ProfileGroupResponse> getList(){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }

        ProfileGroupBaseRequest profileGroupBaseRequest = new ProfileGroupBaseRequest();
        profileGroupBaseRequest.setUserId(Long.parseLong(customerId));
        List<ProfileGroupVO> profileGroupVOList = profileGroupService.getProfileList(profileGroupBaseRequest);

        return Result.OK(ProfileGroupResponse.builder()
                .profileGroups(profileGroupVOList)
                .build());
    }

    /**
     * 删除分组下单个档案
     * @param profileGroupRelDelRequest
     * @return
     */
    @ApiOperation("删除分组下单个档案")
    @PostMapping("/del/profile")
    public Result delProfile(@RequestBody ProfileGroupRelDelRequest profileGroupRelDelRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        profileGroupService.delByProfileAndProfileGroup(profileGroupRelDelRequest);
        return Result.OK();
    }

    /**
     * 更新分组下档案成员
     * @param profileGroupRelUpdateRequest
     * @return
     */
    @ApiOperation("更新分组下档案成员")
    @PostMapping("/update/profile")
    public Result updateProfile(@RequestBody ProfileGroupRelUpdateRequest profileGroupRelUpdateRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }

        profileGroupService.updateProfileGroupRel(profileGroupRelUpdateRequest,userInfoVO.getUserId());
        return Result.OK();
    }

    /**
     * 检查给定的字符串是否包含 Emoji。
     *
     * @param text 要检查的字符串
     * @return 如果字符串中包含 Emoji，则返回 true；否则返回 false
     */
    public static boolean containsEmoji(String text) {
        // 正则表达式匹配 Emoji
        String emojiRegex = "[\\ud83c[\\udf00-\\udfff]|\\ud83d[\\udc00-\\ude4f\\ude80-\\udeff]|\\u2694|\\u2600-\\u26FF\\u2702-\\u27B0]";
        Pattern pattern = Pattern.compile(emojiRegex);

        // 使用正则表达式查找 Emoji
        return pattern.matcher(text).find();
    }
}
