package com.lhx.birthday.controller;

import com.lhx.birthday.constant.TriggerTypeConstant;
import com.lhx.birthday.enums.DefaultFlag;
import com.lhx.birthday.request.market.*;
import com.lhx.birthday.response.market.*;
import com.lhx.birthday.service.IMarketInviteService;
import com.lhx.birthday.service.IMarketService;
import com.lhx.birthday.service.IMarketSignService;
import com.lhx.birthday.service.IUserInfoService;
import com.lhx.birthday.util.CommonErrorCode;
import com.lhx.birthday.util.CommonUtil;
import com.lhx.birthday.vo.Result;
import com.lhx.birthday.vo.UserInfoVO;
import com.lhx.birthday.vo.market.Market5StarReviewVO;
import com.lhx.birthday.vo.market.Market5StarStateVO;
import com.lhx.birthday.vo.market.RecoverSettingVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.time.LocalDate;
import java.util.Map;
import java.util.Objects;

@RequestMapping("/market")
@RestController
@Slf4j
@Api(description = "app接口 - 营销模块", tags = "MarketController")
public class MarketController {

    @Autowired
    private IMarketService marketService;

    @Autowired
    private IMarketInviteService marketInviteService;

    @Autowired
    private IMarketSignService marketSignService;

    @Resource
    private CommonUtil commonUtil;

    @Autowired
    private IUserInfoService userInfoService;

    /**
     * 5星好评
     * @return
     */
    @ApiOperation("5星好评")
    @PostMapping("/v2/star5-review")
    public Result star5ReviewV2(@Valid @RequestBody Star5Request star5Request) {
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }

        // 游客不能使用该功能
//        if(userInfoVO.getTouristState().equals(DefaultFlag.YES)){
//            return Result.error("限登录用户使用");
//        }

        // 校验事件类型是否合法
        if (!TriggerTypeConstant.isValidTriggerType(star5Request.getTriggerType())) {
            return Result.error("无效的事件类型");
        }

        // 如果是确认操作，需要检查是否已经好评过
        if (star5Request.getOpType() == 1 && marketService.checkStar5ReviewV2(userInfoVO.getUserId())) {
            return Result.error("已5星过");
        }

        return Result.ok(Market5StarReviewResponse.builder()
                .market5StarReview(marketService.star5ReviewV2(userInfoVO, star5Request))
                .build());
    }

    /**
     * 5星好评状态
     * @return
     */
    @ApiOperation("5星好评状态")
    @PostMapping("/v2/star5-state")
    public Result<Market5StarStateResponse> getStar5StateV2() {
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }

        // 检查用户是否已经提交过5星好评
        boolean hasStar5 = marketService.checkStar5ReviewV2(userInfoVO.getUserId());

        return Result.ok(Market5StarStateResponse.builder()
                .market5StarState(Market5StarStateVO.builder()
                        .state(hasStar5 ? DefaultFlag.YES : DefaultFlag.NO)
                        .build())
                .build());
    }

    /**
     * 5星好评
     * @return
     */
    @ApiOperation("5星好评")
    @PostMapping("/star5-review")
    public Result star5Review(@RequestBody Star5Request star5Request) {
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        if(userInfoVO.getTouristState().equals(DefaultFlag.YES)){
            return Result.error("限登录用户使用");
        }
        if(marketService.checkStar5Review(userInfoVO.getUserId())){
            return Result.error("已5星过");
        }
        return Result.ok(Market5StarReviewResponse.builder()
                .market5StarReview(marketService.star5Review(userInfoVO,star5Request))
                .build());
    }

    /**
     * 5星好评状态
     * @return
     */
    @ApiOperation("5星好评状态")
    @PostMapping("/star5-state")
    public Result<Market5StarStateResponse> getStar5State() {
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        return Result.ok(Market5StarStateResponse.builder()
                .market5StarState(Market5StarStateVO.builder()
                        .state(userInfoVO.getStar5State())
                        .build())
                .build());
    }

    /**
     * 兑换邀请
     * @param invitationRequest
     * @return
     */
    @ApiOperation("兑换邀请")
    @PostMapping("/invitation")
    public Result invitation(@Valid @RequestBody InvitationRequest invitationRequest) {
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        if(userInfoVO.getTouristState().equals(DefaultFlag.YES)){
            return Result.error("限登录用户使用");
        }
        return marketInviteService.invitation(invitationRequest,userInfoVO);
    }

    /**
     * 邀请明细
     * @return
     */
    @ApiOperation("邀请明细")
    @PostMapping("/invitation-history")
    public Result<InvitationHistoryResponse> getInvitationHistory(@RequestBody InvitationHisRequest invitationHisRequest) {
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        return Result.ok(InvitationHistoryResponse.builder()
                .invitationHistory(marketInviteService.getInvitationHistory(userInfoVO,invitationHisRequest))
                .build());
    }

    /**
     * 积分明细
     * @return
     */
    @ApiOperation("积分明细")
    @PostMapping("/integral-history")
    public Result<PointHistoryResponse> getIntegralHistory() {
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        return Result.ok(PointHistoryResponse.builder()
                .pointHistory(marketService.getIntegralHistory(userInfoVO))
                .build());
    }


    /**
     * 积分兑换
     * @param integralPayRequest
     * @return
     */
    @ApiOperation("积分兑换")
    @PostMapping("/integral-pay")
    public Result pointPay(@Valid @RequestBody IntegralPayRequest integralPayRequest) {
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        if(userInfoVO.getTouristState().equals(DefaultFlag.YES)){
            return Result.error("限登录用户使用");
        }
        return marketService.pointPay(integralPayRequest,userInfoVO);
    }

    /**
     * 签到
     * @return
     */
    @ApiOperation("签到")
    @PostMapping("/sign-in")
    public Result signIn(@RequestBody SignRequest signRequest) {
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        if(userInfoVO.getTouristState().equals(DefaultFlag.YES)){
            return Result.error("限登录用户使用");
        }
        return marketSignService.signIn(userInfoVO,signRequest);
    }

    /**
     * 签到历史
     * @return
     */
    @ApiOperation("签到历史")
    @PostMapping("/sign-in-history")
    public Result<SignInHistoryResponse> getSignInHistory(@RequestBody SignHisRequest signHisRequest) {
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        return Result.ok(SignInHistoryResponse.builder()
                .signInHistory(marketSignService.getSignInHistory(userInfoVO,signHisRequest))
                .build());
    }


    /**
     * 检查/更新挽回状态
     * @param recoverStateRequest
     * @return
     */
    @ApiOperation("检查/更新挽回状态")
    @PostMapping("/recover-state")
    public Result<RecoverStateResponse> checkRecoverState(@RequestBody(required = false) RecoverStateRequest recoverStateRequest) {
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户信息
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }

        // 如果请求参数不为空且包含recoverState=0，则更新状态为关闭
        if (recoverStateRequest != null && recoverStateRequest.getRecoverState() != null && recoverStateRequest.getRecoverState() == 0) {
            userInfoService.updateRecoverState(userInfoVO.getUserId(), 0);
            return Result.ok(RecoverStateResponse.builder()
                    .needPopup(false)
                    .recoverSetting(RecoverSettingVO.builder().build())
                    .isFirstPopup(false)
                    .build());
        }

        // 检查是否需要弹窗
        boolean needPopup = false;
        boolean isFirstPopup = false;
        // 如果状态为null或1，且日期为null或小于今天，则需要弹窗
        if (userInfoVO.getRecoverState() == null || userInfoVO.getRecoverState() == 1) {
            if (userInfoVO.getRecoverDate() == null ||
                    userInfoVO.getRecoverDate().isBefore(LocalDate.now())) {
                needPopup = true;
                // 判断是否是第一次弹窗
                isFirstPopup = userInfoVO.getRecoverDate() == null;
                // 如果需要弹窗，则更新日期为今天
                userInfoService.updateRecoverDate(userInfoVO.getUserId(), LocalDate.now());
            }
        }

        // 获取设备类型
        Integer deviceType = null;
        if (recoverStateRequest != null) {
            deviceType = recoverStateRequest.getDeviceType();
        }

        // 获取挽回设置
        RecoverSettingVO recoverSetting = marketService.getRecoverSetting(deviceType);

        return Result.ok(RecoverStateResponse.builder()
                .needPopup(needPopup)
                .recoverSetting(recoverSetting)
                .isFirstPopup(isFirstPopup)
                .build());
    }

}
