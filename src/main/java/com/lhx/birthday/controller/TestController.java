package com.lhx.birthday.controller;

import com.lhx.birthday.constant.RedisKeyConstant;
import com.lhx.birthday.entity.UserInfo;
import com.lhx.birthday.entity.ProfilePrompt;
import com.lhx.birthday.enums.ActionUnit;
import com.lhx.birthday.enums.ZodiacSignType;
import com.lhx.birthday.mapper.UserInfoMapper;
import com.lhx.birthday.redis.RedisService;
import com.lhx.birthday.service.IUserInfoService;
import com.lhx.birthday.vo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

@RestController
@RequestMapping("/test")
@Slf4j
@Api(description = "测试接口", tags = "TestController")
public class TestController {

    @Resource
    private UserInfoMapper userInfoMapper;
    
    @Autowired
    private IUserInfoService userInfoService;
    
    @Autowired
    private RedisService redisService;
    
    @PersistenceContext
    private EntityManager entityManager;
    
    /**
     * 初始化所有用户的推送设置到Redis
     * 只处理registrationId不为空的用户
     * @return 初始化结果
     */
    @ApiOperation("初始化所有用户的推送设置到Redis")
    @GetMapping("/init-push-settings")
    public Result<String> initPushSettings() {
        log.info("开始初始化用户推送设置...");
        
        // 清空已存在的Redis键
        clearExistingRedisKeys();

        // 重新初始化 birthdays（档案生日提醒） 和 推送开关（星座与各天象事件）
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);

        // 1) 初始化生日提醒（遍历所有 profile_prompt，按用户语言写入简体或:hant）
        try {
            String sqlProfile = "SELECT * FROM profile_prompt";
            Query qProfile = entityManager.createNativeQuery(sqlProfile, ProfilePrompt.class);
            List<ProfilePrompt> prompts = qProfile.getResultList();
            if (prompts != null && !prompts.isEmpty()) {
                userInfoService.initPushData(prompts, ActionUnit.ADD, null);
            }
            log.info("生日提醒初始化完成，处理{}条", prompts == null ? 0 : prompts.size());
        } catch (Exception e) {
            log.error("初始化生日提醒失败", e);
        }

        // 2) 初始化各类推送开关（只处理有registrationId的活跃用户）
        String sql = "SELECT * FROM user_info WHERE registration_id IS NOT NULL AND registration_id != '' AND state = 0";
        Query query = entityManager.createNativeQuery(sql, UserInfo.class);
        List<UserInfo> users = query.getResultList();
        log.info("找到{}个有registrationId的用户", users.size());
        users.forEach(user -> {
            try {
                initUserPushSettings(user);
                successCount.incrementAndGet();
                if (successCount.get() % 200 == 0) {
                    log.info("已处理{}个用户", successCount.get());
                }
            } catch (Exception e) {
                failCount.incrementAndGet();
                log.error("初始化用户[{}]推送设置失败: {}", user.getUserId(), e.getMessage(), e);
            }
        });

        String result = String.format("初始化完成，成功: %d，失败: %d", successCount.get(), failCount.get());
        log.info(result);
        return Result.ok(result);
    }
    
    /**
     * 清空已存在的推送相关Redis键
     */
    private void clearExistingRedisKeys() {
        log.info("开始清空已存在的推送相关Redis键...");
        
        List<String> keyPatterns = new ArrayList<>();
        
        // 添加星座推送相关的键模式
        keyPatterns.add("user:push:horoscope:remind*");
        
        // 添加其他推送相关的键模式
        keyPatterns.add(RedisKeyConstant.USER_PUSH_RETROGRADE_WARNING + "*");
        keyPatterns.add(RedisKeyConstant.USER_PUSH_PLANET_CHANGE + "*");
        keyPatterns.add(RedisKeyConstant.USER_PUSH_MOON_PHASE + "*");
        keyPatterns.add(RedisKeyConstant.USER_PUSH_ECLIPSE + "*");
        keyPatterns.add(RedisKeyConstant.USER_PUSH_MOON_VOID + "*");
        keyPatterns.add(RedisKeyConstant.USER_PUSH_PLANET_ASPECT + "*");
        
        // 添加生日推送相关的键模式（包括所有语言版本）
        keyPatterns.add(RedisKeyConstant.USER_PUSH_SOLAR_DAILY_PROMPT + "*");
        keyPatterns.add(RedisKeyConstant.USER_PUSH_SOLAR_ONE_DAY_PROMPT + "*");
        keyPatterns.add(RedisKeyConstant.USER_PUSH_SOLAR_THREE_DAY_PROMPT + "*");
        keyPatterns.add(RedisKeyConstant.USER_PUSH_SOLAR_SEVEN_DAY_PROMPT + "*");
        keyPatterns.add(RedisKeyConstant.USER_PUSH_SOLAR_FIFTEEN_DAY_PROMPT + "*");
        keyPatterns.add(RedisKeyConstant.USER_PUSH_SOLAR_THIRTY_DAY_PROMPT + "*");
        keyPatterns.add(RedisKeyConstant.USER_PUSH_LUNAR_DAILY_PROMPT + "*");
        keyPatterns.add(RedisKeyConstant.USER_PUSH_LUNAR_ONE_DAY_PROMPT + "*");
        keyPatterns.add(RedisKeyConstant.USER_PUSH_LUNAR_THREE_DAY_PROMPT + "*");
        keyPatterns.add(RedisKeyConstant.USER_PUSH_LUNAR_SEVEN_DAY_PROMPT + "*");
        keyPatterns.add(RedisKeyConstant.USER_PUSH_LUNAR_FIFTEEN_DAY_PROMPT + "*");
        keyPatterns.add(RedisKeyConstant.USER_PUSH_LUNAR_THIRTY_DAY_PROMPT + "*");
        
        // 按星座类型分开存储的键模式
        for (int i = 0; i < 12; i++) {
            keyPatterns.add("user:push:horoscope:remind:" + i + "*");
        }
        
        int deletedKeysCount = 0;
        
        // 遍历所有键模式，查找并删除匹配的键
        for (String pattern : keyPatterns) {
            try {
                Set<String> keys = redisService.keys(pattern);
                if (keys != null && !keys.isEmpty()) {
                    for (String key : keys) {
                        redisService.delete(key);
                        deletedKeysCount++;
                    }
                    log.info("已删除{}个匹配模式{}的Redis键", keys.size(), pattern);
                }
            } catch (Exception e) {
                log.error("删除Redis键模式{}时发生错误: {}", pattern, e.getMessage(), e);
            }
        }
        
        log.info("已清空{}个推送相关的Redis键", deletedKeysCount);
    }
    
    /**
     * 初始化单个用户的所有推送设置
     * @param userInfo 用户信息
     */
    private void initUserPushSettings(UserInfo userInfo) {
        if (userInfo == null || userInfo.getUserId() == null || 
                userInfo.getRegistrationId() == null || userInfo.getRegistrationId().isEmpty()) {
            return;
        }
        
        Long userId = userInfo.getUserId();
        
        // 初始化星座提醒推送设置
        if (userInfo.getHoroscopeRemind() == 1 && Objects.nonNull(userInfo.getRemindTime()) && 
                Objects.nonNull(userInfo.getZodiacSignType())) {
            updateZodiacPushSettings(userId, userInfo.getZodiacSignType(), userInfo.getRemindTime(), ActionUnit.ADD);
        }
        
        // 初始化逆行警告推送设置
        if (userInfo.getRetrogradeWarning() == 1) {
            updatePushSettings(userId, "retrograde:warning", null, ActionUnit.ADD);
        }
        
        // 初始化行星换座推送设置
        if (userInfo.getPlanetChange() == 1) {
            updatePushSettings(userId, "planet:change", null, ActionUnit.ADD);
        }
        
        // 初始化月相推送设置
        if (userInfo.getMoonPhase() == 1) {
            updatePushSettings(userId, "moon:phase", null, ActionUnit.ADD);
        }
        
        // 初始化日食月食推送设置
        if (userInfo.getEclipse() == 1) {
            updatePushSettings(userId, "eclipse", null, ActionUnit.ADD);
        }
        
        // 初始化空亡推送设置
        if (userInfo.getMoonVoid() == 1) {
            updatePushSettings(userId, "moon:void", null, ActionUnit.ADD);
        }
        
        // 初始化行星相位推送设置
        if (userInfo.getPlanetAspect() == 1) {
            updatePushSettings(userId, "planet:aspect", null, ActionUnit.ADD);
        }
    }
    
    /**
     * 更新用户的星座推送设置到Redis
     * @param userId 用户ID
     * @param zodiacSignType 星座类型
     * @param timeValue 提醒时间
     * @param actionUnit 操作类型（添加或删除）
     */
    private void updateZodiacPushSettings(Long userId, ZodiacSignType zodiacSignType, String timeValue, ActionUnit actionUnit) {
        userInfoService.updateZodiacPushSettings(userId, zodiacSignType, timeValue, actionUnit);
    }
    
    /**
     * 更新用户的推送设置到Redis
     * @param userId 用户ID
     * @param settingType 设置类型，如 "retrograde:warning", "planet:change" 等
     * @param timeValue 时间值，仅星座提醒等需要具体时间的设置使用
     * @param actionUnit 操作类型（添加或删除）
     */
    private void updatePushSettings(Long userId, String settingType, String timeValue, ActionUnit actionUnit) {
        userInfoService.updatePushSettings(userId, settingType, timeValue, actionUnit);
    }
} 