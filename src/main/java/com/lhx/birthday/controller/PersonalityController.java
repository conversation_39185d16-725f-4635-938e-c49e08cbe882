package com.lhx.birthday.controller;

import com.github.houbb.opencc4j.core.impl.ZhConvertBootstrap;
import com.lhx.birthday.entity.Personality;
import com.lhx.birthday.entity.PersonalityTestQuestion;
import com.lhx.birthday.entity.PersonalityTestResult;
import com.lhx.birthday.enums.LanguageType;
import com.lhx.birthday.request.personality.PersonalityTestRequest;
import com.lhx.birthday.service.IPersonalityTestService;
import com.lhx.birthday.service.IUserInfoService;
import com.lhx.birthday.util.CommonUtil;
import com.lhx.birthday.vo.PersonalityQuestionVO;
import com.lhx.birthday.vo.PersonalityResultVO;
import com.lhx.birthday.vo.Result;
import com.lhx.birthday.vo.UserInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 性格测试Controller
 * <AUTHOR> lhx
 */
@RestController
@RequestMapping("/api/personality")
@Slf4j
public class PersonalityController {

    @Resource
    private IPersonalityTestService personalityTestService;
    
    @Resource
    private CommonUtil commonUtil;

    @Autowired
    private IUserInfoService userInfoService;

    /**
     * 获取所有题目
     * 
     * @return 题目列表（按照questionId排序）
     */
    @GetMapping("/questions")
    public Result<List<PersonalityQuestionVO>> getAllQuestions() {
        try {
            // 获取用户id
            String customerId = commonUtil.getOperatorId();
            // 查询用户已关注的省份列表
            UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
            if(Objects.isNull(userInfoVO)){
                return Result.error("error");
            }

            Map<Integer, List<PersonalityTestQuestion>> questionMap = personalityTestService.getAllQuestions();
            
            // 转换为前端需要的格式
            List<PersonalityQuestionVO> questionVOList = new ArrayList<>();
            
            for (Map.Entry<Integer, List<PersonalityTestQuestion>> entry : questionMap.entrySet()) {
                Integer questionId = entry.getKey();
                List<PersonalityTestQuestion> options = entry.getValue();
                
                // 确保所有选项都有相同的问题文本
                String questionText = options.get(0).getQuestionText();
                
                // 构建选项列表
                List<PersonalityQuestionVO.Option> optionList = options.stream()
                        .sorted(Comparator.comparing(PersonalityTestQuestion::getOptionLetter))
                        .map(opt -> PersonalityQuestionVO.Option.builder()
                                .optionLetter(opt.getOptionLetter())
                                .optionText(opt.getOptionText())
                                .build())
                        .collect(Collectors.toList());
                
                // 创建问题VO
                PersonalityQuestionVO questionVO = PersonalityQuestionVO.builder()
                        .questionId(questionId)
                        .questionText(questionText)
                        .options(optionList)
                        .build();
                
                questionVOList.add(questionVO);
            }
            
            // 按照问题ID排序
            questionVOList.sort(Comparator.comparing(PersonalityQuestionVO::getQuestionId));

            // 如果用户语言类型是繁体中文，需要进行转换
            if(userInfoVO.getLanguageType().equals(LanguageType.ZH_HANT)){
                ZhConvertBootstrap zhConvertBootstrap = ZhConvertBootstrap.newInstance();
                for (PersonalityQuestionVO questionVO : questionVOList) {
                    // 转换问题文本
                    questionVO.setQuestionText(zhConvertBootstrap.toTraditional(questionVO.getQuestionText()));
                    // 转换选项文本
                    for (PersonalityQuestionVO.Option option : questionVO.getOptions()) {
                        option.setOptionText(zhConvertBootstrap.toTraditional(option.getOptionText()));
                    }
                }
            }

            return Result.OK(questionVOList);
        } catch (Exception e) {
            log.error("获取题目列表失败", e);
            return Result.error("获取题目列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 提交测试答案并获取测试结果
     * 
     * @param request 测试请求
     * @return 测试结果
     */
    @PostMapping("/submit")
    public Result<PersonalityResultVO> submitTest(@RequestBody PersonalityTestRequest request) {
        try {
            // 从CommonUtil获取用户ID
            Long userId = Long.parseLong(commonUtil.getOperatorId());
            
            // 保存测试结果
            PersonalityTestResult testResult = personalityTestService.saveTestResult(
                    userId, request.getAnswers());
            
            // 获取对应的性格类型详情
            Personality personality = personalityTestService.getPersonalityByTypeCode(testResult.getTypeCode());
            
            // 转换为VO
            PersonalityResultVO resultVO = PersonalityResultVO.from(testResult, personality);
            
            // 获取用户信息进行繁体转换检查
            UserInfoVO userInfoVO = userInfoService.getByUserId(userId);
            if(Objects.nonNull(userInfoVO) && userInfoVO.getLanguageType().equals(LanguageType.ZH_HANT)){
                ZhConvertBootstrap zhConvertBootstrap = ZhConvertBootstrap.newInstance();
                if(resultVO.getTypeName() != null) {
                    resultVO.setTypeName(zhConvertBootstrap.toTraditional(resultVO.getTypeName()));
                }
                if(resultVO.getTypeTitle() != null) {
                    resultVO.setTypeTitle(zhConvertBootstrap.toTraditional(resultVO.getTypeTitle()));
                }
                if(resultVO.getTypeDesc() != null) {
                    resultVO.setTypeDesc(zhConvertBootstrap.toTraditional(resultVO.getTypeDesc()));
                }
                if(resultVO.getTagName() != null) {
                    resultVO.setTagName(zhConvertBootstrap.toTraditional(resultVO.getTagName()));
                }
                if(resultVO.getDescription() != null) {
                    resultVO.setDescription(zhConvertBootstrap.toTraditional(resultVO.getDescription()));
                }
            }
            
            return Result.OK(resultVO);
        } catch (Exception e) {
            log.error("提交测试失败", e);
            return Result.error("提交测试失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取用户的最新测试结果
     * 
     * @return 最新的测试结果
     */
    @GetMapping("/result")
    public Result<PersonalityResultVO> getUserLatestResult() {
        try {
            // 从CommonUtil获取用户ID
            Long userId = Long.parseLong(commonUtil.getOperatorId());
            
            PersonalityTestResult testResult = personalityTestService.getUserLatestResult(userId);
            
            if (testResult == null) {
                return Result.error("用户尚未进行测试");
            }
            
            // 获取对应的性格类型详情
            Personality personality = personalityTestService.getPersonalityByTypeCode(testResult.getTypeCode());
            
            // 转换为VO
            // 转换为VO
            PersonalityResultVO resultVO = PersonalityResultVO.from(testResult, personality);
            
            // 获取用户信息进行繁体转换检查
            UserInfoVO userInfoVO = userInfoService.getByUserId(userId);
            if(Objects.nonNull(userInfoVO) && userInfoVO.getLanguageType().equals(LanguageType.ZH_HANT)){
                ZhConvertBootstrap zhConvertBootstrap = ZhConvertBootstrap.newInstance();
                if(resultVO.getTypeName() != null) {
                    resultVO.setTypeName(zhConvertBootstrap.toTraditional(resultVO.getTypeName()));
                }
                if(resultVO.getTypeTitle() != null) {
                    resultVO.setTypeTitle(zhConvertBootstrap.toTraditional(resultVO.getTypeTitle()));
                }
                if(resultVO.getTypeDesc() != null) {
                    resultVO.setTypeDesc(zhConvertBootstrap.toTraditional(resultVO.getTypeDesc()));
                }
                if(resultVO.getTagName() != null) {
                    resultVO.setTagName(zhConvertBootstrap.toTraditional(resultVO.getTagName()));
                }
                if(resultVO.getDescription() != null) {
                    resultVO.setDescription(zhConvertBootstrap.toTraditional(resultVO.getDescription()));
                }
            }
            
            return Result.OK(resultVO);
        } catch (Exception e) {
            log.error("获取用户测试结果失败", e);
            return Result.error("获取用户测试结果失败：" + e.getMessage());
        }
    }
} 