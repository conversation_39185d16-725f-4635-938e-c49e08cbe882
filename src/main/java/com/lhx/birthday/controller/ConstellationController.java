package com.lhx.birthday.controller;

import com.github.houbb.opencc4j.core.impl.ZhConvertBootstrap;
import com.lhx.birthday.constant.LockManager;
import com.lhx.birthday.enums.ConstellationRelationship;
import com.lhx.birthday.enums.DefaultFlag;
import com.lhx.birthday.enums.LanguageType;
import com.lhx.birthday.redis.RedisService;
import com.lhx.birthday.request.constellation.*;
import com.lhx.birthday.request.profile.ProfileUpdateSortRequest;
import com.lhx.birthday.request.profile.RegionRequest;
import com.lhx.birthday.response.constellation.*;
import com.lhx.birthday.service.*;
import com.lhx.birthday.service.impl.ICountryCodeServiceImpl;
import com.lhx.birthday.util.CommonErrorCode;
import com.lhx.birthday.util.CommonUtil;
import com.lhx.birthday.vo.Result;
import com.lhx.birthday.vo.UserInfoVO;
import com.lhx.birthday.vo.constellation.ConstellationProfileVO;
import com.lhx.birthday.vo.profile.RegionVO;
import com.lhx.birthday.vo.profile.RelationshipVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.regex.Pattern;

@RequestMapping("/constellation")
@RestController
@Slf4j
@Api(description = "app接口 - 星座档案模块", tags = "ConstellationController")
public class ConstellationController {

    @Autowired
    private IConstellationService constellationService;

    @Autowired
    private IUserInfoService userInfoService;

    @Autowired
    private CommonUtil commonUtil;

    @Autowired
    private ICountryCodeServiceImpl countryCodeService;

    @Autowired
    private RedisService redisService;

    /**
     * 添加星座档案
     */
    @ApiOperation("添加星座档案")
    @PostMapping("/add")
    public Result add(@RequestBody ConstellationAddRequest request) {
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if (Objects.isNull(userInfoVO)) {
            return Result.error("error");
        }
        if(containsEmoji(request.getName())){
            return Result.error("真实姓名输入不合法");
        }

        // 使用Redis分布式锁防止重复提交
        String lockKey = "constellation:add:" + customerId;
        String lockValue = String.valueOf(System.currentTimeMillis());
        
        try {
            // 尝试获取锁，过期时间5秒
            boolean lockAcquired = redisService.setNx(lockKey, lockValue, 5L);
            if (!lockAcquired) {
                return Result.error("请勿重复提交");
            }

            // 验证是否已存在自己的档案
            ConstellationBaseRequest baseRequest = new ConstellationBaseRequest();
            baseRequest.setUserId(Long.parseLong(customerId));
            List<ConstellationProfileVO> constellationList = constellationService.getConstellationList(baseRequest);
            
            boolean hasSelfReference = constellationList.stream()
                    .anyMatch(constellation -> constellation.getRelationship().toValue() == 0);
            if (hasSelfReference && request.getRelationship().toValue() == 0) {
                return Result.error("关系选择有误");
            }

            ConstellationBaseRequest addRequest = new ConstellationBaseRequest();
            BeanUtils.copyProperties(request, addRequest);
            addRequest.setUserId(Long.parseLong(customerId));
            constellationService.addConstellation(addRequest);
            return Result.OK();
            
        } finally {
            // 释放锁
            redisService.delete(lockKey);
        }
    }

    /**
     * 更新星座档案
     */
    @ApiOperation("更新星座档案")
    @PostMapping("/update")
    public Result update(@RequestBody ConstellationUpdateRequest request) {
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if (Objects.isNull(userInfoVO)) {
            return Result.error("error");
        }

        // 验证姓名
        if (Objects.isNull(request.getName()) || containsEmoji(request.getName())) {
            return Result.error("真实姓名输入不合法");
        }

        ConstellationBaseRequest baseRequest = new ConstellationBaseRequest();
        baseRequest.setUserId(Long.parseLong(customerId));
        List<ConstellationProfileVO> constellationList = constellationService.getConstellationList(baseRequest);

        Optional<ConstellationProfileVO> optionalSelfConstellation = constellationList.stream()
                .filter(constellation -> constellation.getRelationship().equals(ConstellationRelationship.SELF))
                .findFirst();

        if (optionalSelfConstellation.isPresent()) {
            ConstellationProfileVO selfConstellation = optionalSelfConstellation.get();
            if (!selfConstellation.getId().equals(request.getId()) &&
                    request.getRelationship().equals(ConstellationRelationship.SELF)) {
                return Result.error(CommonErrorCode.INVALID_PROFILE_RELATIONSHIP, "关系选择有误");
            }
        }

        ConstellationBaseRequest updateRequest = new ConstellationBaseRequest();
        BeanUtils.copyProperties(request, updateRequest);
        updateRequest.setUserId(Long.parseLong(customerId));
        constellationService.updateConstellation(updateRequest);
        return Result.OK();
    }

    /**
     * 删除星座档案
     */
    @ApiOperation("删除星座档案")
    @PostMapping("/del")
    public Result del(@RequestBody ConstellationDelRequest request) {
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if (Objects.isNull(userInfoVO)) {
            return Result.error("error");
        }

        ConstellationBaseRequest delRequest = new ConstellationBaseRequest();
        delRequest.setId(request.getId());
        constellationService.delById(delRequest);
        return Result.OK();
    }

    /**
     * 星座档案列表
     */
    @ApiOperation("星座档案列表")
    @RequestMapping(value = "/getList", method = {RequestMethod.GET, RequestMethod.POST})
    public Result<ConstellationListResponse> getList(@RequestBody ConstellationListRequest request) {
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if (Objects.isNull(userInfoVO)) {
            return Result.error("error");
        }

        ConstellationBaseRequest listRequest = new ConstellationBaseRequest();
        BeanUtils.copyProperties(request, listRequest);
        listRequest.setUserId(Long.parseLong(customerId));
        List<ConstellationProfileVO> constellationList = constellationService.getConstellationList(listRequest);

        return Result.OK(ConstellationListResponse.builder()
                .constellations(constellationList)
                .build());
    }

    /**
     * 星座档案详情
     */
    @ApiOperation("星座档案详情")
    @PostMapping("/detail")
    public Result<ConstellationDetailResponse> detail(@RequestBody ConstellationDetailRequest request) {
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if (Objects.isNull(userInfoVO)) {
            return Result.error("error");
        }

        ConstellationBaseRequest detailRequest = new ConstellationBaseRequest();
        BeanUtils.copyProperties(request, detailRequest);
        detailRequest.setUserId(Long.parseLong(customerId));
        List<ConstellationProfileVO> constellationList = constellationService.getConstellationList(detailRequest);
        ConstellationProfileVO constellationVO = null;
        if (!constellationList.isEmpty()) {
            constellationVO = constellationList.get(0);
        }

        return Result.OK(ConstellationDetailResponse.builder()
                .constellation(constellationVO)
                .build());
    }

    /**
     * 更新排序
     * @param constellationProfileUpdateSortRequest
     * @return
     */
    @ApiOperation("更新排序")
    @PostMapping("/updateSort")
    public Result updateSort(@RequestBody ConstellationProfileUpdateSortRequest constellationProfileUpdateSortRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        constellationService.updateSort(constellationProfileUpdateSortRequest);
        return Result.OK();
    }

    /**
     * 关系列表
     */
    @ApiOperation("关系列表")
    @GetMapping("/getRelationship")
    public Result<RelationshipResponse> getRelationship() {
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if (Objects.isNull(userInfoVO)) {
            return Result.error("error");
        }

        ConstellationBaseRequest baseRequest = new ConstellationBaseRequest();
        baseRequest.setUserId(Long.parseLong(customerId));
        List<ConstellationProfileVO> constellationList = constellationService.getConstellationList(baseRequest);
        boolean hasSelfReference = constellationList.stream()
                .anyMatch(constellation -> constellation.getRelationship().toValue() == 0);

        List<RelationshipVO> relationshipVOList = new ArrayList<>();
        for (ConstellationRelationship value : ConstellationRelationship.values()) {
            RelationshipVO relationship = new RelationshipVO();
            relationship.setValue(value.toValue());
            String name = value.getValue();
            if(userInfoVO.getLanguageType().equals(LanguageType.ZH_HANT)){
                ZhConvertBootstrap zhConvertBootstrap = ZhConvertBootstrap.newInstance();
                name = zhConvertBootstrap.toTraditional(name);
            }
            relationship.setName(name);
            relationship.setIsSelect(DefaultFlag.YES);
            if (hasSelfReference && value.toValue() == 0) {
                relationship.setIsSelect(DefaultFlag.NO);
            }
            relationshipVOList.add(relationship);
        }

        return Result.OK(RelationshipResponse.builder()
                .relationshipS(relationshipVOList)
                .build());
    }

    @ApiOperation("获取国家/地区")
    @PostMapping("/getCountryRegion")
    public Result<RegionResponse> getCountryRegion(@RequestBody RegionRequest regionRequest){
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        List<RegionVO> countryCodeList = countryCodeService.getCountryCodeList(regionRequest);
        return Result.ok(RegionResponse.builder()
                .countryCodes(countryCodeList)
                .build());
    }

    /**
     * 检查给定的字符串是否包含 Emoji
     */
    public static boolean containsEmoji(String text) {
        String emojiRegex = "[\\ud83c[\\udf00-\\udfff]|\\ud83d[\\udc00-\\ude4f\\ude80-\\udeff]|\\u2694|\\u2600-\\u26FF\\u2702-\\u27B0]";
        Pattern pattern = Pattern.compile(emojiRegex);
        return pattern.matcher(text).find();
    }
} 