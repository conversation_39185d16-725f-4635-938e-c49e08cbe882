package com.lhx.birthday.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lhx.birthday.entity.TvMemberRechargeType;
import com.lhx.birthday.entity.UserPaySubscribe;
import com.lhx.birthday.entity.UserPaySubscribeLog;
import com.lhx.birthday.enums.SubtypeEnum;
import com.lhx.birthday.enums.WuyuanAdActionType;
import com.lhx.birthday.mapper.UserPaySubscribeLogMapper;
import com.lhx.birthday.mapper.UserPaySubscribeMapper;
import com.lhx.birthday.request.WuyuanAdRequest;
import com.lhx.birthday.request.subscribe.RestoreRequest;
import com.lhx.birthday.request.subscribe.SubscribeListRequest;
import com.lhx.birthday.request.subscribe.SubscribeRequest;
import com.lhx.birthday.service.ITvMemberRechargeTypeService;
import com.lhx.birthday.service.IUserInfoService;
import com.lhx.birthday.service.IUserPaySubscribeService;
import com.lhx.birthday.service.IWuyuanAdService;
import com.lhx.birthday.util.*;
import com.lhx.birthday.vo.Result;
import com.lhx.birthday.vo.TvMemberRechargeTypeVO;
import com.lhx.birthday.vo.UserInfoVO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.net.ssl.HttpsURLConnection;
import javax.servlet.http.HttpServletRequest;
import javax.transaction.Transactional;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintStream;
import java.net.URL;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.lhx.birthday.constant.SettingConstant.IS_VIP;
import static com.lhx.birthday.constant.SettingConstant.NOT_VIP;

/**
 * <AUTHOR> lhx
 * @date 2023/10/31 15:53
 */
@RestController
@Slf4j
@Api(description = "app接口 - 用户订阅", tags = "UserPaySubscribeController")
public class UserPaySubscribeController {

    @Autowired
    private IUserInfoService userInfoService;

    @Autowired
    private IUserPaySubscribeService userPaySubscribeService;

    @Autowired
    private ITvMemberRechargeTypeService tvMemberRechargeTypeService;

    @Autowired
    private UserPaySubscribeLogMapper userPaySubscribeLogMapper;

    @Value("${appStore.password}")
    private String password;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private IWuyuanAdService wuyuanAdService;

    @Resource
    private CommonUtil commonUtil;
    @Autowired
    private UserPaySubscribeMapper userPaySubscribeMapper;

    private String webhook = "fb2bc745-1209-4e88-a106-3836702c8785";

    /**
     * 付费订阅列表
     * @return
     */
    @PostMapping("/getSubscribeList")
    public Result<List<TvMemberRechargeTypeVO>> getSubscribeList(@RequestBody SubscribeListRequest subscribeListRequest){
        subscribeListRequest.setUserCenter(1);
        List<TvMemberRechargeTypeVO> subscribeList = tvMemberRechargeTypeService.getSubscribeList(subscribeListRequest);
        subscribeList = subscribeList.stream()
                .sorted(Comparator.comparing(TvMemberRechargeTypeVO::getOrders))
                .collect(Collectors.toList());
        return Result.OK(subscribeList);
    }

    /**
     * 付费订阅列表
     * @param subscribeListRequest
     * @return
     */
    @PostMapping("/v2/getSubscribeList")
    public Result<List<TvMemberRechargeTypeVO>> getSubscribeListV2(@RequestBody SubscribeListRequest subscribeListRequest){
        List<TvMemberRechargeTypeVO> subscribeList = tvMemberRechargeTypeService.getSubscribeList(subscribeListRequest);
        subscribeList = subscribeList.stream()
                .sorted(Comparator.comparing(TvMemberRechargeTypeVO::getOrders))
                .collect(Collectors.toList());
        return Result.OK(subscribeList);
    }


    /**
     * 连续订阅接口
     * @param subscribeRequest
     * @return
     */
    @PostMapping("/subscribe")
    @Transactional
    public Result subscribe(@RequestBody SubscribeRequest subscribeRequest, HttpServletRequest request) {
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if (Objects.isNull(userInfoVO)) {
            return Result.error("没有此用户");
        }
//        log.info("凭证："+subscribeRequest.getReceipt());
        JSONObject result = verifyReceipt("https://buy.itunes.apple.com/verifyReceipt", subscribeRequest.getReceipt());
//        log.info("凭证1："+result.toJSONString());
        if (Objects.equals(result.getInteger("status"), 21007)) {
            result = verifyReceipt("https://sandbox.itunes.apple.com/verifyReceipt", subscribeRequest.getReceipt());
        }
        log.info("user_id[{}]购买续订会员，product_id[{}]凭据内容：{}", Long.parseLong(customerId), "", result.toJSONString());
        if (Objects.equals(result.getInteger("status"), 0)) {
            String environment = result.getString("environment");
            JSONObject first = result.getJSONArray("latest_receipt_info").getJSONObject(0);
            if(first.getString("product_id").contains("forever")){
                LocalDateTime hundredYearsLater = LocalDateTime.now().plusYears(100);
                long millis = hundredYearsLater.toInstant(ZoneOffset.UTC).toEpochMilli();
                first.put("expires_date",String.valueOf(millis));
                first.put("expires_date_ms",String.valueOf(millis));
            }
            if (first.containsKey("expires_date")) {
                boolean data = addSubscribe(first, userInfoVO, environment);
                addHistory(result, userInfoVO, environment);
                // 异步请求AD推广接口
                String appleAdToken = request.getHeader("appleAdToken");
                String osType = request.getHeader("osType");
                String osVersion = request.getHeader("osVersion");
                String ipx = request.getHeader("ipx");
                String idfa = request.getHeader("idfa");
                String imei = request.getHeader("imei");
                String oaid = request.getHeader("oaid");
//                log.info("appleAdToken:"+appleAdToken);
//                log.info("osType:"+osType);
//                log.info("osVersion:"+osVersion);
//                log.info("ipx:"+ipx);
//                log.info("idfa:"+idfa);
                IfconfigUtil ifconfigUtil = new IfconfigUtil();
                String ipAddr = ifconfigUtil.getIpAddr(request);
                if(!StringUtils.isEmpty(ipx)){
                    taskExecutor.execute(() -> {
                        wuyuanAdService.trigger(WuyuanAdRequest.builder()
                                .ipAddr(ipAddr)
                                .ipAddrV6(ipx)
                                .appleAdToken(appleAdToken)
                                .osVersion(osVersion)
                                .osType(osType)
                                .idfa(idfa)
                                .imei(imei)
                                .oaid(oaid)
                                .userId(String.valueOf(userInfoVO.getUserId()))
                                .wuyuanAdActionType(WuyuanAdActionType.ORDERS)
                                .build());
                    });
                }
                // true 新购买，false 之前购买过
                return Result.ok(data);
            } else {
                return Result.error("transactionId 不正确");
            }
        } else {
            return Result.error(CommonErrorCode.FAILED);
        }
    }

    /**
     * 恢复订阅
     * @param restoreRequest
     * @return
     */
    @PostMapping("restore")
    @Transactional
    public Object restore(@RequestBody RestoreRequest restoreRequest) {
        // 获取用户id
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if (Objects.isNull(userInfoVO)) {
            return Result.error("没有此用户");
        }
        JSONObject result = verifyReceipt("https://buy.itunes.apple.com/verifyReceipt", restoreRequest.getReceipt());
        if (result.getInteger("status") == 21007) {
            result = verifyReceipt("https://sandbox.itunes.apple.com/verifyReceipt", restoreRequest.getReceipt());
        }
        log.info("user_id[{}]恢复续订会员，凭据内容：{}", Long.parseLong(customerId), result.toJSONString());
        if (result.getInteger("status") == 0) {
            String environment = result.getString("environment");
            JSONArray array = result.getJSONArray("latest_receipt_info");
            for (int i = 0; i < array.size(); i++) {
                JSONObject object = array.getJSONObject(i);
                if(object.getString("product_id").contains("forever")){
                    LocalDateTime hundredYearsLater = LocalDateTime.now().plusYears(100);
                    long millis = hundredYearsLater.toInstant(ZoneOffset.UTC).toEpochMilli();
                    object.put("expires_date",String.valueOf(millis));
                    object.put("expires_date_ms",String.valueOf(millis));
                }
            }
            JSONObject json = (JSONObject) result.getJSONArray("latest_receipt_info").stream().filter(item -> ((JSONObject) item)
                    .containsKey("expires_date")).findFirst().orElse(null);
            if (json == null) {
                return Result.error("没有可恢复的商品");
            } else {
                JSONObject first = json;
                boolean data = addSubscribe(first, userInfoVO, environment);
                addHistory(result, userInfoVO, environment);
                String expiresDateString = first.getString("expires_date_ms");
                long expiresMs = Long.parseLong(expiresDateString); // 将字符串转换为Long类型
                Instant instant = Instant.ofEpochMilli(expiresMs);
                ZonedDateTime expiresDateZoned = instant.atZone(ZoneId.systemDefault());
                LocalDateTime expiresLocalDateTime = expiresDateZoned.toLocalDateTime();
                LocalDateTime nowLocalDateTime = LocalDateTime.now();
                if (expiresLocalDateTime.isAfter(nowLocalDateTime)) {
//                if (new Date(first.getString("expires_date_ms")).after(new Date())) {
                    return Result.ok(data);
                } else {
                    return Result.error("会员资格已过期");
                }
            }
        } else {
            return Result.error("恢复失败");
        }
    }

    /**
     * 回调
     * @param requestBody
     * @return
     */
    @PostMapping("notify")
    @Transactional
    public Object notify(@RequestBody String requestBody) {
//        log.info("苹果订阅回调:"+requestBody);
        String encryptedPayload = requestBody;
        JSONObject ddd = JSONObject.parseObject(encryptedPayload);

        String[] parts = ddd.getString("signedPayload").split("\\.");
        if (parts.length != 3) {
            throw new IllegalArgumentException("Invalid signedPayload format");
        }
        String base64 = PayloadDecryptor.getFromBase64(parts[1]);
        base64 = base64.substring(base64.indexOf("{"), base64.lastIndexOf("}") + 1);
        JSONObject jsonObject = JSONObject.parseObject(base64);
        String fromBASE641 = jsonObject.getJSONObject("data").getString("signedTransactionInfo");
        String verify = PayloadDecryptor.verify(fromBASE641);
        fromBASE641 = PayloadDecryptor.getFromBase64(verify);
        fromBASE641 = fromBASE641.substring(fromBASE641.indexOf("{"), fromBASE641.lastIndexOf("}") + 1);

        //第一标识
        String notificationType = jsonObject.get("notificationType").toString();
        //第二标识
        String subtype = String.valueOf(Optional.ofNullable(jsonObject.get("subtype")).orElse(""));
        log.info(notificationType);
        log.info(subtype);

        //用户关闭续订  没做其他操作，具体用户会员过期是通过定时任务执行的
        if(SubtypeEnum.DID_CHANGE_RENEWAL_STATUS.code.equals(notificationType) && SubtypeEnum.AUTO_RENEW_DISABLED.code.equals(subtype)){
            log.info("用户关闭自动续订。");
        }
        //用户开启续订
        if(SubtypeEnum.DID_CHANGE_RENEWAL_STATUS.code.equals(notificationType) && SubtypeEnum.AUTO_RENEW_ENABLED.code.equals(subtype)){
            log.info("用户开启自动续订。");
        }
        //用户续订逻辑
        //if (SubtypeEnum.DID_RENEW.code.equals(notificationType) || (SubtypeEnum.SUBSCRIBED.code.equals(notificationType) && SubtypeEnum.DOWNGRADE.code.equals(subtype))) {
        if (SubtypeEnum.DID_RENEW.code.equals(notificationType) && StringUtils.isBlank(subtype) ) {
            log.info("用户续订逻辑......................................");
            JSONObject convertedJson = camelToUnderscore(JSONObject.parseObject(fromBASE641));
            convertedJson.put("expires_date_ms",convertedJson.getString("expires_date"));
            convertedJson.put("original_purchase_date_ms",convertedJson.getString("original_purchase_date"));
            convertedJson.put("purchase_date_ms",convertedJson.getString("purchase_date"));
            String originalTransactionId = convertedJson.getString("original_transaction_id");
            UserPaySubscribe userPaySubscribe = userPaySubscribeMapper.findByOriginalTransactionId(originalTransactionId);
            UserInfoVO userInfoVO = userInfoService.getByUserId(userPaySubscribe.getUserId());
            addSubscribe(convertedJson, userInfoVO, convertedJson.getString("environment"));
            addHistory(convertedJson, userInfoVO, convertedJson.getString("environment"));
        }
        //订阅升级 应该是用户操作购买然后升级  购买的月卡，然后升级成年卡，按照文档说明是会发生部分退款，
        if ((SubtypeEnum.DID_CHANGE_RENEWAL_PREF.code.equals(notificationType) && SubtypeEnum.DOWNGRADE.code.equals(subtype))) {
            log.info("用户升级/降级逻辑......................................");
        }
        //用户发起退款  需要将用户详细数据 给到苹果那边 时间在12小时还是24小时内忘记了。。。
        if (SubtypeEnum.CONSUMPTION_REQUEST.code.equals(notificationType)) {
            log.info("用户发起退款逻辑......................................");
        }
        // 记录日志
        /**
         * jsonObject: 原始base64解密出的订单数据
         * requestBody：原始回调数据
         * fromBASE641：订阅数据
         */
        JSONObject parsed = JSONObject.parseObject(fromBASE641);
        String originalTransactionId = parsed.getString("originalTransactionId");
        UserPaySubscribe userPaySubscribe = userPaySubscribeMapper.findByOriginalTransactionId(originalTransactionId);
        userPaySubscribeLogMapper.saveAndFlush(UserPaySubscribeLog.builder()
                        .notificationType(notificationType)
                        .subtype(subtype)
                        .userId(userPaySubscribe.getUserId())
                        .createDate(LocalDateTime.now())
                .build());
        return Result.OK();
    }

    private JSONObject verifyReceipt(String url, String receipt) {
        try {
            HttpsURLConnection connection = (HttpsURLConnection) new URL(url).openConnection();
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setAllowUserInteraction(false);
            PrintStream ps = new PrintStream(connection.getOutputStream());
            ps.print("{\"receipt-data\": \"" + receipt + "\", \"password\": \""+password+"\"}");
            ps.close();
            BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            StringBuilder sb = new StringBuilder();
            String str;
            while ((str = br.readLine()) != null) {
                sb.append(str);
            }
            br.close();
            String resultStr = sb.toString();
            return JSONObject.parseObject(resultStr);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private void addHistory(JSONObject jsonObject, UserInfoVO user, String environment) {
        if ("Production".equals(environment)) {
            if(jsonObject.containsKey("receipt")){
                jsonObject.getJSONObject("receipt").getJSONArray("in_app").forEach(item -> {
                    JSONObject json = (JSONObject) item;
                    String transactionId = json.getString("transaction_id");
                    if (json.containsKey("expires_date")) {
                        UserPaySubscribe userPayModel = userPaySubscribeService.getByTransactionId(transactionId);
                        if (userPayModel == null) {
                            UserPaySubscribe model = paySubscribeModel(json, user.getUserId());
                            userPaySubscribeService.addUserPaySubscribe(model);
                        }
                    }
                });
            }else{
                String transactionId = jsonObject.getString("transaction_id");
                if (jsonObject.containsKey("expires_date")) {
                    UserPaySubscribe userPayModel = userPaySubscribeService.getByTransactionId(transactionId);
                    if (userPayModel == null) {
                        UserPaySubscribe model = paySubscribeModel(jsonObject, user.getUserId());
                        userPaySubscribeService.addUserPaySubscribe(model);
                    }
                }
            }
        }
    }

    private boolean addSubscribe(JSONObject first, UserInfoVO user, String environment) {
        UserPaySubscribe userPaySubscribe = userPaySubscribeService.getByTransactionId(first.getString("transaction_id"));
        String productId = first.getString("product_id");
        TvMemberRechargeType tvMemberRechargeType = tvMemberRechargeTypeService.getByProductId(productId);
        user.setVipExpiryDate(LocalDateTime.ofInstant(Instant.ofEpochMilli(first.getLong("expires_date_ms")), ZoneId.systemDefault()));
        user.setVipProductId(tvMemberRechargeType != null ? tvMemberRechargeType.getCode() : null);
        // 判断是否需要更新兑换时长
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime rewardVipBeginDate = user.getRewardVipBeginDate();
        if (rewardVipBeginDate == null) {
            rewardVipBeginDate = now;
        }
        if(rewardVipBeginDate.isAfter(now)){
            Duration durationUntilBeginDate = Duration.between(now, rewardVipBeginDate);
            // 获取相差的分钟数
            long minutesUntilBeginDate = durationUntilBeginDate.toMinutes();
            user.setRewardVipBeginDate(user.getVipExpiryDate().plusMinutes(minutesUntilBeginDate));
            // 更新用户信息
            userInfoService.updateRewardVipBeginDate(user.getUserId(),user.getRewardVipBeginDate());
        }
        Integer vip = IS_VIP;
        if(user.getVipExpiryDate().isBefore(LocalDateTime.now())){
            vip = NOT_VIP;
        }
        userInfoService.updateVipExpireDate(user.getUserId(), user.getVipExpiryDate() ,user.getVipProductId(),vip);
        // 判断试用逻辑
        assert tvMemberRechargeType != null;
        if(tvMemberRechargeType.getAppleFreeDay()>0){
            userInfoService.updateFreeUse(user.getUserId());
        }
        if (userPaySubscribe == null) {
            if ("Production".equals(environment)) {
                UserPaySubscribe model = paySubscribeModel(first, user.getUserId());
                userPaySubscribeService.addUserPaySubscribe(model);
                try {
                    String str = "续订";
                    if(Objects.nonNull(model.getIsTrialPeriod())){
                        str = "新购";
                    }
                    FeishuBotClient.sendMessage(webhook, "星愿助手用户:"+ model.getUserId()+str+"会员:" + model.getProductId());
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            return true;
        } else {
            if (!Objects.equals(userPaySubscribe.getUserId(), user.getUserId())) {
                UserInfoVO oriUser = userInfoService.getByUserId(userPaySubscribe.getUserId());
                if (oriUser != null) {
                    oriUser.setVipExpiryDate(null);
                    oriUser.setVipProductId(null);
                    userInfoService.updateVipExpireDate(oriUser.getUserId(), oriUser.getVipExpiryDate(), oriUser.getVipProductId(),NOT_VIP);
                }
                userPaySubscribe.setUserId(user.getUserId());
                userPaySubscribeService.updateUserIdById(userPaySubscribe.getId(),userPaySubscribe.getUserId());
            }
            return false;
        }
    }

    private UserPaySubscribe paySubscribeModel(JSONObject item, Long userId) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        return UserPaySubscribe.builder()
                .userId(userId)
                .expiresDate(LocalDateTime.ofInstant(Instant.ofEpochMilli(item.getLong("expires_date_ms")), ZoneId.systemDefault()).format(formatter))
                .expiresDateMs(item.getString("expires_date_ms"))
                .inAppOwnershipType(item.getString("in_app_ownership_type"))
                .isInIntroOfferPeriod(item.getString("is_in_intro_offer_period"))
                .isTrialPeriod(item.getString("is_trial_period"))
                .originalPurchaseDate(LocalDateTime.ofInstant(Instant.ofEpochMilli(item.getLong("original_purchase_date_ms")), ZoneId.systemDefault()).format(formatter))
                .originalPurchaseDateMs(item.getString("original_purchase_date_ms"))
                .originalTransactionId(item.getString("original_transaction_id"))
                .productId(item.getString("product_id"))
                .purchaseDate(LocalDateTime.ofInstant(Instant.ofEpochMilli(item.getLong("purchase_date_ms")), ZoneId.systemDefault()).format(formatter))
                .purchaseDateMs(item.getString("purchase_date_ms"))
                .quantity(item.getString("quantity"))
                .subscriptionGroupIdentifier(item.getString("subscription_group_identifier"))
                .transactionId(item.getString("transaction_id"))
                .webOrderLineItemId(item.getString("web_order_line_item_id"))
                .createDate(LocalDateTime.now())
                .build();
    }

    public static JSONObject camelToUnderscore(JSONObject json) {
        if (json == null) {
            return null;
        }

        JSONObject newJson = new JSONObject();
        for(Map.Entry<String, Object> entry : json.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            String newKey = camelToUnderscoreKey(key);
            newJson.put(newKey, convertValue(value));
        }
        return newJson;
    }

    private static String camelToUnderscoreKey(String key) {
        StringBuilder underscoreKey = new StringBuilder();
        for (int i = 0; i < key.length(); i++) {
            char c = key.charAt(i);
            if (Character.isUpperCase(c)) {
                if (i > 0) {
                    underscoreKey.append('_');
                }
                underscoreKey.append(Character.toLowerCase(c));
            } else {
                underscoreKey.append(c);
            }
        }
        return underscoreKey.toString();
    }

    private static Object convertValue(Object value) {
        if (value instanceof JSONObject) {
            return camelToUnderscore((JSONObject) value);
        } else if (value instanceof JSONArray) {
            JSONArray array = (JSONArray) value;
            JSONArray newArray = new JSONArray();
            for (Object item : array) {
                newArray.add(convertValue(item));
            }
            return newArray;
        }
        return value;
    }

}
