package com.lhx.birthday.util;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.lhx.birthday.constant.ApiConstant;
import com.lhx.birthday.enums.DefaultFlag;
import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Collections;
import java.util.Enumeration;
import java.util.Objects;

public class AdUtil {

    private static String url = "https://api-adservices.apple.com/api/v1/";

//    public static void main(String[] args) {
//        String token = "xQkA0Qqc0s+7EQnjbJJM8Ez/cDM+98US5EtwNktulSMFhWfKsrl3QAKrcDnwyzNQ9U/hL3AXcAkoU6bXSok0lydrX4HIuAVnFAAAAVADAAAA/QAAAIDkyzBvAdR8WCdEvgauxltW3Uygb/aNQB6KZhlAu0ZN7pU4INT6+Aw0Hyo5+Rbca1yy14stLQGrqHCaiASYdGU7FKhwQkBIZY/U77xWx+I/zkGFTDT/0v4sh32LSXVvBX5rJtCLY25Z6vEZuPSrVLdTP1tq9Wsaisth7sjzOpqf6wAAABnGIUyEfZz29x3qPbQgQ4tX3Fz9uRE+j7vKAAAAnwEZ+0C6cZ/DlIXRAnD3oN3hc2Sm4AAAAIYEAPkwnLUIhJqwrSn8SBqBE51mpzvo2s1vps5ixMOIDTUlOSnXj3Ds7XhloEdY8zCXPozaR3lMPci5uWG0X6cY+0Dsqi8qIFBXU2imIMUL82Q4pQ+gjoEt9cPzpXAxE9feZCcPlZPEC4PERikEhYRU2B50s4WEWXsZkmGQ0tKqCg3ubwj7UgAAAAAAAAABBEcRAAA=";
//        HttpResponse response = sendPostRequest(url, token);
//        // 读取响应结果
//        String result = response.body();
//        System.out.println(result);
//        /**
//         * {
//         *   "attribution": true, 是否是ASA来的
//         *   "orgId": 40669820, 广告系列主ID
//         *   "campaignId": 542370539, 广告系列ID
//         *   "conversionType": "Download", 新老下载 ReDownload
//         *   "clickDate": "2020-04-08T17:17Z", 点击时间
//         *   "adGroupId": 542317095, 广告主ID
//         *   "countryOrRegion": "US", 地区
//         *   "keywordId": 87675432, // 关键词ID 可能为空
//         *   "adId": 542317136  // 广告对象与广告组之间分配关系的标识符 可能为空
//         * }
//         */
//
//        // 关闭连接
//        response.close();
//    }

    private static HttpResponse sendPostRequest(String url, String raw) {
        // 发送POST请求并获取响应
        HttpResponse response = HttpRequest.post(url)
                .header("Content-Type", "text/plain")
                .body(raw)
                .timeout(5000)
                .execute();

        if (response.getStatus() != 200) {
            // 如果不是200 OK状态，可以在这里处理异常情况
            throw new RuntimeException("请求失败，HTTP状态码：" + response.getStatus());
        }

        return response;
    }

}
