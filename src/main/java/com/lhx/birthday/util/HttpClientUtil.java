package com.lhx.birthday.util;

import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;

/**
 * 基于OkHttp的HTTP客户端工具类
 * 提供更高效的HTTP请求实现，特别是对流式API的支持
 */
@Slf4j
@Component
public class HttpClientUtil {

    private static final OkHttpClient CLIENT = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build();

    /**
     * 创建自定义超时设置的客户端
     * @param connectTimeoutSeconds 连接超时（秒）
     * @param readTimeoutSeconds 读取超时（秒）
     * @return 定制的OkHttpClient实例
     */
    public static OkHttpClient createClient(int connectTimeoutSeconds, int readTimeoutSeconds) {
        return CLIENT.newBuilder()
                .connectTimeout(connectTimeoutSeconds, TimeUnit.SECONDS)
                .readTimeout(readTimeoutSeconds, TimeUnit.SECONDS)
                .build();
    }

    /**
     * 执行POST请求，支持流式响应处理
     * @param url 请求URL
     * @param jsonBody 请求体（JSON格式）
     * @param headers 请求头
     * @param lineCallback 行处理回调
     * @param connectTimeout 连接超时（秒）
     * @param readTimeout 读取超时（秒）
     * @throws IOException 请求异常
     */
    public static void postJsonStream(String url, String jsonBody, Map<String, String> headers, 
                                     Consumer<String> lineCallback,
                                     int connectTimeout, int readTimeout) throws IOException {
        OkHttpClient client = createClient(connectTimeout, readTimeout);
        
        // 构建请求体
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(jsonBody, mediaType);
        
        // 构建请求
        Request.Builder requestBuilder = new Request.Builder().url(url).post(body);
        
        // 添加请求头
        if (headers != null) {
            headers.forEach(requestBuilder::addHeader);
        }
        
        // 执行请求
        long startTime = System.currentTimeMillis();
        AtomicBoolean isClientClosed = new AtomicBoolean(false);
        
        try (Response response = client.newCall(requestBuilder.build()).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("请求失败: " + response.code() + " " + response.message());
            }
            
            ResponseBody responseBody = response.body();
            if (responseBody == null) {
                throw new IOException("响应体为空");
            }
            
            // 使用BufferedReader处理流式响应
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(responseBody.byteStream()), 16384)) {
                String line;
                long lineStartTime = System.currentTimeMillis();
                int emptyLineCount = 0;
                int consecutiveErrorCount = 0;
                
                while (!isClientClosed.get() && (line = reader.readLine()) != null) {
                    long now = System.currentTimeMillis();
                    long lineTime = now - lineStartTime;
                    
                    // 如果超过60秒没有数据，可能是流已经结束但没有正确关闭
                    if (lineTime > 60000) {
                        log.warn("长时间未收到数据，可能流已结束: {} ms", lineTime);
                        break;
                    }
                    
                    if (lineTime > 1000) {
                        log.warn("读取单行数据耗时较长: {} ms", lineTime);
                    }
                    lineStartTime = now;
                    
                    // 处理空行，防止无限循环
                    if (line.trim().isEmpty()) {
                        emptyLineCount++;
                        if (emptyLineCount > 10) {
                            log.warn("收到过多连续空行，退出处理");
                            break;
                        }
                        continue;
                    }
                    emptyLineCount = 0;
                    
                    if (line.startsWith("data:")) {
                        String data = line.substring(5).trim();
                        if (!"[DONE]".equals(data)) {
                            try {
                            lineCallback.accept(data);
                                consecutiveErrorCount = 0; // 重置错误计数
                            } catch (Exception e) {
                                consecutiveErrorCount++;
                                log.error("处理回调异常: {}", e.getMessage());
                                
                                // 如果连续出现5次回调异常，可能是客户端已断开连接
                                if (consecutiveErrorCount >= 5) {
                                    log.warn("连续回调异常达到阈值，终止流处理");
                                    isClientClosed.set(true);
                                    break;
                                }
                            }
                        } else {
                            log.info("收到流结束标志 [DONE]");
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("流式请求处理异常: {}", e.getMessage());
            throw e;
        } finally {
            long totalTime = System.currentTimeMillis() - startTime;
            log.debug("流式请求总耗时: {} ms", totalTime);
        }
    }
    
    /**
     * 执行DeepSeek API的流式请求
     * @param url API URL
     * @param apiKey API密钥
     * @param requestBody 请求体JSON字符串
     * @param dataCallback 数据处理回调
     * @throws IOException 请求异常
     */
    public static void streamDeepSeekAPI(String url, String apiKey, String requestBody, 
                                       Consumer<String> dataCallback) throws IOException {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + apiKey);
        
        final int[] chunkCount = {0};
        final long startTime = System.currentTimeMillis();
        final StringBuilder fullResponse = new StringBuilder();
        
        try {
        postJsonStream(url, requestBody, headers, data -> {
            try {
                chunkCount[0]++;
                    fullResponse.append(data).append("\n");
                dataCallback.accept(data);
            } catch (Exception e) {
                    log.error("处理DeepSeek API响应数据异常: {}", e.getMessage());
            }
            }, 10, 120); // 增加超时时间：10秒连接超时，120秒读取超时
        
        log.info("DeepSeek API流处理完成，共接收 {} 个数据块", chunkCount[0]);
        } catch (Exception e) {
            log.error("DeepSeek API流处理异常: {}", e.getMessage());
            
            // 如果已经接收了部分数据，仍然认为是部分成功
            if (chunkCount[0] > 0) {
                log.info("DeepSeek API流处理部分完成，已接收 {} 个数据块", chunkCount[0]);
            } else {
                throw e; // 如果没有接收到任何数据，则重新抛出异常
            }
        }
    }
} 