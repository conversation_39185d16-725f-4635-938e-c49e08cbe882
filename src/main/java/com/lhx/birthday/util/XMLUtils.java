package com.lhx.birthday.util;

import com.sun.org.apache.xpath.internal.XPathAPI;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.transform.TransformerException;

public class XMLUtils {
    /**
     * 获取指定路径下的子元素文本内容
     *
     * @param parent 父元素
     * @param childPath 子元素路径（例如："origin/time/value"）
     * @return 子元素文本内容
     */
    public static String getChildTextContent(Element parent, String childPath) throws TransformerException {
        NodeList nodeList = XPathAPI.selectNodeList(parent, childPath);
        if (nodeList.getLength() > 0) {
            Node node = nodeList.item(0);
            if (node.getNodeType() == Node.ELEMENT_NODE || node.getNodeType() == Node.TEXT_NODE) {
                return node.getTextContent();
            }
        }
        return null;
    }

    /**
     * 获取指定路径下的子元素属性值
     *
     * @param parent 父元素
     * @param childPath 子元素路径（例如："creationInfo"）
     * @param attributeName 属性名称（例如："agencyURI"）
     * @return 子元素的属性值
     */
    public static String getChildAttribute(Element parent, String childPath, String attributeName) {
        Element childElement = getChildElement(parent, childPath);
        if (childElement != null) {
            return childElement.getAttribute(attributeName);
        }
        return null;
    }

    /**
     * 获取指定路径下的子元素对象
     *
     * @param parent 父元素
     * @param childPath 子元素路径（例如："creationInfo"）
     * @return 子元素对象，若未找到则返回null
     */
    private static Element getChildElement(Element parent, String childPath) {
        String[] pathParts = childPath.split("/");
        Element currentElement = parent;
        for (String part : pathParts) {
            NodeList children = currentElement.getElementsByTagName(part);
            if (children.getLength() > 0) {
                currentElement = (Element) children.item(0);
            } else {
                return null;
            }
        }
        return currentElement;
    }
}
