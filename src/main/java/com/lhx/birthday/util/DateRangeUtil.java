package com.lhx.birthday.util;

import lombok.Data;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;

public class DateRangeUtil {

    @Data
    public static class FutureDateRange {
        private LocalDate startDate;
        private LocalDate endDate;
        private int totalDays;

        public FutureDateRange(LocalDate startDate, LocalDate endDate, int totalDays) {
            this.startDate = startDate;
            this.endDate = endDate;
            this.totalDays = totalDays;
        }

        // getter and setter methods...
    }

    public static FutureDateRange getFutureDateRange(Integer future) {
        LocalDate currentDate = LocalDate.now();
        LocalDate startDate;
        LocalDate endDate;
        int totalDays;

        switch (future) {
            case 0:
                totalDays = 7;
                startDate = currentDate;
                endDate = currentDate.plusDays(totalDays - 1);
                break;
            case 1:
                totalDays = 15;
                startDate = currentDate;
                endDate = currentDate.plusDays(totalDays - 1);
                break;
            case 2:
                totalDays = 30; // Assuming a month is considered to be 30 days
                startDate = currentDate;
                endDate = currentDate.plusMonths(1).minusDays(1);
                break;
            case 3:
                totalDays = 90;
                startDate = currentDate;
                endDate = currentDate.plusMonths(3).minusDays(1);
                break;
            default:
                throw new IllegalArgumentException("Invalid future time range option");
        }

        return new FutureDateRange(startDate, endDate, totalDays);
    }

}
