package com.lhx.birthday.util;

import java.util.*;

public class UniqueRandomNumbers {
    private static final Random random = new Random();

    public static String generateUniqueRandomString() {
        // 创建包含1-22的列表并打乱
        List<Integer> numberList = new ArrayList<>();
        for (int i = 1; i <= 22; i++) {
            numberList.add(i);
        }
        Collections.shuffle(numberList, random);

        // 取前19个元素，并构建字符串
        StringBuilder resultBuilder = new StringBuilder();
        for (int i = 0; i < 19; i++) {
            if (i > 0) {
                resultBuilder.append(",");
            }
            resultBuilder.append(numberList.get(i));
        }

        return resultBuilder.toString();
    }

    public static int[] generateUniqueRandomNumbers() {
        // 创建包含1-22的列表
        List<Integer> numberList = new ArrayList<>();
        for (int i = 1; i <= 22; i++) {
            numberList.add(i);
        }

        // 打乱列表以确保随机性
        Collections.shuffle(numberList, random);

        // 取前19个元素作为结果数组
        int[] resultArray = new int[19];
        for (int i = 0; i < 19; i++) {
            resultArray[i] = numberList.get(i);
        }

        return resultArray;
    }

    // 测试方法
    public static void main(String[] args) {
        ;
        System.out.println("Generated array of 19 unique random numbers between 1 and 22:");
        System.out.println( generateUniqueRandomString());
    }
}
