package com.lhx.birthday.util.birthday;

public class XiaoLiuRenCalculator {

    public static long calculateXiaoLiuRenRemainder(long number) {
        long org = number;
        // 数字相加
        long sum = 0;
        while (number > 0) {
            sum += number % 10;
            number /= 10;
        }

        // 根据位数做减法
        if (String.valueOf(org).length() == 4) {
            sum -= 3;
        } else if (String.valueOf(org).length() == 3) {
            sum -= 2;
        }

        // 求余数
        long remainder = sum % 6;

        if(remainder==0){
            remainder = 6;
        }
        // 返回结果
        return remainder;
    }

    public static void main(String[] args) {
        // 示例：使用数字1236计算小六壬余数
        long numberExample = 1436;
        long result = calculateXiaoLiuRenRemainder(numberExample);
        System.out.println("小六壬计算得到的余数是：" + result);
    }
}