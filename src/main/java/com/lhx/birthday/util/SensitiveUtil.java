package com.lhx.birthday.util;

import cn.hutool.core.text.CharSequenceUtil;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/12/2
 */
public class SensitiveUtil {
    private SensitiveUtil() {
    }

    public static String phone(String str) {
        if (CharSequenceUtil.isBlank(str)) {
            return "";
        }
        if (str.length() > 7) {
            return str.substring(0, 3) + "****" + str.substring(7, str.length());
        } else {
            return str;
        }
    }

    public static String uuid(String str) {
        if (CharSequenceUtil.isBlank(str)) {
            return "";
        }

        if (str.length() > 6) {
            return str.substring(0, str.length() - 6) + "******";
        } else {
            return str;
        }
    }

}
