package com.lhx.birthday.util;

/**
 * <AUTHOR> lhx
 * @date 2023/10/23 15:15
 */
public class DistanceCalculator {
    public static int calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        // 将经纬度转换为弧度
        double lat1Rad = Math.toRadians(lat1);
        double lon1Rad = Math.toRadians(lon1);
        double lat2Rad = Math.toRadians(lat2);
        double lon2Rad = Math.toRadians(lon2);

        // 使用Haversine公式计算距离
        double dlon = lon2Rad - lon1Rad;
        double dlat = lat2Rad - lat1Rad;
        double a = Math.pow(Math.sin(dlat/2), 2) + Math.cos(lat1Rad) * Math.cos(lat2Rad) * Math.pow(Math.sin(dlon/2), 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        double distance = 6371 * c;  // 地球半径为6371公里

        return (int) (Math.round(distance * 100) / 100);
    }

    public static void main(String[] args) {
        double lat1 = 40.7128;  // 纽约市的纬度
        double lon1 = -74.0060;  // 纽约市的经度
        double lat2 = 34.0522;  // 洛杉矶的纬度
        double lon2 = -118.2437;  // 洛杉矶的经度

        double distance = calculateDistance(lat1, lon1, lat2, lon2);
        System.out.println("距离：" + distance + "公里");
    }
}
