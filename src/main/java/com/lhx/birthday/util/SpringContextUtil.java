package com.lhx.birthday.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @desc 随时随地获取spring bean和环境的工具类
 * @date 2023-11-25
 */
@Slf4j
@Component
public class SpringContextUtil implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringContextUtil.applicationContext = applicationContext;
    }

    /**
     * @return
     */
    public static Environment getEnvironment() {
        return applicationContext.getEnvironment();
    }

    public static String getConfigValue(String key) {
        return getEnvironment().getProperty(key);
    }

    public static Integer getConfigInteger(String key) {
        return getEnvironment().getProperty(key, Integer.class);
    }

    public static Long getConfigLong(String key) {
        return getEnvironment().getProperty(key, Long.class);
    }

    public static Double getConfigDouble(String key) {
        return getEnvironment().getProperty(key, Double.class);
    }

    public static final String ENV_KEY_ACTIVE_PROFILE = "spring.profiles.active";

    public static String getActiveProfile() {
        return getConfigValue(ENV_KEY_ACTIVE_PROFILE);
    }

    public static boolean isDev() {
        return "dev".equals(getActiveProfile());
    }

    public static boolean isTest() {
        return "test".equals(getActiveProfile());
    }

    public static boolean isProd() {
        return "prod".equals(getActiveProfile());
    }

    public static boolean isPrev() {
        return "prev".equals(getActiveProfile());
    }

    /**
     * @return
     */
    public static ApplicationContext getContext() {
        return applicationContext;
    }

    /**
     * @param cls
     * @return
     */
    public static <T> T getBean(Class<T> cls) {
        if (applicationContext != null) {
            return applicationContext.getBean(cls);
        }
        return null;
    }

    /**
     * 根据bean名称获取bean
     *
     * @param name
     * @return
     */
    public static Object getBean(String name) {
        if (applicationContext != null) {
            return applicationContext.getBean(name);
        }
        return null;
    }

    /**
     * 获取 HttpServletRequest
     *
     * @return
     */
    public static HttpServletRequest getHttpServletRequest() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes != null) {
            return ((ServletRequestAttributes) requestAttributes).getRequest();
        }
        return null;
    }
}
