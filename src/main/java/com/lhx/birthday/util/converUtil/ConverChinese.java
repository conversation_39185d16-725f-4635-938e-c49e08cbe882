package com.lhx.birthday.util.converUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.houbb.opencc4j.core.impl.ZhConvertBootstrap;

public class ConverChinese {

    public static JSONObject convertValuesToTraditionalChinese(String jsonString) {
        ZhConvertBootstrap zhConvertBootstrap = ZhConvertBootstrap.newInstance();
        JSONObject jsonObject = JSON.parseObject(jsonString);
        convert(jsonObject, zhConvertBootstrap);
        return jsonObject;
    }

    public static JSONObject convertValuesToTraditionalChineseJson(JSONObject jsonObject) {
        ZhConvertBootstrap zhConvertBootstrap = ZhConvertBootstrap.newInstance();
        convert(jsonObject, zhConvertBootstrap);
        return jsonObject;
    }

    private static void convert(JSONObject jsonObject, ZhConvertBootstrap zhConvertBootstrap) {
        for (String key : jsonObject.keySet()) {
            Object value = jsonObject.get(key);
            if (value instanceof JSONObject) {
                convert((JSONObject) value, zhConvertBootstrap);
            } else if (value instanceof JSONArray) {
                convert((JSONArray) value, zhConvertBootstrap);
            } else if (value instanceof String) {
                String traditionalCn = zhConvertBootstrap.toTraditional((String) value);
                jsonObject.put(key, traditionalCn);
            }
        }
    }

    private static void convert(JSONArray jsonArray, ZhConvertBootstrap zhConvertBootstrap) {
        for (int i = 0; i < jsonArray.size(); i++) {
            Object value = jsonArray.get(i);
            if (value instanceof JSONObject) {
                convert((JSONObject) value, zhConvertBootstrap);
            } else if (value instanceof String) {
                String traditionalCn = zhConvertBootstrap.toTraditional((String) value);
                jsonArray.set(i, traditionalCn);
            }
        }
    }

}
