package com.lhx.birthday.util.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户信息
 * Created by ji<PERSON><PERSON> on 27/3/2017.
 */
@ApiModel
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Operator implements Serializable {

    private static final long serialVersionUID = 8889187242758862859L;


    @ApiModelProperty(value = "操作人")
    private String name;

    @ApiModelProperty(value = "管理员Id")
    private String adminId;

    @ApiModelProperty(value = "用户Id")
    private String userId;

    @ApiModelProperty(value = "操作所在的Ip地址")
    private String ip;

    /**
     * 操作人账号
     */
    @ApiModelProperty(value = "操作人账号")
    private String account;


    /**
     * 是否首次登陆
     */
    @ApiModelProperty("是否首次登陆")
    private Boolean firstLogin;


    /**
     * 用户的登陆的terminal token
     */
    @ApiModelProperty("terminal token")
    private String terminalToken;
}
