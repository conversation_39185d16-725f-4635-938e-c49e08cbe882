package com.lhx.birthday.util;

import com.lhx.birthday.config.RedisCompressConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.Base64;
import java.util.concurrent.atomic.AtomicLong;
import java.util.zip.Deflater;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * Redis数据压缩工具类
 * 用于减少Redis网络传输量和内存占用
 */
@Component
public class RedisCompressUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(RedisCompressUtil.class);

    @Autowired
    private RedisCompressConfig config;

    // 压缩统计信息
    private static final AtomicLong totalCompressed = new AtomicLong(0);
    private static final AtomicLong totalUncompressed = new AtomicLong(0);
    private static final AtomicLong totalSaved = new AtomicLong(0);

    // 静态配置，从配置类中注入
    private static boolean enabled = true;
    private static int threshold = 8192;
    private static int level = 6;
    private static boolean logStats = false;

    @PostConstruct
    public void init() {
        enabled = config.isEnabled();
        threshold = config.getThreshold();
        level = config.getLevel();
        logStats = config.isLogStats();

        LOGGER.info("Redis压缩工具初始化完成，压缩已{}，阈值: {}字节，压缩级别: {}",
                enabled ? "启用" : "禁用", threshold, level);
    }

    /**
     * 压缩字符串数据
     * 对于小于阈值的数据不进行压缩，以避免压缩开销超过收益
     *
     * @param str 原始字符串
     * @return 压缩后的Base64编码字符串，如果原始字符串较小则返回原始字符串
     */
    public static String compress(String str) {
        if (str == null || str.isEmpty() || !enabled) {
            return str;
        }

        // 小于阈值的字符串不压缩，避免压缩反而增加开销
        if (str.length() < threshold) {
            return str;
        }

        try {
            long startTime = System.currentTimeMillis();
            byte[] inputBytes = str.getBytes("UTF-8");
            int originalSize = inputBytes.length;

            ByteArrayOutputStream out = new ByteArrayOutputStream();
            GZIPOutputStream gzip = new GZIPOutputStream(out) {
                {
                    // 设置压缩级别
                    def.setLevel(level);
                }
            };
            gzip.write(inputBytes);
            gzip.close();

            byte[] compressedBytes = out.toByteArray();
            int compressedSize = compressedBytes.length;

            // 如果压缩后体积反而增大，则返回原始字符串
            if (compressedSize >= originalSize) {
                if (logStats) {
                    LOGGER.debug("压缩无效: 原始大小 {} 字节, 压缩后 {} 字节", originalSize, compressedSize);
                }
                return str;
            }

            // 使用Base64编码压缩后的二进制数据
            String compressedStr = Base64.getEncoder().encodeToString(compressedBytes);

            // 更新统计信息
            if (logStats) {
                long endTime = System.currentTimeMillis();
                totalCompressed.incrementAndGet();
                totalUncompressed.addAndGet(originalSize);
                totalSaved.addAndGet(originalSize - compressedSize);

                LOGGER.debug("压缩成功: 原始大小 {} 字节, 压缩后 {} 字节, 节省 {} 字节 ({}%), 耗时 {} ms",
                        originalSize, compressedSize, originalSize - compressedSize,
                        (originalSize - compressedSize) * 100 / originalSize,
                        endTime - startTime);

                // 每100次压缩输出一次统计信息
                if (totalCompressed.get() % 100 == 0) {
                    logCompressionStats();
                }
            }

            // 添加标记前缀，用于解压时识别
            return "GZIP:" + compressedStr;
        } catch (Exception e) {
            LOGGER.warn("压缩数据失败: {}", e.getMessage());
            // 压缩失败时返回原始字符串
            return str;
        }
    }

    /**
     * 解压缩字符串数据
     *
     * @param str 可能被压缩的字符串
     * @return 解压缩后的原始字符串
     */
    public static String decompress(String str) {
        if (str == null || str.isEmpty() || !enabled) {
            return str;
        }

        // 检查是否为压缩数据
        if (!str.startsWith("GZIP:")) {
            return str;
        }

        long startTime = System.currentTimeMillis();
        try {
            // 移除标记前缀
            String compressedStr = str.substring(5);
            long prefixTime = System.currentTimeMillis();
            LOGGER.debug("解压缩-移除前缀耗时: {} ms", prefixTime - startTime);

            // Base64解码
            byte[] compressed = Base64.getDecoder().decode(compressedStr);
            long decodeTime = System.currentTimeMillis();
            LOGGER.debug("解压缩-Base64解码耗时: {} ms, 解码后大小: {} 字节", decodeTime - prefixTime, compressed.length);

            // GZIP解压
            ByteArrayInputStream bis = new ByteArrayInputStream(compressed);
            GZIPInputStream gis = new GZIPInputStream(bis);
            ByteArrayOutputStream out = new ByteArrayOutputStream();

            byte[] buffer = new byte[1024];
            int len;
            while ((len = gis.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }

            gis.close();
            bis.close();
            long gzipTime = System.currentTimeMillis();
            
            // 返回解压后的字符串
            String result = out.toString("UTF-8");
            long totalTime = System.currentTimeMillis() - startTime;
            
            if (totalTime > 200) { // 如果解压缩耗时超过200ms，使用warn级别记录
                LOGGER.warn("Redis数据解压缩耗时较长: {} ms, 原始大小: {} 字节, 解压后大小: {} 字节", 
                        totalTime, compressed.length, result.length());
            } else {
                LOGGER.debug("Redis数据解压缩完成，GZIP解压耗时: {} ms, 总耗时: {} ms, 解压后大小: {} 字节", 
                        gzipTime - decodeTime, totalTime, result.length());
            }
            
            return result;
        } catch (Exception e) {
            LOGGER.warn("解压数据失败: {}, 耗时: {} ms", e.getMessage(), System.currentTimeMillis() - startTime);
            // 解压失败时返回原始字符串
            return str;
        }
    }

    /**
     * 检查字符串是否需要压缩
     *
     * @param str 要检查的字符串
     * @return 如果字符串长度超过阈值则返回true
     */
    public static boolean shouldCompress(String str) {
        return enabled && str != null && str.length() >= threshold;
    }

    /**
     * 输出压缩统计信息
     */
    public static void logCompressionStats() {
        if (logStats) {
            long compressed = totalCompressed.get();
            long uncompressed = totalUncompressed.get();
            long saved = totalSaved.get();

            if (compressed > 0) {
                LOGGER.info("Redis压缩统计: 已压缩 {} 条数据, 原始大小 {} 字节, 节省 {} 字节 ({}%)",
                        compressed, uncompressed, saved,
                        uncompressed > 0 ? saved * 100 / uncompressed : 0);
            }
        }
    }

    /**
     * 重置统计信息
     */
    public static void resetStats() {
        totalCompressed.set(0);
        totalUncompressed.set(0);
        totalSaved.set(0);
    }
}