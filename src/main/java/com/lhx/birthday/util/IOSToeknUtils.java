package com.lhx.birthday.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.auth0.jwk.Jwk;
import io.jsonwebtoken.*;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.security.PublicKey;
import java.util.Base64;
import java.util.Map;

public class IOSToeknUtils {
    private final static String authUrl = "https://appleid.apple.com/auth/keys";

    private final static String authIss = "https://appleid.apple.com";

    /**
     * 解码identityToken
     * @param identityToken
     * @return
     */
    public static JSONObject parserIdentityToken(String identityToken) {
        String[] arr = identityToken.split("\\.");

        String firstDate = new String(Base64.getDecoder().decode(arr[0]), StandardCharsets.UTF_8);
        String decode = new String(Base64.getDecoder().decode(arr[1]), StandardCharsets.UTF_8);
        JSONObject claimObj = JSON.parseObject(decode);
        // 将第一部分获取到的kid放入消息体中，方便后续匹配对应的公钥使用
        claimObj.put("kid", JSONObject.parseObject(firstDate).get("kid"));
        return claimObj;
    }

    /**
     * 根据kid获取对应的苹果公钥
     * @param kid
     * @return
     */
    public static PublicKey getPublicKey(String kid) {
        try {
            RestTemplate restTemplate = new RestTemplate();
            JSONObject data = restTemplate.getForObject(authUrl, JSONObject.class);
            assert data != null;
            JSONArray jsonArray = data.getJSONArray("keys");
            for (Object obj : jsonArray) {
                Map json = ((Map) obj);
                // 获取kid对应的公钥
                if (json.get("kid").equals(kid)) {
                    Jwk jwa = Jwk.fromValues(json);
                    return jwa.getPublicKey();
                }
            }
        } catch (Exception e) {

        }
        return null;
    }

    /**
     * 对前端传来的identityToken进行验证
     *
     * @param identityToken
     * @param jsonObject
     * @return
     * @throws Exception
     */
    public static Boolean verifyExc(String identityToken, JSONObject jsonObject) throws Exception {
        String kid = (String) jsonObject.get("kid");
        PublicKey publicKey = getPublicKey(kid);

        JwtParser jwtParser = Jwts.parser().setSigningKey(publicKey);
        jwtParser.requireIssuer(authIss);
        jwtParser.requireAudience((String) jsonObject.get("aud"));
        jwtParser.requireSubject((String) jsonObject.get("sub"));
        try {
            Jws<Claims> claim = jwtParser.parseClaimsJws(identityToken);
            if (claim != null && claim.getBody().containsKey("auth_time")) {
                return true;
            }
            return false;
        } catch (ExpiredJwtException e) {
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    public static void main(String[] args) {
        // 请求的JWT
        String identityToken = "eyJraWQiOiJlWGF1bm1MIiwiYWxnIjoiUlMyNTYifQ." +
                "eyJpc3MiOiJodHRwczovL2FwcGxlaWQuYXBwbGUuY29tIiwiYXVkIjoiY29tLmRpeWl5aW4ub25saW5lNTMiLCJl" +
                "eHAiOjE1OTc2NTAxNzQsImlhdCI6MTU5NzY0OTU3NCwic3ViIjoiMDAxMzc3LmQ0ZDVmMTAwODQ0ZTQzZjdiMWM1O" +
                "WRiMzUyZWZkZmI4LjAyNTkiLCJjX2hhc2giOiJkTDVRdld2VTNjVHBxczNSazlUTnRBIiwiZW1haWwiOiI0OTk4O" +
                "TY1MDdAcXEuY29tIiwiZW1haWxfdmVyaWZpZWQiOiJ0cnVlIiwiYXV0aF90aW1lIjoxNTk3NjQ5NTc0LCJub25jZV9" +
                "zdXBwb3J0ZWQiOnRydWV9." +
                "hM9HjNsMJW2PjYP7SfbzF-GqOt0VnMjYGq4BoU68rkQ-K2lPp_ae5ziX6Bbr3WHg" +
                "6cc3Z8OzGO63OfExvSj9gQTR596CZLvNGXhbI3piTK6597-cYsPCTbY7xHxgdHLuL8XhD-9dXPn9rouVYu4QA1" +
                "8JBQG1Q4sGsRzLEJ5DjOM9x1bkBz4Vu_5LEOefHFHkWN_RPCh_AOJGviDzm81kTkCTWn8jpm0tGdevMR93MOf44" +
                "f7bjP2T8yezl0Vbv09TrnkdAqG0BsihCD0VN9JV7X2eagyumoxTdFfoRiOflFKAaQqohVzcqy9tHOGm_6w5h8bsR" +
                "CmtBC4PnqIFqNy_AQ";
        // 解码后的消息体
        JSONObject playloadObj = IOSToeknUtils.parserIdentityToken(identityToken);
        System.out.println(playloadObj.toJSONString());
        playloadObj.put("kid","lVHdOx8ltR");
        Boolean success;
        try {
            success = IOSToeknUtils.verifyExc(identityToken, playloadObj);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        System.out.println(success);
        if (!success) {
            // TODO 校验token失败具体操作
            return;
        }

        // TODO 检验token成功具体业务操作。。。
    }
}
