package com.lhx.birthday.response;

import com.lhx.birthday.vo.UploadVO;
import com.lhx.birthday.vo.UserInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 登录返回
 * Created by <PERSON><PERSON><PERSON><PERSON> on 15/11/28.
 */
@ApiModel
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UploadResponse implements Serializable{

    /**
     * 文件
     */
    @ApiModelProperty(value = "文件")
    private UploadVO uploadInfo;


}
