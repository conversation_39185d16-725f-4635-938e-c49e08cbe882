package com.lhx.birthday.response.operate;

import com.lhx.birthday.vo.market.SignInHistoryVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:02
 */
@ApiModel(description = "生成激活码")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CdkeyGenerateResponse {

    @ApiModelProperty(name = "激活码")
    private List<String> cdkeys;


}
