package com.lhx.birthday.response;

import com.lhx.birthday.vo.UserInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 登录返回
 * Created by <PERSON><PERSON><PERSON><PERSON> on 15/11/28.
 */
@ApiModel
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LoginResponse implements Serializable{

    /**
     * jwt验证token
     */
    @ApiModelProperty(value = "jwt验证token")
    private String token;

    /**
     * uuid
     */
    @ApiModelProperty(value = "uuid")
    private String uuid;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String phone;

    /**
     * 苹果Id
     */
    @ApiModelProperty(value = "苹果Id")
    private String appleId;

    /**
     * 用户详细信息
     */
    @ApiModelProperty(value = "用户详细信息")
    private UserInfoVO userInfo;

}
