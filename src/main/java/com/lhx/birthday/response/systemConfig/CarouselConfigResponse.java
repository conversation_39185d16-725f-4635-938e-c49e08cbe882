package com.lhx.birthday.response.systemConfig;

import com.lhx.birthday.vo.systemConfig.CarouselSourceVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:02
 */
@ApiModel(description = "数据源响应体")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CarouselConfigResponse {


    @ApiModelProperty(name = "数据源信息")
    private List<CarouselSourceVO> carousel;



}
