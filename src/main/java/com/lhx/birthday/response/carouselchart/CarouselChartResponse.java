package com.lhx.birthday.response.carouselchart;

import com.lhx.birthday.vo.carouselchart.CarouselChartVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Page;

import java.io.Serializable;
import java.util.List;

/**
 * 登录返回
 * Created by <PERSON><PERSON><PERSON><PERSON> on 15/11/28.
 */
@ApiModel
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CarouselChartResponse implements Serializable{

    @ApiModelProperty(name = "轮播图信息")
    private List<CarouselChartVO> carouselCharts;

}
