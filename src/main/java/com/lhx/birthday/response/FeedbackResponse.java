package com.lhx.birthday.response;

import com.lhx.birthday.vo.FeedbackVO;
import com.lhx.birthday.vo.UserInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 登录返回
 * Created by <PERSON><PERSON><PERSON><PERSON> on 15/11/28.
 */
@ApiModel
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FeedbackResponse implements Serializable{

    /**
     * 反馈记录列表
     */
    @ApiModelProperty(value = "反馈记录列表")
    private List<FeedbackVO> feedbackVOS;



}
