package com.lhx.birthday.response.constellation;

import com.lhx.birthday.vo.constellation.ConstellationProfileVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@ApiModel(value = "星座档案列表响应")
public class ConstellationListResponse {
    
    @ApiModelProperty(value = "星座档案列表")
    private List<ConstellationProfileVO> constellations;
} 