package com.lhx.birthday.response.constellation;

import com.lhx.birthday.vo.profile.RelationshipVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@ApiModel(value = "关系响应")
public class RelationshipResponse {
    
    @ApiModelProperty(value = "关系列表")
    private List<RelationshipVO> relationshipS;
} 