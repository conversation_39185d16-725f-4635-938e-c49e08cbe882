package com.lhx.birthday.response.constellation;

import com.lhx.birthday.vo.constellation.ConstellationProfileVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@ApiModel(value = "星座档案详情响应")
public class ConstellationDetailResponse {
    
    @ApiModelProperty(value = "星座档案详情")
    private ConstellationProfileVO constellation;
} 