package com.lhx.birthday.response.constellation;

import com.lhx.birthday.vo.profile.RegionVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@ApiModel(value = "地区响应")
public class RegionResponse {
    
    @ApiModelProperty(value = "地区列表")
    private List<RegionVO> countryCodes;
} 