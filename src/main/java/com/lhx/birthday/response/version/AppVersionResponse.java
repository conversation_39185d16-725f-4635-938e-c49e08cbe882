package com.lhx.birthday.response.version;

import com.lhx.birthday.vo.version.AppVersionVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Page;

import java.io.Serializable;
import java.util.List;

@ApiModel
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AppVersionResponse implements Serializable{

    @ApiModelProperty(name = "文章信息")
    private List<AppVersionVO> versions;

}
