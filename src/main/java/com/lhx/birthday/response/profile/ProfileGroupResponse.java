package com.lhx.birthday.response.profile;

import com.lhx.birthday.vo.profile.ProfileGroupVO;
import com.lhx.birthday.vo.profile.ProfileVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:02
 */
@ApiModel(description = "人员档案分组数据响应体")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProfileGroupResponse {

    @ApiModelProperty(name = "个人档案")
    private ProfileVO selfProfile;

    @ApiModelProperty(name = "档案分组")
    private List<ProfileGroupVO> profileGroups;



}
