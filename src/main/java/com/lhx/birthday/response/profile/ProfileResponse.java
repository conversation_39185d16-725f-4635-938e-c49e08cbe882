package com.lhx.birthday.response.profile;

import com.lhx.birthday.vo.profile.ProfileVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:02
 */
@ApiModel(description = "人员档案数据响应体")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProfileResponse {


    @ApiModelProperty(name = "人员档案")
    private List<ProfileVO> profiles;



}
