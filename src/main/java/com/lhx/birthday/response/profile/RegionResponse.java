package com.lhx.birthday.response.profile;

import com.lhx.birthday.vo.profile.RegionVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:02
 */
@ApiModel(description = "省份数据响应体")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RegionResponse {


//    @ApiModelProperty(name = "已关注省份信息")
//    private List<CityCodeVO> followedProvinces;

//    @ApiModelProperty(name = "未关注省份信息")
//    private List<CityCodeVO> unfollowedProvinces;

    @ApiModelProperty(name = "地区信息列表")
    private List<RegionVO> countryCodes;


}
