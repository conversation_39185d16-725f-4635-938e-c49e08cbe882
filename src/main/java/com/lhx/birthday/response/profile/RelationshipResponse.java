package com.lhx.birthday.response.profile;

import com.lhx.birthday.vo.profile.ProfileVO;
import com.lhx.birthday.vo.profile.RelationshipVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:02
 */
@ApiModel(description = "档案关系数据响应体")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RelationshipResponse {


    @ApiModelProperty(name = "档案关系")
    private List<RelationshipVO> relationshipS;



}
