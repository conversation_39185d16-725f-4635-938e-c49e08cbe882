package com.lhx.birthday.response.dream;

import com.lhx.birthday.vo.dream.DreamHotVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 登录返回
 * Created by <PERSON><PERSON><PERSON><PERSON> on 15/11/28.
 */
@ApiModel
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DreamHotResponse implements Serializable{

    /**
     * 热门梦境列表
     */
    @ApiModelProperty(value = "热门梦境列表")
    private List<DreamHotVO> dreamHots;



}
