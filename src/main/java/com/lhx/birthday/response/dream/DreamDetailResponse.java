package com.lhx.birthday.response.dream;

import com.lhx.birthday.vo.dream.DreamDetailVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 登录返回
 * Created by <PERSON><PERSON><PERSON><PERSON> on 15/11/28.
 */
@ApiModel
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DreamDetailResponse implements Serializable{

    /**
     * 梦境列表
     */
    @ApiModelProperty(value = "梦境列表")
    private DreamDetailVO dreamDetail;



}
