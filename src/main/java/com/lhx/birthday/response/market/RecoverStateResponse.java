package com.lhx.birthday.response.market;

import com.lhx.birthday.vo.market.RecoverSettingVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@ApiModel("挽回状态响应")
public class RecoverStateResponse {

    @ApiModelProperty("是否需要弹窗")
    private Boolean needPopup;

    @ApiModelProperty("挽回设置")
    private RecoverSettingVO recoverSetting;

    @ApiModelProperty("是否第一次弹窗")
    private Boolean isFirstPopup;
}