package com.lhx.birthday.response.market;

import com.lhx.birthday.vo.market.Market5StarReviewVO;
import com.lhx.birthday.vo.market.MarketSettingVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel(description = "5星好评")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Market5StarReviewResponse {

    @ApiModelProperty(name = "5星好评")
    private Market5StarReviewVO market5StarReview;

}
