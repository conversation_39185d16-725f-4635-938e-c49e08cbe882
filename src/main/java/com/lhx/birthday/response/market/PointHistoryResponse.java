package com.lhx.birthday.response.market;

import com.lhx.birthday.vo.market.InvitationHistoryVO;
import com.lhx.birthday.vo.market.PointHistoryVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:02
 */
@ApiModel(description = "积分明细响应体")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PointHistoryResponse {

    @ApiModelProperty(name = "积分明细")
    private PointHistoryVO pointHistory;


}
