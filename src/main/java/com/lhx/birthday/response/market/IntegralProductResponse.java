package com.lhx.birthday.response.market;

import com.lhx.birthday.vo.market.PointVipVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:02
 */
@ApiModel(description = "积分商品响应体")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IntegralProductResponse {

    @ApiModelProperty(name = "积分商品配置")
    private List<PointVipVO> integralProducts;


}
