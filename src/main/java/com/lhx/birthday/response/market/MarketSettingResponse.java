package com.lhx.birthday.response.market;

import com.lhx.birthday.vo.market.MarketSettingVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:02
 */
@ApiModel(description = "营销配置响应体")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MarketSettingResponse {

    @ApiModelProperty(name = "营销配置")
    private MarketSettingVO marketSetting;


}
