package com.lhx.birthday.response.market;

import com.lhx.birthday.vo.market.PointVipVO;
import com.lhx.birthday.vo.market.SignInHistoryVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:02
 */
@ApiModel(description = "签到历史响应体")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SignInHistoryResponse {

    @ApiModelProperty(name = "签到历史")
    private SignInHistoryVO signInHistory;


}
