package com.lhx.birthday.response.market;

import com.lhx.birthday.vo.market.Market5StarReviewVO;
import com.lhx.birthday.vo.market.Market5StarStateVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel(description = "5星好评状态")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Market5StarStateResponse {

    @ApiModelProperty(name = "5星好评状态")
    private Market5StarStateVO market5StarState;

}
