package com.lhx.birthday.job.horoscope;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lhx.birthday.constant.ApiConstant;
import com.lhx.birthday.constant.RedisKeyConstant;
import com.lhx.birthday.enums.ZodiacSigns;
import com.lhx.birthday.mapper.ZodiacSignsMapper;
import com.lhx.birthday.redis.RedisService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR> lhx
 * @date 2023/10/23 10:30
 */
@Component
@Slf4j
@JobHandler(value = "DayliHoroscopeTimeJobHandler")
public class HoroscopeTimeJobHandler extends IJobHandler {

    @Autowired
    private ZodiacSignsMapper zodiacSignsMapper;

    @Autowired
    private RedisService redisService;

    @Value("${yuanfenju.appKey}")
    private String appKey;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        // 构建请求URL
        String url = ApiConstant.API_DAYLI_HOROSCOPE;

        // 循环调用所有可能的 type 和 title_yunshi 值
        for (int type = 0; type <= 1; type++) {
            JSONArray dayArray = new JSONArray();
            JSONArray monthArray = new JSONArray();
            JSONArray yearArray = new JSONArray();
            JSONArray sourceArray = new JSONArray();
            for (int titleYunshi = 0; titleYunshi <= 11; titleYunshi++) {
                // 构建请求参数（明确加上语言，默认 zh-cn）
                String requestData = "api_key=" + appKey + "&type=" + type + "&title_yunshi=" + titleYunshi + "&lang=zh-cn";

                // 发送POST请求
                HttpResponse response = HttpRequest.post(url)
                        .header("Content-Type", "application/x-www-form-urlencoded")
                        .body(requestData)
                        .timeout(5000)
                        .execute();

                // 读取响应结果
                String result = response.body();
                JSONObject jsonObject = JSONObject.parseObject(result);
                if (jsonObject.getInteger("errcode").equals(0)) {
                    JSONObject dataObj = jsonObject.getJSONObject("data");
                    dataObj = JSONObject.parseObject(dataObj.toJSONString().replaceAll("魔羯座", "摩羯座"));
                    for (Map.Entry<String, Object> entry : dataObj.entrySet()) {
                        if(entry.getValue() instanceof JSONObject){
                            JSONObject enObj = (JSONObject) entry.getValue();
                            for (Map.Entry<String, Object> val : enObj.entrySet()) {
                                if(val.getKey().contains("分数")){
                                    String value = (String) val.getValue();
                                    value = value.split("\\.")[0];
                                    val.setValue(value);
                                }
                            }
                        }
                    }

                    String key = RedisKeyConstant.HOROSCOPE_KEY + type + ":" + titleYunshi;
                    // 替换生肖语料
                    if(type==1){
                        dataObj = reWrite(dataObj,titleYunshi);
                    }
                    // 默认简体
                    redisService.setString(key, dataObj.toJSONString());

                    JSONObject dayObject = new JSONObject();
                    dayObject.put("运势类型",dataObj.getString("运势类型"));
                    dayObject.put("今明运势",dataObj.getJSONObject("今日运势").getString("今明运势"));
                    dayObject.put("爱情运势",dataObj.getJSONObject("今日运势").getString("爱情运势"));
                    dayObject.put("事业运势",dataObj.getJSONObject("今日运势").getString("事业运势"));
                    dayObject.put("财富运势",dataObj.getJSONObject("今日运势").getString("财富运势"));
                    dayObject.put("健康运势",dataObj.getJSONObject("今日运势").getString("健康运势"));
                    dayArray.add(dayObject);

                    JSONObject monthObject = new JSONObject();
                    monthObject.put("运势类型",dataObj.getString("运势类型"));
                    monthObject.put("今明运势",dataObj.getJSONObject("本月运势").getString("今明运势"));
                    monthObject.put("爱情运势",dataObj.getJSONObject("本月运势").getString("爱情运势"));
                    monthObject.put("事业运势",dataObj.getJSONObject("本月运势").getString("事业运势"));
                    monthObject.put("财富运势",dataObj.getJSONObject("本月运势").getString("财富运势"));
                    monthObject.put("健康运势",dataObj.getJSONObject("本月运势").getString("健康运势"));
                    monthArray.add(monthObject);

                    JSONObject yearObject = new JSONObject();
                    yearObject.put("运势类型",dataObj.getString("运势类型"));
                    yearObject.put("今明运势",dataObj.getJSONObject("本年运势").getString("今明运势"));
                    yearObject.put("爱情运势",dataObj.getJSONObject("本年运势").getString("爱情运势"));
                    yearObject.put("事业运势",dataObj.getJSONObject("本年运势").getString("事业运势"));
                    yearObject.put("财富运势",dataObj.getJSONObject("本年运势").getString("财富运势"));
                    yearObject.put("健康运势",dataObj.getJSONObject("本年运势").getString("健康运势"));
                    yearArray.add(yearObject);

                    // 星座计算排名
                    JSONObject scoreObject = new JSONObject();
                    scoreObject.put("总分",dataObj.getJSONObject("今日运势").getString("综合分数"));
                    scoreObject.put("爱情",dataObj.getJSONObject("今日运势").getString("爱情分数"));
                    scoreObject.put("财富",dataObj.getJSONObject("今日运势").getString("财富分数"));
                    scoreObject.put("事业",dataObj.getJSONObject("今日运势").getString("事业分数"));
                    scoreObject.put("健康",dataObj.getJSONObject("今日运势").getString("健康分数"));
                    scoreObject.put("交际",dataObj.getJSONObject("今日运势").getString("交际分数"));
                    scoreObject.put("运势类型", dataObj.getString("运势类型"));
                    sourceArray.add(scoreObject);
                }

                // 关闭连接
                response.close();
            }
            // 整理生肖/星座数据
            redisService.setString(RedisKeyConstant.HOROSCOPE_KEY + RedisKeyConstant.DAY_KEY + type, dayArray.toJSONString());
            redisService.setString(RedisKeyConstant.HOROSCOPE_KEY + RedisKeyConstant.MONTH_KEY + type, monthArray.toJSONString());
            redisService.setString(RedisKeyConstant.HOROSCOPE_KEY + RedisKeyConstant.YEAR_KEY + type, yearArray.toJSONString());
            if(Objects.isNull(zodiacSignsMapper.findByTimeAndType(formatDate(LocalDate.now(),"yyyy-MM-dd"),type))){
                zodiacSignsMapper.saveAndFlush(ZodiacSigns.builder()
                        .time(formatDate(LocalDate.now(),"yyyy-MM-dd"))
                        .type(type)
                        .info(dayArray.toJSONString())
                        .build());
            }
            if(Objects.isNull(zodiacSignsMapper.findByTimeAndType(formatDate(LocalDate.now(),"yyyy-MM"),type))){
                zodiacSignsMapper.saveAndFlush(ZodiacSigns.builder()
                        .time(formatDate(LocalDate.now(),"yyyy-MM"))
                        .type(type)
                        .info(monthArray.toJSONString())
                        .build());
            }
            if(Objects.isNull(zodiacSignsMapper.findByTimeAndType(formatDate(LocalDate.now(),"yyyy"),type))){
                zodiacSignsMapper.saveAndFlush(ZodiacSigns.builder()
                        .time(formatDate(LocalDate.now(),"yyyy"))
                        .type(type)
                        .info(yearArray.toJSONString())
                        .build());
            }
            // 整理生肖/星座排名
            JSONObject scoresByType = new JSONObject();
            Map<String, Map<String, Integer>> rankings = new HashMap<>();
            // 计算分数
            for (int i = 0; i < sourceArray.size(); i++) {
                JSONObject scoreObject = sourceArray.getJSONObject(i);
                String zodiac = scoreObject.getString("运势类型");
                // 遍历每种分数类型
                for (String scoreType : Arrays.asList("总分", "爱情", "财富", "事业", "健康", "交际")) {
                    int score = Integer.parseInt(scoreObject.getString(scoreType));
                    rankings.putIfAbsent(scoreType, new HashMap<>());
                    rankings.get(scoreType).put(zodiac, score);
                }
            }
            // 生成排名和星级
            for (String scoreType : rankings.keySet()) {
                List<Map.Entry<String, Integer>> sortedEntries = new ArrayList<>(rankings.get(scoreType).entrySet());
                sortedEntries.sort((a, b) -> b.getValue().compareTo(a.getValue())); // 从高到低排序
                JSONArray rankedArray = new JSONArray();
                int rank = 1;
                for (Map.Entry<String, Integer> entry : sortedEntries) {
                    JSONObject rankedObject = new JSONObject();
                    String zodiac = entry.getKey();
                    int score = entry.getValue();
                    rankedObject.put("运势", zodiac);
                    rankedObject.put("分数", score);
                    rankedObject.put("指数", getStarRating(score));
                    rankedObject.put("排名", rank);
                    rankedArray.add(rankedObject);
                    rank++;
                }
                scoresByType.put(scoreType, rankedArray);
            }

            // 生成第二个 JSON，按照星座划分
            JSONObject zodiacRankings = new JSONObject();
            for (String zodiac : rankings.values().iterator().next().keySet()) {
                JSONObject zodiacData = new JSONObject();
                for (String scoreType : rankings.keySet()) {
                    List<Map.Entry<String, Integer>> sortedEntries = new ArrayList<>(rankings.get(scoreType).entrySet());
                    sortedEntries.sort((a, b) -> b.getValue().compareTo(a.getValue())); // 从高到低排序

                    for (int rank = 0; rank < sortedEntries.size(); rank++) {
                        if (sortedEntries.get(rank).getKey().equals(zodiac)) {
                            int score = sortedEntries.get(rank).getValue();
                            JSONObject scoreInfo = new JSONObject();
                            scoreInfo.put("排名", rank + 1);
                            scoreInfo.put("分数", score);
                            scoreInfo.put("指数", getStarRating(score));
                            zodiacData.put(scoreType, scoreInfo);
                            break;
                        }
                    }
                }
                zodiacRankings.put(zodiac, zodiacData);
            }
            redisService.setString(RedisKeyConstant.HOROSCOPE_KEY + RedisKeyConstant.DAY_KEY + "rank:" + type, scoresByType.toJSONString());
            redisService.setString(RedisKeyConstant.HOROSCOPE_KEY + RedisKeyConstant.DAY_KEY + "rankInfo:" + type, zodiacRankings.toJSONString());
        }
        return SUCCESS;
    }

    private static double getStarRating(int score) {
        if (score >= 95) return 5; // 返回5，不带小数点
        if (score >= 90) return 4.5;
        if (score >= 85) return 4;
        if (score >= 80) return 3.5;
        if (score >= 75) return 3;
        if (score >= 70) return 2.5;
        if (score >= 65) return 2;
        if (score >= 60) return 1.5;
        return 1;
    }

    private JSONObject reWrite(JSONObject writeObj,int animalsIndex){
        try {
            LocalDateTime now = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
            String formattedDateTime = now.format(formatter);
            HttpResponse response = HttpRequest.post("https://zsx.linggx365.cn/api/algorithm")
                    .header("Content-Type", "application/x-www-form-urlencoded")
                    .body("activity=ba_zi_zhun_sheng_xiao&year="+now.getYear()+"&month="+now.getMonthValue()+"&optionalList=0%2C1%2C2%2C3%2C4&animalsIndex="+animalsIndex+"&time="+formattedDateTime)
                    .timeout(5000)
                    .execute();
            String result = response.body();
            JSONObject jsonObject = JSONObject.parseObject(result);
            if (jsonObject.getInteger("code").equals(200)) {
                JSONObject dataObj = jsonObject.getJSONObject("data");
                JSONObject dayObj = dataObj.getJSONObject("today_yun_shi");
                JSONObject tomorrowObj = dataObj.getJSONObject("tomorrow_yun_shi");
                JSONObject weekObj = dataObj.getJSONObject("weekly_yun_shi");
                JSONObject monthObj = dataObj.getJSONObject("monthly_yun_shi");
                JSONObject yearObj = dataObj.getJSONObject("yearly_yun_shi");

                JSONObject writeToDayJSONObject = writeObj.getJSONObject("今日运势");
                JSONObject writeTomorrowJSONObject = writeObj.getJSONObject("明日运势");
                JSONObject writeWeekJSONObject = writeObj.getJSONObject("本周运势");
                JSONObject monthJSONObject = writeObj.getJSONObject("本月运势");
                JSONObject yearJSONObject = writeObj.getJSONObject("本年运势");
                for (Object fenXi : dayObj.getJSONArray("fen_xi")) {
                    JSONObject fenxiObj = (JSONObject) fenXi;
                    String title = fenxiObj.getString("title");
                    int totalScore = 0;
                    List<Integer> scores = new ArrayList<>();
                    for (Object score : fenxiObj.getJSONArray("score")) {
                        int hourScore = (int) score;
                        scores.add(hourScore);
                    }
                    Collections.sort(scores, Collections.reverseOrder());
                    for (int i = 0; i < Math.min(3, scores.size()); i++) {
                        totalScore += scores.get(i);
                    }
                    totalScore = totalScore / 3;
                    StringBuffer stringBuffer = new StringBuffer();
                    for (Object dec : fenxiObj.getJSONArray("dec")) {
                        String decStr = (String) dec;
                        stringBuffer.append(decStr);
                    }
                    if(title.equals("事业运势")){
                        writeToDayJSONObject.put("事业分数",String.valueOf(totalScore));
                        writeToDayJSONObject.put("事业运势",stringBuffer);
                    }else if(title.equals("爱情运势")){
                        writeToDayJSONObject.put("爱情分数",String.valueOf(totalScore));
                        writeToDayJSONObject.put("爱情运势",stringBuffer);
                    }else if(title.equals("健康运势")){
                        writeToDayJSONObject.put("健康分数",String.valueOf(totalScore));
                        writeToDayJSONObject.put("健康运势",stringBuffer);
                    }else if(title.equals("财富运势")){
                        writeToDayJSONObject.put("财富分数",String.valueOf(totalScore));
                        writeToDayJSONObject.put("财富运势",stringBuffer);
                    }
                }
                for (Object fenXi : tomorrowObj.getJSONArray("fen_xi")) {
                    JSONObject fenxiObj = (JSONObject) fenXi;
                    String title = fenxiObj.getString("title");
                    int totalScore = 0;
                    List<Integer> scores = new ArrayList<>();
                    for (Object score : fenxiObj.getJSONArray("score")) {
                        int hourScore = (int) score;
                        scores.add(hourScore);
                    }
                    Collections.sort(scores, Collections.reverseOrder());
                    for (int i = 0; i < Math.min(3, scores.size()); i++) {
                        totalScore += scores.get(i);
                    }
                    totalScore = totalScore / 3;
                    StringBuffer stringBuffer = new StringBuffer();
                    for (Object dec : fenxiObj.getJSONArray("dec")) {
                        String decStr = (String) dec;
                        stringBuffer.append(decStr);
                    }
                    if(title.equals("事业运势")){
                        writeTomorrowJSONObject.put("事业分数",String.valueOf(totalScore));
                        writeTomorrowJSONObject.put("事业运势",stringBuffer);
                    }else if(title.equals("爱情运势")){
                        writeTomorrowJSONObject.put("爱情分数",String.valueOf(totalScore));
                        writeTomorrowJSONObject.put("爱情运势",stringBuffer);
                    }else if(title.equals("健康运势")){
                        writeTomorrowJSONObject.put("健康分数",String.valueOf(totalScore));
                        writeTomorrowJSONObject.put("健康运势",stringBuffer);
                    }else if(title.equals("财富运势")){
                        writeTomorrowJSONObject.put("财富分数",String.valueOf(totalScore));
                        writeTomorrowJSONObject.put("财富运势",stringBuffer);
                    }
                }
                for (Object fenXi : weekObj.getJSONArray("fen_xi")) {
                    JSONObject fenxiObj = (JSONObject) fenXi;
                    String title = fenxiObj.getString("title");
                    int totalScore = 0;
                    List<Integer> scores = new ArrayList<>();
                    for (Object score : fenxiObj.getJSONArray("score")) {
                        int hourScore = (int) score;
                        scores.add(hourScore);
                    }
                    Collections.sort(scores, Collections.reverseOrder());
                    for (int i = 0; i < Math.min(3, scores.size()); i++) {
                        totalScore += scores.get(i);
                    }
                    totalScore = totalScore / 3;
                    StringBuffer stringBuffer = new StringBuffer();
                    for (Object dec : fenxiObj.getJSONArray("dec")) {
                        String decStr = (String) dec;
                        stringBuffer.append(decStr);
                    }
                    if(title.equals("事业运势")){
                        writeWeekJSONObject.put("事业分数",String.valueOf(totalScore));
                        writeWeekJSONObject.put("事业运势",stringBuffer);
                    }else if(title.equals("爱情运势")){
                        writeWeekJSONObject.put("爱情分数",String.valueOf(totalScore));
                        writeWeekJSONObject.put("爱情运势",stringBuffer);
                    }else if(title.equals("健康运势")){
                        writeWeekJSONObject.put("健康分数",String.valueOf(totalScore));
                        writeWeekJSONObject.put("健康运势",stringBuffer);
                    }else if(title.equals("财富运势")){
                        writeWeekJSONObject.put("财富分数",String.valueOf(totalScore));
                        writeWeekJSONObject.put("财富运势",stringBuffer);
                    }
                }

                for (Object fenXi : monthObj.getJSONArray("fen_xi")) {
                    JSONObject fenxiObj = (JSONObject) fenXi;
                    String title = fenxiObj.getString("title");
                    StringBuffer stringBuffer = new StringBuffer();
                    for (Object dec : fenxiObj.getJSONArray("dec")) {
                        String decStr = (String) dec;
                        stringBuffer.append(decStr);
                    }
                    if(title.equals("事业运势")){
                        monthJSONObject.put("事业运势",stringBuffer);
                    }else if(title.equals("爱情运势")){
                        monthJSONObject.put("爱情运势",stringBuffer);
                    }else if(title.equals("健康运势")){
                        monthJSONObject.put("健康运势",stringBuffer);
                    }else if(title.equals("财富运势")){
                        monthJSONObject.put("财富运势",stringBuffer);
                    }
                }

                for (Object fenXi : yearObj.getJSONArray("fen_xi")) {
                    JSONObject fenxiObj = (JSONObject) fenXi;
                    String title = fenxiObj.getString("title");
                    StringBuffer stringBuffer = new StringBuffer();
                    if(Objects.nonNull(fenxiObj.getJSONArray("dec"))){
                        for (Object dec : fenxiObj.getJSONArray("dec")) {
                            String decStr = (String) dec;
                            stringBuffer.append(decStr);
                        }
                        if(title.equals("事业运势")){
                            yearJSONObject.put("事业运势",stringBuffer);
                        }else if(title.equals("爱情运势")){
                            yearJSONObject.put("爱情运势",stringBuffer);
                        }else if(title.equals("健康运势")){
                            yearJSONObject.put("健康运势",stringBuffer);
                        }else if(title.equals("财富运势")){
                            yearJSONObject.put("财富运势",stringBuffer);
                        }
                    }
                }
                // 重新计算综合分数
                int toDayZh = writeToDayJSONObject.getInteger("爱情分数") + writeToDayJSONObject.getInteger("健康分数") + writeToDayJSONObject.getInteger("财富分数") + writeToDayJSONObject.getInteger("心情分数") + writeToDayJSONObject.getInteger("交际分数") + writeToDayJSONObject.getInteger("事业分数");
                int tomorrowZh = writeTomorrowJSONObject.getInteger("爱情分数") + writeTomorrowJSONObject.getInteger("健康分数") + writeTomorrowJSONObject.getInteger("财富分数") + writeTomorrowJSONObject.getInteger("心情分数") + writeTomorrowJSONObject.getInteger("交际分数") + writeTomorrowJSONObject.getInteger("事业分数");
                int weekZh = writeWeekJSONObject.getInteger("爱情分数") + writeWeekJSONObject.getInteger("健康分数") + writeWeekJSONObject.getInteger("财富分数") + writeWeekJSONObject.getInteger("心情分数") + writeWeekJSONObject.getInteger("交际分数") + writeWeekJSONObject.getInteger("事业分数");

                writeToDayJSONObject.put("综合分数",String.valueOf(toDayZh/6));
                writeTomorrowJSONObject.put("综合分数",String.valueOf(tomorrowZh/6));
                writeWeekJSONObject.put("综合分数",String.valueOf(weekZh/6));
            }
        } catch (Exception e){
            e.printStackTrace();
            return writeObj;
        }
        return writeObj;
    }

    public static String formatDate(LocalDate date, String pattern) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return date.format(formatter);
    }

}
