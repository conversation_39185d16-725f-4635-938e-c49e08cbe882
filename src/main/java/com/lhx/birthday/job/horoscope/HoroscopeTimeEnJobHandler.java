package com.lhx.birthday.job.horoscope;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lhx.birthday.constant.ApiConstant;
import com.lhx.birthday.constant.RedisKeyConstant;
import com.lhx.birthday.redis.RedisService;
import com.lhx.birthday.mapper.ZodiacSignsEnMapper;
import com.lhx.birthday.entity.ZodiacSignsEn;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.util.*;

/**
 * 英文版 - 星座/生肖每日运势抓取与入库
 * 行为与中文版保持一致，只是请求 lang=en-us，并存储到 :en-us 后缀的 Redis key
 */
@Component
@Slf4j
@JobHandler(value = "DayliHoroscopeTimeEnJobHandler")
public class HoroscopeTimeEnJobHandler extends IJobHandler {


    @Autowired
    private ZodiacSignsEnMapper zodiacSignsEnMapper;

    @Autowired
    private RedisService redisService;

    @Value("${yuanfenju.appKey}")
    private String appKey;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        String url = ApiConstant.API_DAYLI_HOROSCOPE;
        for (int type = 0; type <= 1; type++) {
            JSONArray dayArray = new JSONArray();
            JSONArray monthArray = new JSONArray();
            JSONArray yearArray = new JSONArray();
            JSONArray sourceArray = new JSONArray();
            for (int titleYunshi = 0; titleYunshi <= 11; titleYunshi++) {
                // 英文请求
                String requestDataEn = "api_key=" + appKey + "&type=" + type + "&title_yunshi=" + titleYunshi + "&lang=en-us";
                HttpResponse responseEn = HttpRequest.post(url)
                        .header("Content-Type", "application/x-www-form-urlencoded")
                        .body(requestDataEn)
                        .timeout(5000)
                        .execute();
                String resultEn = responseEn.body();
                JSONObject jsonObjectEn = JSONObject.parseObject(resultEn);
                if (jsonObjectEn.getInteger("errcode").equals(0)) {
                    JSONObject dataObj = jsonObjectEn.getJSONObject("data");

                    String key = RedisKeyConstant.HOROSCOPE_KEY + type + ":" + titleYunshi + ":en-us";
                    // 直接存英文数据
                    redisService.setString(key, dataObj.toJSONString());

                    // 摘要-日/月/年
                    JSONObject dayObject = new JSONObject();
                    dayObject.put("运势类型",dataObj.getString("运势类型"));
                    dayObject.put("今明运势",dataObj.getJSONObject("今日运势").getString("今明运势"));
                    dayObject.put("爱情运势",dataObj.getJSONObject("今日运势").getString("爱情运势"));
                    dayObject.put("事业运势",dataObj.getJSONObject("今日运势").getString("事业运势"));
                    dayObject.put("财富运势",dataObj.getJSONObject("今日运势").getString("财富运势"));
                    dayObject.put("健康运势",dataObj.getJSONObject("今日运势").getString("健康运势"));
                    dayArray.add(dayObject);

                    JSONObject monthObject = new JSONObject();
                    monthObject.put("运势类型",dataObj.getString("运势类型"));
                    monthObject.put("今明运势",dataObj.getJSONObject("本月运势").getString("今明运势"));
                    monthObject.put("爱情运势",dataObj.getJSONObject("本月运势").getString("爱情运势"));
                    monthObject.put("事业运势",dataObj.getJSONObject("本月运势").getString("事业运势"));
                    monthObject.put("财富运势",dataObj.getJSONObject("本月运势").getString("财富运势"));
                    monthObject.put("健康运势",dataObj.getJSONObject("本月运势").getString("健康运势"));
                    monthArray.add(monthObject);

                    JSONObject yearObject = new JSONObject();
                    yearObject.put("运势类型",dataObj.getString("运势类型"));
                    yearObject.put("今明运势",dataObj.getJSONObject("本年运势").getString("今明运势"));
                    yearObject.put("爱情运势",dataObj.getJSONObject("本年运势").getString("爱情运势"));
                    yearObject.put("事业运势",dataObj.getJSONObject("本年运势").getString("事业运势"));
                    yearObject.put("财富运势",dataObj.getJSONObject("本年运势").getString("财富运势"));
                    yearObject.put("健康运势",dataObj.getJSONObject("本年运势").getString("健康运势"));
                    yearArray.add(yearObject);

                    // 评分来源（英文端也产出，便于排名逻辑保持一致）
                    JSONObject scoreObject = new JSONObject();
                    scoreObject.put("总分",dataObj.getJSONObject("今日运势").getString("综合分数"));
                    scoreObject.put("爱情",dataObj.getJSONObject("今日运势").getString("爱情分数"));
                    scoreObject.put("财富",dataObj.getJSONObject("今日运势").getString("财富分数"));
                    scoreObject.put("事业",dataObj.getJSONObject("今日运势").getString("事业分数"));
                    scoreObject.put("健康",dataObj.getJSONObject("今日运势").getString("健康分数"));
                    scoreObject.put("交际",dataObj.getJSONObject("今日运势").getString("交际分数"));
                    scoreObject.put("运势类型", dataObj.getString("运势类型"));
                    sourceArray.add(scoreObject);
                }
                responseEn.close();
            }

            // 英文的 day/month/year 汇总也单独存储（带 :en-us）
            // 同步写英文表（按日/月/年分别）
            if (zodiacSignsEnMapper.findByTimeAndType(formatDate(LocalDate.now(),"yyyy-MM-dd"), type) == null) {
                zodiacSignsEnMapper.saveAndFlush(ZodiacSignsEn.builder()
                        .time(formatDate(LocalDate.now(),"yyyy-MM-dd"))
                        .type(type)
                        .info(dayArray.toJSONString())
                        .build());
            }
            if (zodiacSignsEnMapper.findByTimeAndType(formatDate(LocalDate.now(),"yyyy-MM"), type) == null) {
                zodiacSignsEnMapper.saveAndFlush(ZodiacSignsEn.builder()
                        .time(formatDate(LocalDate.now(),"yyyy-MM"))
                        .type(type)
                        .info(monthArray.toJSONString())
                        .build());
            }
            if (zodiacSignsEnMapper.findByTimeAndType(formatDate(LocalDate.now(),"yyyy"), type) == null) {
                zodiacSignsEnMapper.saveAndFlush(ZodiacSignsEn.builder()
                        .time(formatDate(LocalDate.now(),"yyyy"))
                        .type(type)
                        .info(yearArray.toJSONString())
                        .build());
            }

            redisService.setString(RedisKeyConstant.HOROSCOPE_KEY + RedisKeyConstant.DAY_KEY + type + ":en-us", dayArray.toJSONString());
            redisService.setString(RedisKeyConstant.HOROSCOPE_KEY + RedisKeyConstant.MONTH_KEY + type + ":en-us", monthArray.toJSONString());
            redisService.setString(RedisKeyConstant.HOROSCOPE_KEY + RedisKeyConstant.YEAR_KEY + type + ":en-us", yearArray.toJSONString());

            // 英文排名与排名详情（复用中文逻辑计算方式）
            JSONObject scoresByType = new JSONObject();
            Map<String, Map<String, Integer>> rankings = new HashMap<>();
            for (int i = 0; i < sourceArray.size(); i++) {
                JSONObject scoreObject = sourceArray.getJSONObject(i);
                String zodiac = scoreObject.getString("运势类型");
                for (String scoreType : Arrays.asList("总分", "爱情", "财富", "事业", "健康", "交际")) {
                    int score = Integer.parseInt(scoreObject.getString(scoreType));
                    rankings.putIfAbsent(scoreType, new HashMap<>());
                    rankings.get(scoreType).put(zodiac, score);
                }
            }
            for (String scoreType : rankings.keySet()) {
                List<Map.Entry<String, Integer>> sortedEntries = new ArrayList<>(rankings.get(scoreType).entrySet());
                sortedEntries.sort((a, b) -> b.getValue().compareTo(a.getValue()));
                JSONArray rankedArray = new JSONArray();
                int rank = 1;
                for (Map.Entry<String, Integer> entry : sortedEntries) {
                    JSONObject rankedObject = new JSONObject();
                    String zodiac = entry.getKey();
                    int score = entry.getValue();
                    rankedObject.put("运势", zodiac);
                    rankedObject.put("分数", score);
                    rankedObject.put("指数", getStarRating(score));
                    rankedObject.put("排名", rank);
                    rankedArray.add(rankedObject);
                    rank++;
                }
                scoresByType.put(scoreType, rankedArray);
            }
            JSONObject zodiacRankings = new JSONObject();
            if (!rankings.isEmpty()) {
                for (String zodiac : rankings.values().iterator().next().keySet()) {
                    JSONObject zodiacData = new JSONObject();
                    for (String scoreTypeInner : rankings.keySet()) {
                        List<Map.Entry<String, Integer>> sortedEntries = new ArrayList<>(rankings.get(scoreTypeInner).entrySet());
                        sortedEntries.sort((a, b) -> b.getValue().compareTo(a.getValue()));
                        for (int rank = 0; rank < sortedEntries.size(); rank++) {
                            if (sortedEntries.get(rank).getKey().equals(zodiac)) {
                                int score = sortedEntries.get(rank).getValue();
                                JSONObject scoreInfo = new JSONObject();
                                scoreInfo.put("排名", rank + 1);
                                scoreInfo.put("分数", score);
                                scoreInfo.put("指数", getStarRating(score));
                                zodiacData.put(scoreTypeInner, scoreInfo);
                                break;
                            }
                        }
                    }
                    zodiacRankings.put(zodiac, zodiacData);
                }
            }
            redisService.setString(RedisKeyConstant.HOROSCOPE_KEY + RedisKeyConstant.DAY_KEY + "rank:" + type + ":en-us", scoresByType.toJSONString());
            redisService.setString(RedisKeyConstant.HOROSCOPE_KEY + RedisKeyConstant.DAY_KEY + "rankInfo:" + type + ":en-us", zodiacRankings.toJSONString());
        }
        return SUCCESS;
    }

    private static String formatDate(LocalDate date, String pattern) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return date.format(formatter);
    }

    private static double getStarRating(int score) {
        if (score >= 95) return 5;
        if (score >= 90) return 4.5;
        if (score >= 85) return 4;
        if (score >= 80) return 3.5;
        if (score >= 75) return 3;
        if (score >= 70) return 2.5;
        if (score >= 65) return 2;
        if (score >= 60) return 1.5;
        return 1;
    }

}

