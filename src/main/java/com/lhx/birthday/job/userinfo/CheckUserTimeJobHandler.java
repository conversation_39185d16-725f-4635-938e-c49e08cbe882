package com.lhx.birthday.job.userinfo;

import com.lhx.birthday.entity.UserInfo;
import com.lhx.birthday.mapper.UserInfoMapper;
import com.lhx.birthday.service.IUserInfoService;
import com.lhx.birthday.service.impl.UserInfoServiceImpl;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.lhx.birthday.constant.SettingConstant.NOT_VIP;

/**
 * 检测会员过期定时任务
 */
@Component
@Slf4j
@JobHandler(value = "CheckUserTimeJobHandler")
public class CheckUserTimeJobHandler extends IJobHandler {


    @Autowired
    private UserInfoMapper userInfoMapper;

    @Autowired
    private UserInfoServiceImpl userInfoService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        List<UserInfo> expiryVipUserinfoList = userInfoMapper.getExpiryVipUserinfoList();
        for (UserInfo userInfo : expiryVipUserinfoList) {
            log.info("用户："+userInfo.getUserId()+" vip已过期");
            //移除用户vip
            userInfo.setVip(NOT_VIP);
            userInfoMapper.save(userInfo);
        }
        return SUCCESS;
    }
}
