package com.lhx.birthday.job.order;

import com.lhx.birthday.entity.OrderSequence;
import com.lhx.birthday.entity.UserPaySubscribe;
import com.lhx.birthday.entity.pay.PayOrder;
import com.lhx.birthday.mapper.OrderSequenceMapper;
import com.lhx.birthday.mapper.PayOrderMapper;
import com.lhx.birthday.mapper.UserPaySubscribeMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> lhx
 * @date 2023/10/23 10:30
 */
@Component
@Slf4j
@JobHandler(value = "OrderJobTimeJobHandler")
public class OrderJobTimeJob<PERSON>and<PERSON> extends IJobHandler  {

    @Autowired
    private OrderSequenceMapper orderSequenceMapper;

    @Autowired
    private UserPaySubscribeMapper userPaySubscribeMapper;

    @Autowired
    private PayOrderMapper payOrderMapper;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        OrderSequence orderSequence = orderSequenceMapper.findById(1L).get();
        Integer iosVal = orderSequence.getIosVal();
        Integer wxVal = orderSequence.getWxVal();

        // 查询ios支付订单
        List<UserPaySubscribe> userPaySubscribeList = userPaySubscribeMapper.findBySequence(iosVal);
        for (UserPaySubscribe userPaySubscribe : userPaySubscribeList) {
            Integer userPaySubscribeId = userPaySubscribe.getId();
            Long userId = userPaySubscribe.getUserId();

            UserPaySubscribe lastBySequence = userPaySubscribeMapper.findLastBySequence(userPaySubscribeId, userId);
            if(Objects.isNull(lastBySequence)){
                if(Objects.nonNull(userPaySubscribe.getIsTrialPeriod())){
                    userPaySubscribe.setSubCount(1);
                }else{
                    userPaySubscribe.setSubCount(0);
                }
                userPaySubscribe.setPayCount(1);
            }else{
                if(Objects.nonNull(userPaySubscribe.getIsTrialPeriod())){
                    userPaySubscribe.setSubCount(lastBySequence.getSubCount() + 1);
                }else{
                    userPaySubscribe.setSubCount(lastBySequence.getSubCount());
                }
                userPaySubscribe.setPayCount(lastBySequence.getPayCount() + 1);
            }
            iosVal = userPaySubscribeId;
            userPaySubscribeMapper.saveAndFlush(userPaySubscribe);
        }
        // 查询wechat支付订单
        List<PayOrder> payOrderList = payOrderMapper.findBySequence(wxVal);
        for (PayOrder payOrder : payOrderList) {
            Long payOrderId = payOrder.getId();
            Long userId = payOrder.getUserId();

            PayOrder lastBySequence = payOrderMapper.findLastBySequence(payOrderId, userId);
            if(Objects.isNull(lastBySequence)){
                payOrder.setPayCount(1);
            }else{
                payOrder.setPayCount(lastBySequence.getPayCount() + 1);
            }
            wxVal = Math.toIntExact(payOrderId);
            payOrderMapper.saveAndFlush(payOrder);
        }

        orderSequence.setIosVal(iosVal);
        orderSequence.setWxVal(wxVal);
        orderSequenceMapper.saveAndFlush(orderSequence);
        return SUCCESS;
    }

}
