// This file is auto-generated, don't edit it. Thanks.
package com.lhx.birthday.job.matket;

import cn.hutool.core.collection.CollUtil;
import com.lhx.birthday.entity.MarketRewardPoint;
import com.lhx.birthday.entity.UserInfo;
import com.lhx.birthday.mapper.MarketRewardPointMapper;
import com.lhx.birthday.mapper.UserInfoMapper;
import com.lhx.birthday.service.impl.MarketRewardPointServiceImpl;
import com.lhx.birthday.service.impl.MarketRewardVipServiceImpl;
import com.lhx.birthday.util.SafeUtil;
import com.lhx.birthday.vo.UserInfoVO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @desc 定时任务服务
 * @date 2023-11-25
 */
//@Component
//@Slf4j
//@RestController
//@Api(description = "app接口 - 营销", tags = "MarketVipController")
//@JobHandler(value = "MarketVipTimeJobHandler")
public class MarketVipTimeJobHandler extends IJobHandler {

    @Autowired
    UserInfoMapper userInfoMapper;

    @Autowired
    MarketRewardVipServiceImpl marketRewardVipService;

    public static final Integer CRON_PAGE_SIZE = 1;

//    @Override
//    @ApiOperation("下发会员")
//    @PostMapping("/market/cron/check-reward-vip-job")
    public ReturnT<String> execute(String s) throws Exception {
//        log.info("[MarketCronService.marketCheckRewardVipAll] ");
        Long minId = 0L;
        while (minId != null) {
            minId = this.marketCheckRewardVipPage(minId);
        }
        return SUCCESS;
    }

    public Long marketCheckRewardVipPage(Long minId) {
//        log.info("[MarketCronService.marketCheckRewardVipPage] ");
        List<Long> idList = userInfoMapper.getVipHoursIdList(minId, CRON_PAGE_SIZE);
        if (CollUtil.isEmpty(idList)) {
            return null;
        }
        for (Long id : idList) {
            try {
                Optional<UserInfo> userInfoOptional = userInfoMapper.findById(id);
                if (!userInfoOptional.isPresent()) {
                    continue;
                }
                marketCheckRewardVipOne(userInfoOptional.get());
            } catch (Exception e) {
//                log.error("[MarketCronVipService.marketCheckRewardVipPage][ERROR] e=", e);
            }
        }
        return idList.get(idList.size() - 1);
    }

    public void marketCheckRewardVipOne(UserInfo user) {
        UserInfoVO userInfoVO = new UserInfoVO();
        BeanUtils.copyProperties(user ,userInfoVO);
        marketRewardVipService.resetUserVip(userInfoVO);
    }

}
