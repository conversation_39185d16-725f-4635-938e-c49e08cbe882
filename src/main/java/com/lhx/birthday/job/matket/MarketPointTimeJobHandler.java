// This file is auto-generated, don't edit it. Thanks.
package com.lhx.birthday.job.matket;

import cn.hutool.core.collection.CollUtil;
import com.lhx.birthday.entity.MarketRewardPoint;
import com.lhx.birthday.entity.UserInfo;
import com.lhx.birthday.mapper.MarketRewardPointMapper;
import com.lhx.birthday.mapper.UserInfoMapper;
import com.lhx.birthday.service.impl.MarketRewardPointServiceImpl;
import com.lhx.birthday.util.SafeUtil;
import com.lhx.birthday.vo.UserInfoVO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @desc 定时任务服务
 * @date 2023-11-25
 */
@Component
@Slf4j
@JobHandler(value = "MarketPointTimeJobHandler")
public class MarketPointTimeJobHandler extends IJobHandler {

    @Autowired
    UserInfoMapper userInfoMapper;

    @Autowired
    MarketRewardPointMapper marketRewardPointMapper;

    @Autowired
    MarketRewardPointServiceImpl marketRewardPointService;

    @Value("#{T(java.lang.Integer).parseInt('${app.market.point-expiry-month:12}')}")
    private Integer pointExpiryMonth;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        List<Long> userIdList = marketRewardPointMapper.getUserIdListHasExpiry(pointExpiryMonth);
        if (CollUtil.isNotEmpty(userIdList)) {
            userIdList.forEach(userId -> {
                Optional<UserInfo> userInfoOptional = userInfoMapper.findById(userId);
                userInfoOptional.ifPresent(this::pointExpiryCheckUser);
            });
        }
        return SUCCESS;
    }

    void pointExpiryCheckUser(UserInfo user) {
        List<MarketRewardPoint> expiryList = marketRewardPointMapper.getExpiryList(user.getUserId(), pointExpiryMonth);
        if (CollUtil.isEmpty(expiryList)) {
            return;
        }
        Integer point = 0;
        for (MarketRewardPoint entity : expiryList) {
            point += SafeUtil.of(entity.getValidValue());
        }
        if (point <= 0) {
            return;
        }

        log.info("[MarketCronService.pointExpiryCheckUser] user={}, expiry point={}", user.getUserId(), point);

        for (MarketRewardPoint entity : expiryList) {
            entity.setValidValue(0);
            marketRewardPointMapper.save(entity);
        }
        UserInfoVO userInfoVO = new UserInfoVO();
        BeanUtils.copyProperties(user ,userInfoVO);

        MarketRewardPoint newEntity = marketRewardPointService.createExpiryEntity(userInfoVO, -point);
        marketRewardPointService.updateUserPoint(userInfoVO, newEntity);
    }

}
