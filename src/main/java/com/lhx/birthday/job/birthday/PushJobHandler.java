package com.lhx.birthday.job.birthday;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.houbb.opencc4j.core.impl.ZhConvertBootstrap;
import com.lhx.birthday.constant.RedisKeyConstant;
import com.lhx.birthday.entity.Push;
import com.lhx.birthday.redis.RedisService;
import com.lhx.birthday.service.IJPushService;
import com.xkzhangsan.time.LunarDate;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 用户数据推送（生日提醒）
 * 按用户语言发送：默认简体，繁体转换，英文预留
 */
@Component
@Slf4j
@JobHandler(value = "PushJobHandler")
public class PushJobHandler extends IJobHandler {

    @Autowired
    private RedisService redisService;

    @Autowired
    private IJPushService pushService;

    private static final ZhConvertBootstrap ZH_CONVERTER = ZhConvertBootstrap.newInstance();

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            sendPromptReminders();
        } catch (Exception e) {
            log.error("PushJobHandler execute error", e);
        }
        return SUCCESS;
    }

    private Set<String> getIdSet(String jsonString) {
        Set<String> idSet = new HashSet<>();
        JSONArray array = JSONArray.parseArray(jsonString);
        for (int j = 0; j < array.size(); j++) {
            idSet.add(String.valueOf(array.getInteger(j)));
        }
        return idSet;
    }

    public static String processString(String input) {
        final int maxLength = 50;
        if (input.length() > maxLength) {
            return input.substring(0, maxLength - 3) + "...";
        } else if (input.length() < maxLength) {
            StringBuilder sb = new StringBuilder(input);
            while (sb.length() < maxLength) {
                sb.append(" ");
            }
            return sb.toString();
        } else {
            return input;
        }
    }

    private void sendPush(Push push, List<String> pushIds) {
        Iterator<String> iterator = pushIds.iterator();
        while (iterator.hasNext()) {
            List<String> subList = new ArrayList<>();
            for (int k = 0; k < 1000 && iterator.hasNext(); k++) {
                subList.add(iterator.next());
            }
            pushService.pushIos(push, subList.toArray(new String[0]));
            push.setAlert(processString(push.getAlert()));
            pushService.pushAndroid(push, subList.toArray(new String[0]));
        }
    }

    private void sendPromptReminders() {
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");

        LunarDate lunarDate = LunarDate.now();
        String solarTime = LocalDate.now().format(dateFormatter) + ":" + timeFormatter.format(LocalTime.now());
        String lunarTime = lunarDate.getlYearCn() + "-" + lunarDate.getLeapMonthCn() + lunarDate.getlMonthCn() + "-" + lunarDate.getlDayCn() + ":" + timeFormatter.format(LocalTime.now());

        // 简体中文文案（繁体由转换器生成）；英文标题如下
        String title = "生日提醒";
        String titleEn = "Birthday Reminder";

        // 简体key
        sendReminderByLanguage(solarTime, RedisKeyConstant.USER_PUSH_SOLAR_DAILY_PROMPT, title, "今天是xx公历生日");
        sendReminderByLanguage(solarTime, RedisKeyConstant.USER_PUSH_SOLAR_ONE_DAY_PROMPT, title, "距离xx公历生日还有1天");
        sendReminderByLanguage(solarTime, RedisKeyConstant.USER_PUSH_SOLAR_THREE_DAY_PROMPT, title, "距离xx公历生日还有3天");
        sendReminderByLanguage(solarTime, RedisKeyConstant.USER_PUSH_SOLAR_SEVEN_DAY_PROMPT, title, "距离xx公历生日还有7天");
        sendReminderByLanguage(solarTime, RedisKeyConstant.USER_PUSH_SOLAR_FIFTEEN_DAY_PROMPT, title, "距离xx公历生日还有15天");
        sendReminderByLanguage(solarTime, RedisKeyConstant.USER_PUSH_SOLAR_THIRTY_DAY_PROMPT, title, "距离xx公历生日还有30天");
        // 繁体key（写入时使用 :hant 后缀）
        sendReminderForHant(solarTime, RedisKeyConstant.USER_PUSH_SOLAR_DAILY_PROMPT, title, "今天是xx公历生日");
        sendReminderForHant(solarTime, RedisKeyConstant.USER_PUSH_SOLAR_ONE_DAY_PROMPT, title, "距离xx公历生日还有1天");
        sendReminderForHant(solarTime, RedisKeyConstant.USER_PUSH_SOLAR_THREE_DAY_PROMPT, title, "距离xx公历生日还有3天");
        sendReminderForHant(solarTime, RedisKeyConstant.USER_PUSH_SOLAR_SEVEN_DAY_PROMPT, title, "距离xx公历生日还有7天");
        sendReminderForHant(solarTime, RedisKeyConstant.USER_PUSH_SOLAR_FIFTEEN_DAY_PROMPT, title, "距离xx公历生日还有15天");
        sendReminderForHant(solarTime, RedisKeyConstant.USER_PUSH_SOLAR_THIRTY_DAY_PROMPT, title, "距离xx公历生日还有30天");
        // 英文桶（:en）先用简体中文内容
        sendReminderForEn(solarTime, RedisKeyConstant.USER_PUSH_SOLAR_DAILY_PROMPT, titleEn, "Today is XX's solar calendar birthday.");
        sendReminderForEn(solarTime, RedisKeyConstant.USER_PUSH_SOLAR_ONE_DAY_PROMPT, titleEn, "There is 1 day left until xx's Gregorian birthday.");
        sendReminderForEn(solarTime, RedisKeyConstant.USER_PUSH_SOLAR_THREE_DAY_PROMPT, titleEn, "There are 3 days left until xx's Gregorian birthday.");
        sendReminderForEn(solarTime, RedisKeyConstant.USER_PUSH_SOLAR_SEVEN_DAY_PROMPT, titleEn, "There are 7 days left until xx's Gregorian birthday.");
        sendReminderForEn(solarTime, RedisKeyConstant.USER_PUSH_SOLAR_FIFTEEN_DAY_PROMPT, titleEn, "There are 15 days left until xx's Gregorian birthday.");
        sendReminderForEn(solarTime, RedisKeyConstant.USER_PUSH_SOLAR_THIRTY_DAY_PROMPT, titleEn, "There are 30 days left until xx's Gregorian birthday.");

        sendReminderByLanguage(lunarTime, RedisKeyConstant.USER_PUSH_LUNAR_DAILY_PROMPT, title, "今天是xx农历生日");
        sendReminderByLanguage(lunarTime, RedisKeyConstant.USER_PUSH_LUNAR_ONE_DAY_PROMPT, title, "距离xx农历生日还有1天");
        sendReminderByLanguage(lunarTime, RedisKeyConstant.USER_PUSH_LUNAR_THREE_DAY_PROMPT, title, "距离xx农历生日还有3天");
        sendReminderByLanguage(lunarTime, RedisKeyConstant.USER_PUSH_LUNAR_SEVEN_DAY_PROMPT, title, "距离xx农历生日还有7天");
        sendReminderByLanguage(lunarTime, RedisKeyConstant.USER_PUSH_LUNAR_FIFTEEN_DAY_PROMPT, title, "距离xx农历生日还有15天");
        sendReminderByLanguage(lunarTime, RedisKeyConstant.USER_PUSH_LUNAR_THIRTY_DAY_PROMPT, title, "距离xx农历生日还有30天");
        sendReminderForHant(lunarTime, RedisKeyConstant.USER_PUSH_LUNAR_DAILY_PROMPT, title, "今天是xx农历生日");
        sendReminderForHant(lunarTime, RedisKeyConstant.USER_PUSH_LUNAR_ONE_DAY_PROMPT, title, "距离xx农历生日还有1天");
        sendReminderForHant(lunarTime, RedisKeyConstant.USER_PUSH_LUNAR_THREE_DAY_PROMPT, title, "距离xx农历生日还有3天");
        sendReminderForHant(lunarTime, RedisKeyConstant.USER_PUSH_LUNAR_SEVEN_DAY_PROMPT, title, "距离xx农历生日还有7天");
        sendReminderForHant(lunarTime, RedisKeyConstant.USER_PUSH_LUNAR_FIFTEEN_DAY_PROMPT, title, "距离xx农历生日还有15天");
        sendReminderForHant(lunarTime, RedisKeyConstant.USER_PUSH_LUNAR_THIRTY_DAY_PROMPT, title, "距离xx农历生日还有30天");
        sendReminderForEn(lunarTime, RedisKeyConstant.USER_PUSH_LUNAR_DAILY_PROMPT, titleEn, "Today is XX's Lunar birthday.");
        sendReminderForEn(lunarTime, RedisKeyConstant.USER_PUSH_LUNAR_ONE_DAY_PROMPT, titleEn, "There is 1 day left until xx's Lunar birthday.");
        sendReminderForEn(lunarTime, RedisKeyConstant.USER_PUSH_LUNAR_THREE_DAY_PROMPT, titleEn, "There are 3 days left until xx's Lunar birthday.");
        sendReminderForEn(lunarTime, RedisKeyConstant.USER_PUSH_LUNAR_SEVEN_DAY_PROMPT, titleEn, "There are 7 days left until xx's Lunar birthday.");
        sendReminderForEn(lunarTime, RedisKeyConstant.USER_PUSH_LUNAR_FIFTEEN_DAY_PROMPT, titleEn, "There are 15 days left until xx's Lunar birthday.");
        sendReminderForEn(lunarTime, RedisKeyConstant.USER_PUSH_LUNAR_THIRTY_DAY_PROMPT, titleEn, "There are 30 days left until xx's Lunar birthday.");
    }

    private void sendReminderByLanguage(String scheduleTime, String key, String baseTitle, String messagePrefix) {
        String reminderStr = redisService.hget(key, scheduleTime);
        if (Objects.nonNull(reminderStr)) {
            JSONArray jsonArray = JSONArray.parseArray(reminderStr);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String alert = messagePrefix.replace("xx", jsonObject.getString("msg"));
                Set<String> idSet = getIdSet(jsonObject.getJSONArray("id").toJSONString());
                List<String> pushIds = new ArrayList<>(idSet);

                // 简体用户
                if (!pushIds.isEmpty()) {
                    Push push = createPush(baseTitle, alert, "tabbar", "2");
                    sendPush(push, pushIds);
                }
                // 英文（预留未来，此处暂不区分）
            }
        }
    }

    private void sendReminderForHant(String scheduleTime, String key, String baseTitle, String messagePrefix) {
        String reminderStr = redisService.hget(key + ":hant", scheduleTime);
        if (Objects.nonNull(reminderStr)) {
            JSONArray jsonArray = JSONArray.parseArray(reminderStr);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String alert = messagePrefix.replace("xx", jsonObject.getString("msg"));
                Set<String> idSet = getIdSet(jsonObject.getJSONArray("id").toJSONString());
                List<String> pushIds = new ArrayList<>(idSet);

                // 繁体（转换简体文案）
                Push push = createPush(convertToTraditional(baseTitle), convertToTraditional(alert), "tabbar", "2");
                sendPush(push, pushIds);
            }
        }
    }

    private void sendReminderForEn(String scheduleTime, String key, String baseTitle, String messagePrefix) {
        String reminderStr = redisService.hget(key + ":en", scheduleTime);
        if (Objects.nonNull(reminderStr)) {
            JSONArray jsonArray = JSONArray.parseArray(reminderStr);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String alert = messagePrefix.replace("xx", jsonObject.getString("msg"));
                Set<String> idSet = getIdSet(jsonObject.getJSONArray("id").toJSONString());
                List<String> pushIds = new ArrayList<>(idSet);

                // 英文桶暂用简体
                Push push = createPush(baseTitle, alert, "tabbar", "2");
                sendPush(push, pushIds);
            }
        }
    }

    private String convertToTraditional(String text) {
        try {
            return ZH_CONVERTER.toTraditional(text);
        } catch (Exception e) {
            return text;
        }
    }

    public static Push createPush(String title, String alert, String type, String link) {
        Push push = new Push();
        push.setTitle(title);
        push.setAlert(alert);
        Map<String, String> extras = new HashMap<>();
        extras.put("type", type);
        extras.put("link", link);
        extras.put("param", "{}");
        push.setExtras(extras);
        return push;
    }

}
