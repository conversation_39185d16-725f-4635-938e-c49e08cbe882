package com.lhx.birthday.job.birthday;

import com.lhx.birthday.entity.ProfilePrompt;
import com.lhx.birthday.entity.UserInfo;
import com.lhx.birthday.enums.ActionUnit;
import com.lhx.birthday.mapper.ProfilePromptMapper;
import com.lhx.birthday.mapper.UserInfoMapper;
import com.lhx.birthday.service.IUserInfoService;
import com.lhx.birthday.service.impl.UserInfoServiceImpl;
import com.lhx.birthday.vo.UserInfoVO;
import com.tyme.lunar.LunarDay;
import com.xkzhangsan.time.LunarDate;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.lhx.birthday.constant.SettingConstant.NOT_VIP;
import static com.lhx.birthday.service.impl.ProfileServiceImpl.calculateNextBirthday;
import static com.lhx.birthday.service.impl.ProfileServiceImpl.calculateNextLunarBirthday;

/**
 * 检测生日过期任务
 */
@Component
@Slf4j
@JobHandler(value = "CheckBirthdayTimeJobHandler")
public class CheckBirthdayTimeJobHandler extends IJobHandler {

    @Autowired
    private ProfilePromptMapper profilePromptMapper;

    @Autowired
    private IUserInfoService userInfoService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 处理公历生日 solar
        LocalDate localDate = LocalDate.now().minusDays(1);
        String solarBirthDay = localDate.format(formatter);

        // 处理农历生日 lunar
        LunarDate lunarDate = LunarDate.from(LunarDate.now().minus(1, ChronoUnit.DAYS));
        String lunarBirthDay = lunarDate.getlYear() + "-" + lunarDate.getlMonth() + "-" + lunarDate.getlDay();

        List<ProfilePrompt> profilePromptList = profilePromptMapper.findByBirthday(lunarBirthDay, solarBirthDay);

        for (ProfilePrompt prompt : profilePromptList) {
            ProfilePrompt oldProfilePrompt = new ProfilePrompt();
            BeanUtils.copyProperties(prompt,oldProfilePrompt);
            // 重新下发生日和推送信息
            if(prompt.getNextSolarTime().equals(solarBirthDay)){
                Map<String, String> solarMap = calculateNextBirthday(prompt.getSolarTime().toLocalDate(),LocalDate.now());
                String solarNextBirthday = solarMap.get("nextBirthday");
                prompt.setNextSolarTime(solarNextBirthday);

//                oldProfilePrompt.setSolarTime(prompt.getSolarTime().minusDays(1));
            }
            if(prompt.getNextLunarTime().equals(lunarBirthDay)){
                String[] lunarSpl = prompt.getLunarTime().split(" ")[0].split("-");
                Map<String, String> lunarMap = calculateNextLunarBirthday(Integer.parseInt(lunarSpl[0]),Integer.parseInt(lunarSpl[1]),Integer.parseInt(lunarSpl[2]),LocalDate.now());
                String lunarNextBirthday = lunarMap.get("nextBirthday");
                prompt.setNextLunarTime(lunarNextBirthday);

//                LunarDate oldLunarDate = LunarDate.from(LunarDate.of(Integer.parseInt(lunarSpl[0]), Integer.parseInt(lunarSpl[1]), Integer.parseInt(lunarSpl[2])).minus(1, ChronoUnit.DAYS));
//                oldProfilePrompt.setLunarTime(oldLunarDate.getlYear()+"-"+oldLunarDate.getlMonth()+"-"+oldLunarDate.getlDay()+ " " +prompt.getLunarTime().split(" ")[1]);
            }
            // 更新档案推送
            UserInfoVO userInfoVO = userInfoService.getByUserId(prompt.getUserId());
            if(Objects.nonNull(userInfoVO.getRegistrationId())){
                userInfoService.initPushData(Collections.singletonList(oldProfilePrompt), ActionUnit.DELETE,1);
                userInfoService.initPushData(Collections.singletonList(prompt), ActionUnit.ADD,null);
            }
            profilePromptMapper.saveAndFlush(prompt);
        }
        return SUCCESS;
    }

}
