package com.lhx.birthday.job;

import com.lhx.birthday.service.IAstrolabeService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.YearMonth;

@Component
@Slf4j
@JobHandler(value = "AstroCalendarJob")
public class AstroCalendarJob extends IJobHandler {

    @Autowired
    private IAstrolabeService astrolabeService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("AstroCalendarJob start: fetching data for 6 months ahead.");
        
        // Calculate the target month, which is 6 months from now.
        YearMonth targetYearMonth = YearMonth.from(LocalDate.now().plusMonths(6));
        int year = targetYearMonth.getYear();
        int month = targetYearMonth.getMonthValue();

        try {
            astrolabeService.fetchAndSaveAstroCalendar(year, month);
            log.info("Successfully fetched and saved astro calendar for {}-{}", year, month);
        } catch (Exception e) {
            log.error("Failed to process astro calendar for {}-{}", year, month, e);
            return FAIL; // Return FAIL to indicate the job execution failed.
        }
        
        log.info("AstroCalendarJob finished successfully.");
        return SUCCESS;
    }

//    @Scheduled(fixedRate = 1000 * 60 * 5000)
//    public void exc(){
//        log.info("AstroCalendarJob start: fetching data for 6 months ahead.");
//
//        // Calculate the target month, which is 6 months from now.
//        YearMonth targetYearMonth = YearMonth.from(LocalDate.now().plusMonths(6));
//        int year = targetYearMonth.getYear();
//        int month = targetYearMonth.getMonthValue();
//
//        try {
//            astrolabeService.fetchAndSaveAstroCalendar(year, month);
//            log.info("Successfully fetched and saved astro calendar for {}-{}", year, month);
//        } catch (Exception e) {
//            log.error("Failed to process astro calendar for {}-{}", year, month, e);
//        }
//
//        log.info("AstroCalendarJob finished successfully.");
//    }
} 