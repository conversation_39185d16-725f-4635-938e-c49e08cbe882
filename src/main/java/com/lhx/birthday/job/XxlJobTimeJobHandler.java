package com.lhx.birthday.job;

import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR> lhx
 * @date 2023/10/23 10:30
 */
@Component
@Profile("prod")
public class XxlJobTimeJobHandler {

//    @Autowired
//    private BirthdayInfoMapper birthdayInfoMapper;

    @Scheduled(fixedRate = 1000 * 60 * 5)
    @Async("taskExecutor")
    @Transactional
    public void insertMessage() {
//        try {
//            // 1月1日 情绪的组织者 宫位：魔羯座10度-12度 星座：魔羯座一，本位的土象
//            birthdayInfoMapper.findAll().forEach(birthdayInfo -> {
//                JSONObject jsonObject = JSONObject.parseObject(birthdayInfo.getInfo());
//                String string = jsonObject.getString("简介").replace(",","，");
//                jsonObject.put("简介",string);
//                jsonObject.remove("month");
//                jsonObject.remove("day");
//
////                JSONObject json = new JSONObject();
//                jsonObject.put("元素",string.split("，")[1].split(" ")[0].split("4")[0].split("在")[0]);
//                jsonObject.put("特性",string.split(" ")[1]);
//                jsonObject.put("宫位",string.split(" ")[2].replace("宫位：",""));
//                jsonObject.put("星座",string.split(" ")[3].replace("星座：","").split("，")[0]);
//                birthdayInfo.setInfo(jsonObject.toJSONString());
//                birthdayInfoMapper.saveAndFlush(birthdayInfo);
//            });
//        }catch (Exception e){
//            e.printStackTrace();
//        }
    }
}
