package com.lhx.birthday.vo.profile;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "关注地区响应体",description = "关注地区响应体")
public class RegionVO {


//    @ApiModelProperty(value = "中文名称", name = "chn")
//    private String chn;

//    @ApiModelProperty(value = "英文编码", name = "eng")
//    private String eng;

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "父ID")
    private Long parentId;

    @ApiModelProperty(value = "层级")
    private Integer level;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "英文编码")
    private String countryEng;

    @ApiModelProperty(value = "英文编码")
    private String level1Eng;

    @ApiModelProperty(value = "英文编码")
    private String level2Eng;

    @ApiModelProperty(value = "英文编码")
    private String level3Eng;

    @ApiModelProperty(value = "中文编码")
    private String countryChn;

    @ApiModelProperty(value = "中文编码")
    private String level1Chn;

    @ApiModelProperty(value = "中文编码")
    private String level2Chn;

    @ApiModelProperty(value = "中文编码")
    private String level3Chn;

    private String isoCountryCode;

    @ApiModelProperty(value = "经度")
    private BigDecimal lon;

    @ApiModelProperty(value = "纬度")
    private BigDecimal lat;

//    @ApiModelProperty(value = "区域信息", name = "countryInfos")
//    private List<RegionLevel1VO> regions;

}
