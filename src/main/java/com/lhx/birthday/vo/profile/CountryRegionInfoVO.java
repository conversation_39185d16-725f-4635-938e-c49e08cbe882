package com.lhx.birthday.vo.profile;

import com.lhx.birthday.enums.DefaultFlag;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "关注地区响应体",description = "关注地区响应体")
public class CountryRegionInfoVO {

    @ApiModelProperty(value = "ID", name = "id")
    private Long id;

    @ApiModelProperty(value = "中文名称", name = "chn")
    private String chn;

    @ApiModelProperty(value = "英文编码", name = "eng")
    private String eng;




}
