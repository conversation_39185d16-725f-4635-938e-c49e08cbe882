package com.lhx.birthday.vo.profile;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lhx.birthday.enums.*;
import com.lhx.birthday.util.LocalDateTimeDeserializer;
import com.lhx.birthday.util.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Enumerated;
import java.time.LocalDateTime;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "档案关系响应体",description = "档案关系响应体")
public class RelationshipVO {

    @ApiModelProperty(name = "关系值", value = "关系的值")
    private Integer value;

    @ApiModelProperty(name = "关系名称", value = "关系的名称")
    private String name;

    /**
     * 是否可选 0:否 1:是
     */
    @ApiModelProperty(name = "是否可选 0:否 1:是", value = "是否可选，0表示否，1表示是")
    private DefaultFlag isSelect;


}
