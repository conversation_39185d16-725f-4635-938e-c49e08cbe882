package com.lhx.birthday.vo.profile;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lhx.birthday.enums.*;
import com.lhx.birthday.util.LocalDateTimeDeserializer;
import com.lhx.birthday.util.LocalDateTimeSerializer;
import com.lhx.birthday.util.LocalTimeDeserializer;
import com.lhx.birthday.util.LocalTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Enumerated;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "人员档案响应体",description = "人员档案响应体")
public class ProfileVO {

    @ApiModelProperty(name = "档案id", value = "档案的ID")
    private Long id;

    @ApiModelProperty(value = "头像url")
    private String avatar;

    @ApiModelProperty(name = "档案姓名", value = "档案的姓名")
    private String name;

    @ApiModelProperty(value = "性别，0男 1女", notes = "0表示男性，1表示女性")
    private GenderType gender;

    /**
     * 星座
     */
    @Enumerated
    @ApiModelProperty(value = "星座")
    private ZodiacSignType zodiacSignType;

    @ApiModelProperty(value = "星座")
    private String zodiacSignTypeStr;

    @ApiModelProperty(value = "星座图片Url")
    private String zodiacSignTypePic;

    /**
     * 生肖
     */
    @Enumerated
    @ApiModelProperty(value = "生肖")
    private ZodiacType zodiacType;

    @ApiModelProperty(value = "生肖")
    private String zodiacTypeStr;

    @ApiModelProperty(value = "生肖图片Url")
    private String zodiacTypePic;

    /**
     * 档案关系
     */
    @Enumerated
    @ApiModelProperty(value = "档案关系")
    private Relationship relationship;

    @ApiModelProperty(value = "档案关系")
    private String relationshipStr;

    @ApiModelProperty(value = "生日类型 0公历 1农历")
    private BirthdayType birthdayType;

    @ApiModelProperty(name = "公历生日 格式：yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime solarTime;

    @ApiModelProperty(name = "农历生日 格式：yyyy-MM-dd HH:mm:ss")
    private String lunarTime;

    private String solarStr;
    private String solarYearStr;
    private String solarFullStr;
    private int solarNextAge;
    private int solarNextAgeDay;

    private String lunarStr;
    private String lunarYearStr;
    private String lunarFullStr;
    private int lunarNextAge;
    private int lunarNextAgeDay;

    private JSONObject birthdayInfo;

    @ApiModelProperty(value = "备注")
    private String remarkInfo;

    @ApiModelProperty(value = "列表查询如果根据分组查询，表示是否存在该分组内 0否 1是")
    private DefaultFlag inGroup;

    @ApiModelProperty(value = "生日当天")
    private DefaultFlag onTheDay;
    @ApiModelProperty(value = "提前1天")
    private DefaultFlag oneDayBefore;
    @ApiModelProperty(value = "提前3天")
    private DefaultFlag threeDaysBefore;
    @ApiModelProperty(value = "提前7天")
    private DefaultFlag sevenDaysBefore;
    @ApiModelProperty(value = "提前15天")
    private DefaultFlag fifteenDaysBefore;
    @ApiModelProperty(value = "提前30天")
    private DefaultFlag thirtyDaysBefore;

    @ApiModelProperty(value = "提醒时间")
    @JsonSerialize(using = LocalTimeSerializer.class)
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    private LocalTime promptTime;

    @ApiModelProperty(value = "双历提醒")
    private DefaultFlag promptFlag;

    @ApiModelProperty(value = "时区")
    private String timeZone;

    @ApiModelProperty(value = "分组id列表")
    private String groupIds;
}
