package com.lhx.birthday.vo.profile;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lhx.birthday.enums.GenderType;
import com.lhx.birthday.enums.Relationship;
import com.lhx.birthday.enums.ZodiacSignType;
import com.lhx.birthday.enums.ZodiacType;
import com.lhx.birthday.util.LocalDateTimeDeserializer;
import com.lhx.birthday.util.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Enumerated;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "档案分组响应体",description = "人员档案响应体")
public class ProfileGroupVO {

    @ApiModelProperty(name = "id", value = "ID")
    private Long id;

    private int count;

    @ApiModelProperty(name = "分组名称", value = "分组名称")
    private String groupName;

    @ApiModelProperty(name = "人员档案")
    private List<ProfileVO> profiles;
}
