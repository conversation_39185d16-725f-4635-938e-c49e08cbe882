package com.lhx.birthday.vo.profile;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "关注地区响应体",description = "关注地区响应体")
public class RegionLevel2VO {

    @ApiModelProperty(value = "ID", name = "id")
    private Long id;

    @ApiModelProperty(value = "中文名称", name = "chn")
    private String chn;

    @ApiModelProperty(value = "英文编码", name = "eng")
    private String eng;

    @ApiModelProperty(value = "区域信息", name = "countryInfos")
    private List<RegionLevel3VO> regions;


}
