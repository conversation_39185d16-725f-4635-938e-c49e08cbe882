package com.lhx.birthday.vo.dream;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "梦境响应体",description = "梦境响应体")
public class DreamVO {

    @ApiModelProperty(name = "梦境id", value = "梦境的ID")
    private Long id;

    @ApiModelProperty(name = "梦境标题", value = "梦境的标题")
    private String title;

}
