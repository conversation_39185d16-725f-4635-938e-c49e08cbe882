package com.lhx.birthday.vo.dream;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "梦境响应体",description = "梦境响应体")
public class DreamDetailVO {

    @ApiModelProperty(name = "梦境标题", value = "梦境的标题")
    private String title;

    @ApiModelProperty(name = "梦境分类", value = "梦境的分类")
    private String type;

    @ApiModelProperty(name = "梦境二级分类", value = "梦境的二级分类")
    private String secType;

    @ApiModelProperty(name = "梦境内容", value = "梦境的具体内容")
    private String content;

}
