package com.lhx.birthday.vo.market;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lhx.birthday.annotation.ApiEnum;
import com.lhx.birthday.annotation.ApiEnumProperty;
import com.lhx.birthday.entity.MarketRewardPoint;
import com.lhx.birthday.util.LocalDateTimeDeserializer;
import com.lhx.birthday.util.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "积分明细",description = "积分明细")
public class PointHistoryVO {

    @ApiModelProperty(name = "列表",value = "列表")
    private List<Item> list;


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "明细",description = "明细")
    public static class Item {
        @JsonSerialize(using = LocalDateTimeSerializer.class)
        @JsonDeserialize(using = LocalDateTimeDeserializer.class)
        private LocalDateTime date;
        private Integer value;

        @ApiModelProperty(name = "START5-五星好评 INVITE-邀请 INVITED-被邀请 SIGN-签到 OTHER-其他 USE-使用 EXPIRY-过期",value = "START5-五星好评 INVITE-邀请 INVITED-被邀请 SIGN-签到 OTHER-其他 USE-使用 EXPIRY-过期")
        private ItemType type;
    }

    @ApiEnum
    public enum ItemType {
        @ApiEnumProperty("五星好评")
        START5, // 五星好评
        @ApiEnumProperty("邀请")
        INVITE, // 邀请
        @ApiEnumProperty("被邀请")
        INVITED, // 被邀请
        @ApiEnumProperty("签到")
        SIGN, // 签到
        @ApiEnumProperty("其他")
        OTHER, // 其他

        @ApiEnumProperty("使用")
        USE, // 使用
        @ApiEnumProperty("过期")
        EXPIRY; // 过期
    }

    public static Item itemFrom(MarketRewardPoint entity) {
        ItemType type = null;
        switch (entity.getType()) {
            case ADD:
                if (entity.getReason() != null) {
                    switch (entity.getReason()) {
                        case START5:
                            type = ItemType.START5;
                            break;
                        case INVITE:
                            type = ItemType.INVITE;
                            break;
                        case INVITED:
                            type = ItemType.INVITED;
                            break;
                        case SIGN:
                            type = ItemType.SIGN;
                            break;
                        default:
                            type = ItemType.OTHER;
                            break;
                    }
                }
                break;
            case USE:
                type = ItemType.USE;
                break;
            case EXPIRY:
                type = ItemType.EXPIRY;
                break;
        }
        return Item.builder()
                .date(entity.getCreateTime())
                .value(entity.getInitValue())
                .type(type)
                .build();
    }

}
