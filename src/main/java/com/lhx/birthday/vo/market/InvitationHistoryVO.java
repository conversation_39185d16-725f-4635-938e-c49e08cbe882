package com.lhx.birthday.vo.market;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lhx.birthday.util.LocalDateTimeDeserializer;
import com.lhx.birthday.util.LocalDateTimeSerializer;
import com.lhx.birthday.vo.systemConfig.RewardSettingVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "营销配置",description = "营销配置")
public class InvitationHistoryVO {

    private Integer peopleCount;
    private Integer vipDays;

    private InvitationSettingVO welfare;

    private List<Item> list;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Item {
        @JsonSerialize(using = LocalDateTimeSerializer.class)
        @JsonDeserialize(using = LocalDateTimeDeserializer.class)
        private LocalDateTime date;
        private String phone;
    }

}
