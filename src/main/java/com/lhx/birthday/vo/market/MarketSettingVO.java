package com.lhx.birthday.vo.market;

import com.lhx.birthday.enums.RewardType;
import com.lhx.birthday.enums.RewardVipUnit;
import com.lhx.birthday.util.NumUtil;
import com.lhx.birthday.vo.systemConfig.RewardSettingVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "营销配置",description = "营销配置")
public class MarketSettingVO {

    @ApiModelProperty(name = "是否显示五星好评： 0 显示；1 隐藏")
    private Integer starReviewHidden;
    private String beginClock;
    private String endClock;
    private RewardSettingVO star5;

    @ApiModelProperty(name = "是否显示邀请页： 0 显示；1 隐藏")
    private Integer invitationHidden;
    private RewardSettingVO invite;
    private RewardSettingVO invited;

    @ApiModelProperty(name = "是否显示签到： 0 显示；1 隐藏")
    private Integer signHidden;

    private Integer showMarketing;

}
