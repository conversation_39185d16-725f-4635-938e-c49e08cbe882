package com.lhx.birthday.vo.market;

import com.lhx.birthday.enums.CycleUnit;
import com.lhx.birthday.vo.systemConfig.RewardSettingVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "营销订阅商品",description = "营销订阅商品")
public class ProductMarketingVO {

    @ApiModelProperty(value = "是否开启营销页，true表示开启，false表示不显示")
    private Boolean isEnable;

    @ApiModelProperty(value = "免费订阅设置")
    private Free free;

    @ApiModelProperty(value = "普通订阅设置")
    private Normal normal;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Free {
        @ApiModelProperty(value = "苹果的 product_id")
        private String productId;

        @ApiModelProperty(value = "免费天数")
        private String freeDay;

        @ApiModelProperty(value = "周期单位，可选值：week、month、quarter、year")
        private CycleUnit cycleUnit;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Normal {
        @ApiModelProperty(value = "苹果的 product_id")
        private String productId;

        @ApiModelProperty(value = "周期单位，可选值：week、month、quarter、year")
        private CycleUnit cycleUnit;
    }


}
