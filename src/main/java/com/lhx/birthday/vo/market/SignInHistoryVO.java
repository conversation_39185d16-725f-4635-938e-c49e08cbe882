package com.lhx.birthday.vo.market;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lhx.birthday.util.LocalDateTimeDeserializer;
import com.lhx.birthday.util.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "签到历史",description = "签到历史")
public class SignInHistoryVO {

    @ApiModelProperty(value = "连续签到天数")
    private Integer count;

    @ApiModelProperty(value = "今天是否签到，0未签到；1已签到")
    private Integer state;

    @ApiModelProperty(value = "每周签到情况")
    private List<Item> weeks;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Item {
        @ApiModelProperty(value = "日期")
        private String date;

        @ApiModelProperty(value = "星期几")
        private Integer week;

        @ApiModelProperty(value = "积分")
        private Integer integral;

        @ApiModelProperty(value = "是否签到，0未签到；1已签到")
        private Integer state;

        @ApiModelProperty(value = "顺序")
        private Integer seq;

        public boolean checkSign() {
            return state == 1;
        }
    }


}
