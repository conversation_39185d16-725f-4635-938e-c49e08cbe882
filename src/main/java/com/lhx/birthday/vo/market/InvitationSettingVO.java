package com.lhx.birthday.vo.market;

import com.alibaba.fastjson.JSONObject;
import com.lhx.birthday.enums.RewardType;
import com.lhx.birthday.enums.RewardVipUnit;
import com.lhx.birthday.vo.systemConfig.RewardSettingVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/12/2
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvitationSettingVO {

    /**
     * 邀请人
     */
    @ApiModelProperty(value = "邀请人")
    private RewardSettingVO inviter;

    /**
     * 被邀请人
     */
    @ApiModelProperty(value = "被邀请人")
    private RewardSettingVO invitee;

    public static final InvitationSettingVO from(JSONObject jsonObject) {
        RewardSettingVO inviter = RewardSettingVO.builder()
                .type(RewardType.valueOf(jsonObject.getString("invite_reward_type")))
//                .unit(entity.getInviteRewardUnit())
                .value(jsonObject.getIntValue("invite_reward_value"))
                .build();
        RewardSettingVO invitee = RewardSettingVO.builder()
                .type(RewardType.valueOf(jsonObject.getString("invited_reward_type")))
//                .unit(entity.getInvitedRewardUnit())
                .value(jsonObject.getIntValue("invited_reward_value"))
                .build();
        if(Objects.nonNull(jsonObject.getString("invite_reward_unit"))){
            inviter.setUnit(RewardVipUnit.valueOf(jsonObject.getString("invite_reward_unit")));
        }
        if(Objects.nonNull(jsonObject.getString("invited_reward_unit"))){
            invitee.setUnit(RewardVipUnit.valueOf(jsonObject.getString("invited_reward_unit")));
        }

        return InvitationSettingVO.builder()
                .inviter(inviter)
                .invitee(invitee)
                .build();
    }
}
