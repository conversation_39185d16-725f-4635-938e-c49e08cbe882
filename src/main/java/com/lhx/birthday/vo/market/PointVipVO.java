package com.lhx.birthday.vo.market;

import com.lhx.birthday.enums.RewardVipUnit;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "积分商品",description = "积分商品")
public class PointVipVO {

    private String productId;
    private RewardVipUnit vipUnit;
    private Integer vipValue;

    private Integer pointValue;


}
