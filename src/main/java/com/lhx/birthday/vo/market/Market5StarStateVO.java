package com.lhx.birthday.vo.market;

import com.lhx.birthday.enums.DefaultFlag;
import com.lhx.birthday.enums.RewardType;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "5星好评状态",description = "5星好评状态")
public class Market5StarStateVO {

    private DefaultFlag state; // 0:未参与 1:已参与


}
