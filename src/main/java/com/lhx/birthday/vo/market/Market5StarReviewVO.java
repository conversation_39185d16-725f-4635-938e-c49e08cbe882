package com.lhx.birthday.vo.market;

import com.lhx.birthday.enums.RewardType;
import com.lhx.birthday.vo.systemConfig.RewardSettingVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "营销配置",description = "营销配置")
public class Market5StarReviewVO {

    private RewardType type;
    private Integer value;


}
