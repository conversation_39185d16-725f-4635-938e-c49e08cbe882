package com.lhx.birthday.vo.market;

import com.lhx.birthday.vo.TvMemberRechargeTypeVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@ApiModel("挽回设置响应")
public class RecoverSettingVO {

    @ApiModelProperty("倒计时时间(秒)")
    private Integer countdown;

    @ApiModelProperty("主商品信息")
    private TvMemberRechargeTypeVO code;

}