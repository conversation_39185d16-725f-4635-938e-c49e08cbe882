package com.lhx.birthday.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 性格测试问题视图对象
 * <AUTHOR> lhx
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PersonalityQuestionVO {
    
    /**
     * 问题ID
     */
    private Integer questionId;
    
    /**
     * 问题文本
     */
    private String questionText;
    
    /**
     * 选项列表
     */
    private List<Option> options;
    
    /**
     * 选项视图对象
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Option {
        
        /**
         * 选项字母
         */
        private Character optionLetter;
        
        /**
         * 选项文本
         */
        private String optionText;
    }
} 