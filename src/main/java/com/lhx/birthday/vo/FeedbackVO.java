package com.lhx.birthday.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "省份信息响应体",description = "省份信息响应体")
public class FeedbackVO {

    @ApiModelProperty(name = "省份id",value = "省份id")
    private Long id;

    @ApiModelProperty(name = "系统")
    private String system;

    @ApiModelProperty(name = "联系方式")
    private String contact;

    @ApiModelProperty(name = "反馈内容")
    private String content;

    @ApiModelProperty(name = "机器名")
    private String machine;

    @ApiModelProperty(name = "系统版本")
    private String systemVersion;
}
