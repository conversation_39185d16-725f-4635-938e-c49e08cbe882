package com.lhx.birthday.vo.constellation;

import com.lhx.birthday.enums.BirthdayType;
import com.lhx.birthday.enums.ConstellationRelationship;
import com.lhx.birthday.enums.GenderType;
import com.lhx.birthday.enums.ZodiacSignType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.time.LocalDateTime;

@Data
@ApiModel(value = "星座档案VO")
public class ConstellationProfileVO {
    
    @ApiModelProperty(value = "ID")
    private Long id;
    
    @ApiModelProperty(value = "用户ID")
    private Long userId;
    
    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "性别 0男 1女")
    private GenderType gender;
    
    @ApiModelProperty(value = "档案关系")
    @Enumerated
    private ConstellationRelationship relationship;

    @ApiModelProperty(value = "档案关系")
    private String relationshipStr;

    @ApiModelProperty(name = "生日 格式：yyyy-MM-dd HH:mm:ss")
    private String birthdayTime;

    @ApiModelProperty(value = "生日类型 0公历")
    private BirthdayType birthdayType;
    
    @ApiModelProperty(value = "出生地点")
    private String birthPlaceId;
    
    @ApiModelProperty(value = "居住地点")
    private String livingPlaceId;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "时区")
    private String timeZone;

    @Enumerated
    @ApiModelProperty(value = "星座")
    private ZodiacSignType zodiacSignType;

    @ApiModelProperty(value = "星座")
    private String zodiacSignTypeStr;

    @ApiModelProperty(value = "星座图片Url")
    private String zodiacSignTypePic;

    private CountryCodeVO birthPlace;

    private CountryCodeVO livingPlace;

    @ApiModelProperty(value = "太阳星座")
    private String sunSign;

    @ApiModelProperty(value = "月亮星座")
    private String moonSign;

    @ApiModelProperty(value = "上升星座")
    private String ascendantSign;
}