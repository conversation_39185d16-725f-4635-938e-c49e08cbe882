package com.lhx.birthday.vo.constellation;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class CountryCodeVO {
    private Long id;
    private String districtId;
    private String countryEng;
    private String level1Eng;
    private String level2Eng;
    private String level3Eng;
    private String countryChn;
    private String level1Chn;
    private String level2Chn;
    private String level3Chn;
    private String isoCountryCode;
    private BigDecimal lon;
    private BigDecimal lat;
} 