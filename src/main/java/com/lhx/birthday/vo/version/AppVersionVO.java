package com.lhx.birthday.vo.version;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "版本信息响应体",description = "版本信息响应体")
public class AppVersionVO {

    private String sourceChannel;

    private String targetChannel;

    private String version;

    private String url;

}
