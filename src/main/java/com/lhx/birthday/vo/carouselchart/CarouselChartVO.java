package com.lhx.birthday.vo.carouselchart;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "轮播图信息响应体",description = "轮播图信息响应体")
public class CarouselChartVO {

    @ApiModelProperty(name = "类型", value = "类型")
    private String type;

    @ApiModelProperty(name = "链接", value = "链接")
    private String link;

    @ApiModelProperty(name = "轮播图链接", value = "轮播图链接")
    private String bannerUrl;

    @ApiModelProperty(name = "参数", value = "参数")
    private String param;

}
