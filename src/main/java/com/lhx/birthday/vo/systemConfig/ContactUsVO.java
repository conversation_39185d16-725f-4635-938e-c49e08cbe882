package com.lhx.birthday.vo.systemConfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "数据源响应体",description = "数据源响应体")
public class ContactUsVO {

//    @ApiModelProperty(name = "微信名称", value = "用户的微信名称")
//    private String wxName;
//
//    @ApiModelProperty(name = "微信头像URL", value = "用户的微信头像URL")
//    private String wxPortraitUrl;
//
//    @ApiModelProperty(name = "微信二维码URL", value = "用户的微信二维码URL")
//    private String wxQrcodeUrl;
//
//    @ApiModelProperty(name = "服务时间", value = "用户的服务时间")
//    private String wxServiceTime;
//
//    @ApiModelProperty(name = "邮箱地址", value = "用户的邮箱地址")
//    private String email;

    @ApiModelProperty(name = "图片地址", value = "图片地址")
    private String imgUrl;



}
