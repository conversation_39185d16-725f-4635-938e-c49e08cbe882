package com.lhx.birthday.vo.systemConfig;

import com.lhx.birthday.enums.RewardType;
import com.lhx.birthday.enums.RewardVipUnit;
import com.lhx.birthday.util.NumUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> lhx
 * @date 2024/1/8 23:42
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RewardSettingVO {

    private RewardType type;
    private RewardVipUnit unit;
    private Integer value;


    public static RewardSettingVO from(RewardType type, RewardVipUnit unit, Integer value) {
        return RewardSettingVO.builder().type(type).unit(unit).value(value).build();
    }


    public boolean checkValid() {
        return checkStaticValid(type, unit, value);
    }

    public boolean checkValidVip() {
        return checkStaticValidVip(type, unit, value);
    }

    public static boolean checkStaticValid(RewardType t, RewardVipUnit u, Integer v) {
        if (t == null) {
            return false;
        }
        if (t == RewardType.VIP) {
            return u != null && NumUtil.gt0(v);
        }
        return NumUtil.gt0(v);
    }

    public boolean checkStaticValidVip(RewardType t, RewardVipUnit u, Integer v) {
        if (t == null) {
            return false;
        }
        if (t == RewardType.VIP) {
            return u != null && NumUtil.gt0(v);
        }
        return false;
    }

    public Integer toHours() {
        return RewardVipUnit.toHours(unit, value);
    }
}
