package com.lhx.birthday.vo.systemConfig;

import com.lhx.birthday.enums.DeleteFlag;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>系统配置表实体类</p>
 * <AUTHOR>
 * @date 2019-11-05 18:33:04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "系统配置",description = "系统配置")
public class SystemConfigVO {

	/**
	 * 键
	 */
	@ApiModelProperty(name = "configKey", value = "配置的键")
	private String configKey;

	/**
	 * 类型
	 */
	@ApiModelProperty(name = "configType", value = "配置的类型")
	private String configType;

	/**
	 * 名称
	 */
	@ApiModelProperty(name = "configName", value = "配置的名称")
	private String configName;

	/**
	 * 备注
	 */
	@ApiModelProperty(name = "remark", value = "配置的备注")
	private String remark;

	/**
	 * 状态,0:未启用1:已启用
	 */
	@ApiModelProperty(name = "status", value = "配置的状态，0表示未启用，1表示已启用")
	private Integer status;

	/**
	 * 配置内容，如JSON内容
	 */
	@ApiModelProperty(name = "context", value = "配置的内容，例如JSON格式的内容")
	private String context;



}