package com.lhx.birthday.vo.systemConfig;

import com.lhx.birthday.enums.DefaultFlag;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "数据源响应体",description = "数据源响应体")
public class SmsSourceVO {

    @ApiModelProperty(name = "中文名", value = "该字段的中文名")
    private String chn;

    @ApiModelProperty(name = "国家（或地区）", value = "国家或地区的名称")
    private String eng;

    @ApiModelProperty(name = "国家（或地区）码", value = "国家或地区的代码")
    private String countryCode;

    @ApiModelProperty(name = "代号", value = "该字段的代号")
    private String code;


}
