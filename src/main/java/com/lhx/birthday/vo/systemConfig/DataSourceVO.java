package com.lhx.birthday.vo.systemConfig;

import com.lhx.birthday.enums.DefaultFlag;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "数据源响应体",description = "数据源响应体")
public class DataSourceVO {

    @ApiModelProperty(name = "中文备注", value = "该字段的中文备注")
    private String chn;

    @ApiModelProperty(name = "英文备注", value = "该字段的英文备注")
    private String eng;

    @ApiModelProperty(name = "数据源名称", value = "数据源的名称")
    private String sourceName;

    @ApiModelProperty(name = "数据源来源信息", value = "数据源的来源信息")
    private String sourceInfo;

    @ApiModelProperty(name = "数据值", value = "数据的值")
    private Integer source;

    @ApiModelProperty(name = "是否预警 0:否 1:是", value = "是否预警，0表示否，1表示是")
    private DefaultFlag isAlarm;

    @ApiModelProperty(name = "是否选中 0:否 1:是", value = "是否选中，0表示否，1表示是")
    private DefaultFlag isSelect;


}
