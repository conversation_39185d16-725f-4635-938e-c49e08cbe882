package com.lhx.birthday.vo;

import com.lhx.birthday.entity.Personality;
import com.lhx.birthday.entity.PersonalityTestResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 性格测试结果视图对象
 * <AUTHOR> lhx
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PersonalityResultVO {
    
    /**
     * 结果ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 类型代码
     */
    private String typeCode;
    
    /**
     * 类型名称
     */
    private String typeName;
    
    /**
     * 类型标题
     */
    private String typeTitle;
    
    /**
     * 类型描述
     */
    private String typeDesc;
    
    /**
     * 标签名称
     */
    private String tagName;
    
    /**
     * 详细描述
     */
    private String description;
    
    /**
     * E维度分数
     */
    private Integer eScore;
    
    /**
     * I维度分数
     */
    private Integer iScore;
    
    /**
     * S维度分数
     */
    private Integer sScore;
    
    /**
     * N维度分数
     */
    private Integer nScore;
    
    /**
     * T维度分数
     */
    private Integer tScore;
    
    /**
     * F维度分数
     */
    private Integer fScore;
    
    /**
     * J维度分数
     */
    private Integer jScore;
    
    /**
     * P维度分数
     */
    private Integer pScore;
    
    /**
     * 测试时间
     */
    private LocalDateTime testTime;
    
    /**
     * 从PersonalityTestResult和Personality创建PersonalityResultVO
     *
     * @param result 测试结果
     * @param personality 性格类型
     * @return PersonalityResultVO
     */
    public static PersonalityResultVO from(PersonalityTestResult result, Personality personality) {
        if (result == null) {
            return null;
        }
        
        PersonalityResultVO vo = PersonalityResultVO.builder()
                .id(result.getId())
                .userId(result.getUserId())
                .typeCode(result.getTypeCode())
                .eScore(result.getEScore())
                .iScore(result.getIScore())
                .sScore(result.getSScore())
                .nScore(result.getNScore())
                .tScore(result.getTScore())
                .fScore(result.getFScore())
                .jScore(result.getJScore())
                .pScore(result.getPScore())
                .testTime(result.getTestTime())
                .build();
                
        if (personality != null) {
            vo.setTypeName(personality.getTypeName());
            vo.setTypeTitle(personality.getTypeTitle());
            vo.setTypeDesc(personality.getTypeDesc());
            vo.setTagName(personality.getTagName());
            vo.setDescription(personality.getDescription());
        }
        
        return vo;
    }
} 