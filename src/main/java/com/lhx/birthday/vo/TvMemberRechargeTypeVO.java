package com.lhx.birthday.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lhx.birthday.util.LocalDateTimeDeserializer;
import com.lhx.birthday.util.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "订阅信息响应体",description = "订阅信息响应体")
public class TvMemberRechargeTypeVO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 创建日期
     */
    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createDate;

    /**
     * 修改日期
     */
    @ApiModelProperty(value = "修改日期")
    private LocalDateTime modifyDate;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer orders;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String code;

    /**
     * 有效期（天）
     */
    @ApiModelProperty(value = "有效期（天）")
    private Integer expire;

    /**
     * iOS价格
     */
    @ApiModelProperty(value = "iOS价格")
    private BigDecimal priceIos;

    /**
     * Android价格
     */
    @ApiModelProperty(value = "Android价格")
    private BigDecimal priceAndroid;

    /**
     * 是否自动订阅
     */
    @ApiModelProperty(value = "是否自动订阅")
    private Boolean autoSubscribe;

    /**
     * 是否在iOS端展示
     */
    @ApiModelProperty(value = "是否在iOS端展示")
    private Boolean iosShow;

    /**
     * 是否在Android端展示
     */
    @ApiModelProperty(value = "是否在Android端展示")
    private Boolean androidShow;

    /**
     * 是否有折扣
     */
    @ApiModelProperty(value = "是否有折扣")
    private Boolean discount;

    /**
     * 折扣开始时间
     */
    @ApiModelProperty(value = "折扣开始时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime discountBegin;

    /**
     * 折扣结束时间
     */
    @ApiModelProperty(value = "折扣结束时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime discountEnd;

    /**
     * 折扣码
     */
    @ApiModelProperty(value = "折扣码")
    private String codeDiscount;

    /**
     * 折扣后iOS价格
     */
    @ApiModelProperty(value = "折扣后iOS价格")
    private BigDecimal priceDiscountIos;

    /**
     * 折扣后Android价格
     */
    @ApiModelProperty(value = "折扣后Android价格")
    private BigDecimal priceDiscountAndroid;

    /**
     * 最大折扣额度
     */
    @ApiModelProperty(value = "最大折扣额度")
    private Integer discountMax;

    /**
     * 免费试用天数
     */
    @ApiModelProperty(value = "免费试用天数")
    private Integer appleFreeDay;
}
