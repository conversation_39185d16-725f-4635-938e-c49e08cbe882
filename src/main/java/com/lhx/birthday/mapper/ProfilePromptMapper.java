package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.Profile;
import com.lhx.birthday.entity.ProfilePrompt;
import com.lhx.birthday.entity.UserPaySubscribe;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/9/22 14:27
 */
public interface ProfilePromptMapper extends JpaRepository<ProfilePrompt, Long>, JpaSpecificationExecutor<ProfilePrompt>  {

    ProfilePrompt findByProfileId(Long profileId);

    @Transactional
    int deleteByProfileId(Long profileId);

    List<ProfilePrompt> findByUserId(Long userId);

    @Query(nativeQuery = true,value = "SELECT * FROM profile_prompt WHERE next_lunar_time = ?1 OR next_solar_time = ?2")
    List<ProfilePrompt> findByBirthday(String nextLunarTime, String nextSolarTime);


}
