package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.Tool;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.time.LocalDate;

/**
 * <AUTHOR> lhx
 * @date 2023/9/22 14:27
 */
public interface ToolMapper extends JpaRepository<Tool, Long>, JpaSpecificationExecutor<Tool>  {

    Tool findByTime(LocalDate time);

}
