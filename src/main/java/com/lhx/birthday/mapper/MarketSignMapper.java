package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.MarketInvite;
import com.lhx.birthday.entity.MarketSign;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/9/22 14:27
 */
public interface MarketSignMapper extends JpaRepository<MarketSign, Long>, JpaSpecificationExecutor<MarketSign>  {

    @Query(nativeQuery = true,value = "SELECT * FROM market_sign WHERE user_id=?1 ORDER BY id DESC LIMIT 1")
    MarketSign getLastOne(@Param("userId") Long userId);

    @Query(nativeQuery = true,value ="SELECT * FROM market_sign WHERE user_id=?1 AND create_time>=?2 ORDER BY id ASC LIMIT 7")
    List<MarketSign> getLastWeek(@Param("userId") Long userId, @Param("lastMonday") String lastMonday);
}
