package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.AnswerBook;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

/**
 * <AUTHOR> lhx
 * @date 2023/9/22 14:27
 */
public interface AnswerBookMapper extends JpaRepository<AnswerBook, Long>, JpaSpecificationExecutor<AnswerBook>  {

    @Query(nativeQuery = true,value = "select * from answer_book ORDER BY rand() limit 1")
    AnswerBook findRand();

}
