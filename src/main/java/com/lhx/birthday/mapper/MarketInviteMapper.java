package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.MarketInvite;
import com.lhx.birthday.entity.MarketStarRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/9/22 14:27
 */
public interface MarketInviteMapper extends JpaRepository<MarketInvite, Long>, JpaSpecificationExecutor<MarketInvite>  {

    MarketInvite findByInvitedUserId(Long userId);

    List<MarketInvite> findByInviteUserId(Long userId);
}
