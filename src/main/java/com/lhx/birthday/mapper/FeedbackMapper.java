package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.Feedback;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/9/22 14:27
 */
public interface FeedbackMapper extends JpaRepository<Feedback, Long>, JpaSpecificationExecutor<Feedback>  {


    @Query("from Feedback u where u.userId = ?1 order by u.createTime desc")
    List<Feedback> getByUserId(Long userId);
}
