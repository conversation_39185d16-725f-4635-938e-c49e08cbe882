package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.MarketRewardPoint;
import com.lhx.birthday.entity.MarketRewardVip;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/9/22 14:27
 */
public interface MarketRewardPointMapper extends JpaRepository<MarketRewardPoint, Long>, JpaSpecificationExecutor<MarketRewardPoint>  {

    @Query(nativeQuery = true,value = "SELECT * FROM market_reward_point where user_id=?1 ORDER BY create_time DESC")
    List<MarketRewardPoint> findByUserId(Long userId);

    @Query(nativeQuery = true,value = "SELECT * FROM market_reward_point where user_id=?1 AND type='ADD' AND valid_value>0 AND create_time>=DATE_SUB(NOW(),INTERVAL ?2 MONTH) ORDER BY id ASC")
    List<MarketRewardPoint> getValidList(@Param("userId") Long userId, @Param("expiryMonthCount") Integer expiryMonthCount);

    @Query(nativeQuery = true,value = "SELECT DISTINCT user_id FROM market_reward_point where type='ADD' AND valid_value>0 AND create_time<DATE_SUB(NOW(),INTERVAL ?1 MONTH)")
    List<Long> getUserIdListHasExpiry(@Param("expiryMonthCount") Integer expiryMonthCount);

    @Query(nativeQuery = true,value = "SELECT * FROM market_reward_point where user_id=?1 AND type='ADD' AND valid_value>0 AND create_time<DATE_SUB(NOW(),INTERVAL ?2 MONTH)")
    List<MarketRewardPoint> getExpiryList(@Param("userId") Long userId, @Param("expiryMonthCount") Integer expiryMonthCount);
}
