package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.SolarTerms;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * <AUTHOR> lhx
 * @date 2023/9/22 14:27
 */
public interface SolarTermsMapper extends JpaRepository<SolarTerms, Long>, JpaSpecificationExecutor<SolarTerms>  {

    SolarTerms findByYear(int year);

}
