package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.CorpusAstroEn;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface CorpusAstroEnMapper extends JpaRepository<CorpusAstroEn, Integer>, JpaSpecificationExecutor<CorpusAstroEn> {
    Optional<CorpusAstroEn> findByTypeAndTitleCn(Integer type, String title);
}