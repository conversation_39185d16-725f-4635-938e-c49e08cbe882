package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.Feedback;
import com.lhx.birthday.entity.MarketPointVip;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/9/22 14:27
 */
public interface MarketPointVipMapper extends JpaRepository<MarketPointVip, Long>, JpaSpecificationExecutor<MarketPointVip>  {

    List<MarketPointVip> findByDevice(Integer device);

}
