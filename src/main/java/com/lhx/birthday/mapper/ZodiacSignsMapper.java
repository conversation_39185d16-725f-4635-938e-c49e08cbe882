package com.lhx.birthday.mapper;

import com.lhx.birthday.enums.ZodiacSigns;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/9/22 14:27
 */
public interface ZodiacSignsMapper extends JpaRepository<ZodiacSigns, Long>, JpaSpecificationExecutor<ZodiacSigns>  {

    ZodiacSigns findByTimeAndType(String time,int type);

    @Query( nativeQuery = true, value ="SELECT `time` " +
            "FROM zodiac_signs " +
            "WHERE `time` BETWEEN DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND CURDATE() " +
            "group by `time` " +
            "ORDER BY `time` DESC")
    List<String> getRecentSevenDaysZodiacSignsDates();

}
