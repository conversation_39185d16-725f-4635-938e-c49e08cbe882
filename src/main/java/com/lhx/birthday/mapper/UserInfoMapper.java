package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.UserInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/9/22 14:27
 */
public interface UserInfoMapper extends JpaRepository<UserInfo, Long>, JpaSpecificationExecutor<UserInfo>  {

    UserInfo findByPhone(String phone);

    @Modifying
    @Transactional
    @Query("update UserInfo u set u.vipExpiryDate = ?2,u.vipProductId = ?3,u.vip = ?4 where u.userId = ?1")
    int updateVipExpiryDateByUserId(Long userId, LocalDateTime vipExpireDate, String productId,Integer vip);

    @Transactional
    @Modifying
    @Query("update UserInfo u set u.rewardVipBeginDate = ?2 where u.userId = ?1")
    int updateRewardVipBeginDate(Long userId, LocalDateTime rewardVipBeginDate);

    @Modifying
    @Transactional
    @Query("update UserInfo u set u.freeUsed = 1 where u.userId = ?1")
    int updateFreeUse(Long userId);

    UserInfo findByInvitationCode(String invitationCode);

    @Query(nativeQuery = true,value = "SELECT * FROM user_info WHERE  user_id>?1 AND vip_hours>0 AND state=0 ORDER BY user_id ASC LIMIT ?2")
    List<Long> getVipHoursIdList(@Param("minId") Long minId, @Param("limit") Integer limit);

    @Query(nativeQuery = true,value = "SELECT u.* " +
            "FROM user_info u " +
            "WHERE " +
            "    (u.vip_expiry_date IS NULL OR u.vip_expiry_date < NOW()) " +
            "    AND " +
            "    (u.reward_vip_begin_date IS NULL OR u.reward_vip_begin_date < NOW()) " +
            "    AND " +
            "    (u.cdkey_expiry_date IS NULL OR u.cdkey_expiry_date < NOW()) " +
            "    AND u.vip = 1")
    List<UserInfo> getExpiryVipUserinfoList();

}
