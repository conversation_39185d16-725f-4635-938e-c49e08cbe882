package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.PersonalityTestResult;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 性格测试结果Mapper
 * <AUTHOR> lhx
 */
public interface PersonalityTestResultMapper extends JpaRepository<PersonalityTestResult, Long>, JpaSpecificationExecutor<PersonalityTestResult> {
    
    /**
     * 根据用户ID查找测试结果
     *
     * @param userId 用户ID
     * @return 该用户的所有测试结果
     */
    List<PersonalityTestResult> findByUserIdOrderByTestTimeDesc(Long userId);
    
    /**
     * 查找用户最近的测试结果
     *
     * @param userId 用户ID
     * @return 最近的测试结果
     */
    PersonalityTestResult findTopByUserIdOrderByTestTimeDesc(Long userId);
} 