package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.CountryCode;
import com.lhx.birthday.entity.MarketSign;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/9/22 14:27
 */
public interface CountryCodeMapper extends JpaRepository<CountryCode, Long>, JpaSpecificationExecutor<CountryCode>  {


    @Query(nativeQuery = true,value = "select * from country_code GROUP BY iso_country_code;")
    List<CountryCode> getCountryList();

    @Query(nativeQuery = true,value = "SELECT *,SQRT(POW(69.1 * (?1 - lat), 2) + POW(69.1 * (?2 - lon) * COS(lat / 57.3), 2)) AS distance " +
            "FROM country_code " +
            "ORDER BY distance " +
            "LIMIT 1")
    CountryCode findDis(Double lat,Double lon);

}
