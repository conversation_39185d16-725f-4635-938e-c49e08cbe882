package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.Dream;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * <AUTHOR> lhx
 * @date 2023/9/22 14:27
 */
public interface DreamMapper extends JpaRepository<Dream, Long>, JpaSpecificationExecutor<Dream>  {

    Dream findByTitle(String title);
    Dream findByKeyword(String title);

}
