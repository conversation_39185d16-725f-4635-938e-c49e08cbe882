package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.ProfileGroupRel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import javax.transaction.Transactional;
import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/9/22 14:27
 */
public interface ProfileGroupRelMapper extends JpaRepository<ProfileGroupRel, Long>, JpaSpecificationExecutor<ProfileGroupRel>  {

    List<ProfileGroupRel> findByUserId(Long userId);

    List<ProfileGroupRel> findByProfileId(Long profileId);

    List<ProfileGroupRel> findByProfileGroupId(Long profileGroupId);

    @Transactional
    int deleteByProfileId(Long profileId);

    @Transactional
    int deleteByProfileGroupId(Long profileGroupId);

    @Transactional
    int deleteByProfileGroupIdAndProfileId(Long profileGroupId,Long profileId);

}
