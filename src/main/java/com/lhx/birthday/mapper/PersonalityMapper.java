package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.Personality;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * 性格类型Mapper
 * <AUTHOR> lhx
 */
public interface PersonalityMapper extends JpaRepository<Personality, Integer>, JpaSpecificationExecutor<Personality> {

    /**
     * 根据类型代码查找性格类型
     *
     * @param typeCode 类型代码 (如: INTJ, ENFP等)
     * @return 性格类型
     */
    Personality findByTypeCode(String typeCode);
} 