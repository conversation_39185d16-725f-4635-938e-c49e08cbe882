package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.Attachment;
import com.lhx.birthday.entity.pay.PayOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/9/22 14:27
 */
public interface PayOrderMapper extends JpaRepository<PayOrder, Long>, JpaSpecificationExecutor<PayOrder>  {

    PayOrder findByOrderId(String orderId);

    @Query(nativeQuery = true,value = "SELECT * FROM pay_order where id > ?1 and transaction_id is not null")
    List<PayOrder> findBySequence(Integer id);

    @Query(nativeQuery = true,value = "select * from pay_order where id < ?1 and user_id = ?2 and transaction_id is not null order by id desc limit 1")
    PayOrder findLastBySequence(Long id,Long userId);

}
