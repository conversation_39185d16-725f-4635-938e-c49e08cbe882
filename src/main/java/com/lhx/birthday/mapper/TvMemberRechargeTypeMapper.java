package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.TvMemberRechargeType;
import com.lhx.birthday.entity.UserPaySubscribe;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * <AUTHOR> lhx
 * @date 2023/10/30 15:42
 */
public interface TvMemberRechargeTypeMapper extends JpaRepository<TvMemberRechargeType, Long>, JpaSpecificationExecutor<TvMemberRechargeType> {

    TvMemberRechargeType findByCode(String code);

}
