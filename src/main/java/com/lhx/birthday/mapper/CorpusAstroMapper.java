package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.CorpusAstro;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface CorpusAstroMapper extends JpaRepository<CorpusAstro, Integer>, JpaSpecificationExecutor<CorpusAstro> {
    Optional<CorpusAstro> findByTypeAndTitle(Integer type, String title);
} 