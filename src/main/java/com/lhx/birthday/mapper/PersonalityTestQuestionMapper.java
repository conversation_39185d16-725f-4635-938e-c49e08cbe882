package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.PersonalityTestQuestion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * 性格测试题目Mapper
 * <AUTHOR> lhx
 */
public interface PersonalityTestQuestionMapper extends JpaRepository<PersonalityTestQuestion, Integer>, JpaSpecificationExecutor<PersonalityTestQuestion> {

    /**
     * 根据问题ID查找题目选项
     *
     * @param questionId 问题ID
     * @return 题目选项列表
     */
    List<PersonalityTestQuestion> findByQuestionId(Integer questionId);
    
    /**
     * 获取所有不同的问题ID
     *
     * @return 问题ID列表
     */
    @Query("SELECT DISTINCT p.questionId FROM PersonalityTestQuestion p ORDER BY p.questionId")
    List<Integer> findDistinctQuestionIds();
} 