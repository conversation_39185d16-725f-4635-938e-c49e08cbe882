package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.UserRewardRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * 用户好评记录Mapper
 */
public interface UserRewardRecordMapper extends JpaRepository<UserRewardRecord, Long> {

    /**
     * 根据用户ID和触发类型查询好评记录
     * @param userId 用户ID
     * @param triggerType 触发类型
     * @return 好评记录列表
     */
    List<UserRewardRecord> findByUserIdAndTriggerType(Long userId, Integer triggerType);

    /**
     * 根据用户ID查询好评记录
     * @param userId 用户ID
     * @return 好评记录列表
     */
    List<UserRewardRecord> findByUserId(Long userId);

    /**
     * 查询用户的操作序号最大值
     * @param userId 用户ID
     * @return 操作序号最大值
     */
    @Query("SELECT MAX(r.opIdx) FROM UserRewardRecord r WHERE r.userId = :userId")
    Integer findMaxOpIdxByUserId(@Param("userId") Long userId);

    /**
     * 检查用户是否有确认的好评记录
     * @param userId 用户ID
     * @param opType 操作类型 1-确认
     * @return 是否存在记录
     */
    boolean existsByUserIdAndOpType(Long userId, Integer opType);
}