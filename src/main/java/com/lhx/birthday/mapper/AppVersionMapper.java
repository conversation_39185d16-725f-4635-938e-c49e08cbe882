package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.AppVersion;
import com.lhx.birthday.entity.Attachment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/9/22 14:27
 */
public interface AppVersionMapper extends JpaRepository<AppVersion, Long>, JpaSpecificationExecutor<AppVersion>  {

    List<AppVersion> findByState(int state);

}
