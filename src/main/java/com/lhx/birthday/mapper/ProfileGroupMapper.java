package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.Profile;
import com.lhx.birthday.entity.ProfileGroup;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/9/22 14:27
 */
public interface ProfileGroupMapper extends JpaRepository<ProfileGroup, Long>, JpaSpecificationExecutor<ProfileGroup>  {

    List<ProfileGroup> findByUserId(Long userId);

}
