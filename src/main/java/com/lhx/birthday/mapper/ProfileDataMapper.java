package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.ProfileData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

/**
 * <AUTHOR> lhx
 * @date 2023/9/22 14:27
 */
public interface ProfileDataMapper extends JpaRepository<ProfileData, Long>, JpaSpecificationExecutor<ProfileData>  {

    ProfileData findByProfileIdAndType(Long profileId,String type);

    ProfileData findByProfileIdAndTypeAndProfileKey(Long profileId,String type,String key);

    ProfileData findByMaleProfileIdAndFemaleProfileIdAndType(Long maleProfileId, Long femaleProfileId,String type);

    @Modifying
    @Query(nativeQuery = true,value = "delete from profile_data where profile_id = ?1 or male_profile_id = ?1 or female_profile_id = ?1")
    int deleteProfile(Long profileId);

}
