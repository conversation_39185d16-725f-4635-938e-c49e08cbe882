package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.MarketPointVip;
import com.lhx.birthday.entity.MarketStarRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * <AUTHOR> lhx
 * @date 2023/9/22 14:27
 */
public interface MarketStarRateMapper extends JpaRepository<MarketStarRate, Long>, JpaSpecificationExecutor<MarketStarRate>  {

    MarketStarRate findByUserId(Long userId);

}
