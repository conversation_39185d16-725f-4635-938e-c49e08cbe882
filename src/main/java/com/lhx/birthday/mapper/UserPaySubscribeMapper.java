package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.UserPaySubscribe;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/10/30 15:42
 */
public interface UserPaySubscribeMapper extends JpaRepository<UserPaySubscribe, Long>, JpaSpecificationExecutor<UserPaySubscribe> {

    @Modifying
    @Transactional
    @Query("update UserPaySubscribe u set u.userId = ?2 where u.id = ?1")
    int updateUserIdById(Integer id, Long userId);

    @Query(nativeQuery = true,value = "SELECT * FROM user_pay_subscribe WHERE status = 1 OR status IS NULL GROUP BY original_transaction_id")
    List<UserPaySubscribe> findAllOrder();

    @Query(nativeQuery = true,value = "SELECT * FROM user_pay_subscribe WHERE original_transaction_id = ?1 order by id desc limit 1")
    UserPaySubscribe findByOriginalTransactionId(String originalTransactionId);

    UserPaySubscribe findByTransactionId(String tid);

    @Query(nativeQuery = true,value = "SELECT * FROM user_pay_subscribe WHERE original_transaction_id = ?1")
    List<UserPaySubscribe> findListByOriginalTransactionId(String originalTransactionId);

    @Query(nativeQuery = true,value = "SELECT * FROM user_pay_subscribe where id > ?1 ")
    List<UserPaySubscribe> findBySequence(Integer id);

    @Query(nativeQuery = true,value = "select * from user_pay_subscribe where id < ?1 and user_id = ?2 order by id desc limit 1")
    UserPaySubscribe findLastBySequence(Integer id,Long userId);

}
