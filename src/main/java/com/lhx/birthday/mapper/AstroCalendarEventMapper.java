package com.lhx.birthday.mapper;

import com.lhx.birthday.entity.AstroCalendarEvent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface AstroCalendarEventMapper extends JpaRepository<AstroCalendarEvent, Long>, JpaSpecificationExecutor<AstroCalendarEvent> {

    void deleteAllByEventDatetimeBetween(LocalDateTime start, LocalDateTime end);

    List<AstroCalendarEvent> findAllByEventDatetimeBetween(LocalDateTime start, LocalDateTime end);
    
    @Query(value = "SELECT " +
            "DATE_FORMAT(event_datetime, '%Y-%m-%d') AS formattedDate, " +
            "COALESCE(" +
            "MAX(CASE WHEN type = 4 THEN title_short END), " +
            "MAX(CASE WHEN type = 2 THEN title_short END), " +
            "MAX(CASE WHEN type = 9 THEN title_short END), " +
            "MAX(CASE WHEN type = 6 THEN title_short END)" +
            ") AS titleShort," +
            "COALESCE( " +
            "MAX(CASE WHEN type = 4 THEN type END), " +
            "MAX(CASE WHEN type = 2 THEN type END), " +
            "MAX(CASE WHEN type = 9 THEN type END), " +
            "MAX(CASE WHEN type = 6 THEN type END)" +
            ") AS type  " +
            "FROM astro_calendar_event " +
            "WHERE event_datetime BETWEEN ?1 AND ?2 " +
            "GROUP BY DATE_FORMAT(event_datetime, '%Y-%m-%d') " +
            "ORDER BY event_datetime ASC", nativeQuery = true)
    List<Map<String, Object>> findEventSummaryByDateRange(LocalDateTime start, LocalDateTime end);
    
    /**
     * 查询行星逆行-顺行事件对
     * @param planetCode 行星代码
     * @param retrogradeTitle 逆行标题
     * @param directTitle 顺行标题
     * @return 逆行-顺行事件对列表
     */
    @Query(value = "WITH \n" +
            "retrograde_events AS (\n" +
            "    SELECT \n" +
            "        id,\n" +
            "        event_datetime,\n" +
            "        title_short,\n" +
            "        title\n" +
            "    FROM \n" +
            "        astro_calendar_event\n" +
            "    WHERE \n" +
            "        type = 4 \n" +
            "        AND planet_code = :planetCode\n" +
            "        AND title_short = :retrogradeTitle\n" +
            "        AND event_datetime < DATE_ADD(CURRENT_DATE(), INTERVAL 2 YEAR)\n" +
            "),\n" +
            "direct_events AS (\n" +
            "    SELECT \n" +
            "        id,\n" +
            "        event_datetime,\n" +
            "        title_short,\n" +
            "        title\n" +
            "    FROM \n" +
            "        astro_calendar_event\n" +
            "    WHERE \n" +
            "        type = 4 \n" +
            "        AND planet_code = :planetCode\n" +
            "        AND title_short = :directTitle\n" +
            "),\n" +
            "paired_events AS (\n" +
            "    SELECT \n" +
            "        s.id AS id,\n" +
            "        s.event_datetime AS start_datetime,\n" +
            "        s.title_short AS short_title,\n" +
            "        s.title AS title,\n" +
            "        (\n" +
            "            SELECT sh.id\n" +
            "            FROM direct_events sh\n" +
            "            WHERE sh.event_datetime > s.event_datetime\n" +
            "            ORDER BY sh.event_datetime ASC\n" +
            "            LIMIT 1\n" +
            "        ) AS end_id\n" +
            "    FROM \n" +
            "        retrograde_events s\n" +
            "),\n" +
            "final_pairs AS (\n" +
            "    SELECT \n" +
            "        p.id,\n" +
            "        p.start_datetime,\n" +
            "        p.short_title,\n" +
            "        p.title,\n" +
            "        MIN(p.end_id) AS end_id\n" +
            "    FROM \n" +
            "        paired_events p\n" +
            "    GROUP BY \n" +
            "        p.id, p.start_datetime, p.short_title, p.title\n" +
            "    HAVING \n" +
            "        end_id IS NOT NULL\n" +
            ")\n" +
            "\n" +
            "SELECT \n" +
            "    a.id,\n" +
            "    DATE_FORMAT(a.event_datetime, '%Y年%m月%d日') AS start_date,\n" +
            "    a.event_datetime AS start_datetime,\n" +
            "    a.title_short AS short_title,\n" +
            "    a.title AS title,\n" +
            "    b.id AS end_id,\n" +
            "    DATE_FORMAT(b.event_datetime, '%Y年%m月%d日') AS end_date,\n" +
            "    b.event_datetime AS end_datetime,\n" +
            "    b.title_short AS end_short_title,\n" +
            "    b.title AS end_title,\n" +
            "    a.type\n" +
            "FROM \n" +
            "    astro_calendar_event a\n" +
            "JOIN \n" +
            "    final_pairs p ON a.id = p.id\n" +
            "JOIN \n" +
            "    astro_calendar_event b ON b.id = p.end_id\n" +
            "WHERE \n" +
            "    b.event_datetime > DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)\n" +
            "ORDER BY \n" +
            "    a.event_datetime", nativeQuery = true)
    List<Object[]> findPlanetRetrogradePairs(@Param("planetCode") String planetCode, 
                                            @Param("retrogradeTitle") String retrogradeTitle, 
                                            @Param("directTitle") String directTitle);
} 