package com.lhx.birthday.service;

import com.lhx.birthday.entity.CountryCode;
import com.lhx.birthday.entity.UserInfo;
import com.lhx.birthday.mapper.CountryCodeMapper;
import com.lhx.birthday.mapper.UserInfoMapper;
import com.lhx.birthday.request.profile.RegionRequest;
import com.lhx.birthday.service.impl.ICountryCodeServiceImpl;
import com.lhx.birthday.vo.profile.RegionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;

public interface CountryCodeService {

    List<RegionVO> getCountryCodeList(RegionRequest regionRequest);

}
