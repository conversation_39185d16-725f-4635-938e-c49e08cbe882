package com.lhx.birthday.service;

import com.lhx.birthday.vo.market.MarketSettingVO;
import com.lhx.birthday.vo.market.PointVipVO;
import com.lhx.birthday.vo.market.ProductMarketingVO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface IMarketSettingService {

    List<PointVipVO> getPointVipList(Integer device);

    ProductMarketingVO getProductMarketing(String configKey);

    MarketSettingVO getMarketing(String configKey, HttpServletRequest request);
}
