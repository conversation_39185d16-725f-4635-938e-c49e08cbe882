package com.lhx.birthday.service;

import com.lhx.birthday.request.profile.ProfileBaseRequest;
import com.lhx.birthday.request.profile.ProfileUpdateSortRequest;
import com.lhx.birthday.request.profilegroup.*;
import com.lhx.birthday.vo.profile.ProfileGroupVO;
import com.lhx.birthday.vo.profile.ProfileVO;

import java.util.List;

public interface IProfileGroupService {

    ProfileGroupVO addProfileGroup(ProfileGroupBaseRequest profileGroupBaseRequest);

    ProfileGroupVO updateProfileGroup(ProfileGroupBaseRequest profileGroupBaseRequest);

    ProfileGroupVO updateProfileGroupRel(ProfileGroupRelUpdateRequest profileGroupRelUpdateRequest,Long userId);

    int updateSort(ProfileGroupUpdateSortRequest profileGroupUpdateSortRequest);

    int delById(Long id);

    int delByProfileAndProfileGroup(ProfileGroupRelDelRequest profileGroupRelDelRequest);

    List<ProfileGroupVO> getProfileList(ProfileGroupBaseRequest profileGroupBaseRequest);

}
