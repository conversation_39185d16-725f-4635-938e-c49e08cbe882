package com.lhx.birthday.service;

import com.lhx.birthday.request.constellation.ConstellationBaseRequest;
import com.lhx.birthday.request.constellation.ConstellationProfileUpdateSortRequest;
import com.lhx.birthday.request.profile.ProfileUpdateSortRequest;
import com.lhx.birthday.vo.constellation.ConstellationProfileVO;

import java.util.List;

public interface IConstellationService {
    
    /**
     * 添加星座档案
     * @param request
     */
    void addConstellation(ConstellationBaseRequest request);
    
    /**
     * 更新星座档案
     * @param request
     */
    int updateConstellation(ConstellationBaseRequest request);
    
    /**
     * 删除星座档案
     * @param request
     */
    int delById(ConstellationBaseRequest request);

    int updateSort(ConstellationProfileUpdateSortRequest constellationProfileUpdateSortRequest);

    /**
     * 获取星座档案列表
     * @param request
     * @return
     */
    List<ConstellationProfileVO> getConstellationList(ConstellationBaseRequest request);
} 