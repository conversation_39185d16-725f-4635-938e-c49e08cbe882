package com.lhx.birthday.service;

import com.lhx.birthday.request.market.SignHisRequest;
import com.lhx.birthday.request.market.SignRequest;
import com.lhx.birthday.vo.Result;
import com.lhx.birthday.vo.UserInfoVO;
import com.lhx.birthday.vo.market.SignInHistoryVO;

public interface IMarketSignService {

    Result signIn(UserInfoVO userInfoVO, SignRequest signRequest);

    SignInHistoryVO getSignInHistory(UserInfoVO userInfoVO, SignHisRequest signHisRequest);
}
