package com.lhx.birthday.service;

import com.lhx.birthday.entity.UserPaySubscribe;
import com.lhx.birthday.vo.UserPaySubscribeVO;

/**
 * <AUTHOR> lhx
 * @date 2023/10/27 09:45
 */
public interface IUserPaySubscribeService {

    UserPaySubscribe getByTransactionId(String transactionId);

    UserPaySubscribe addUserPaySubscribe(UserPaySubscribe userPaySubscribe);

    int updateUserIdById( Integer id,Long userId);

}
