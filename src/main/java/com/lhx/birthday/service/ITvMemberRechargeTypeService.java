package com.lhx.birthday.service;

import com.lhx.birthday.entity.TvMemberRechargeType;
import com.lhx.birthday.request.subscribe.SubscribeListRequest;
import com.lhx.birthday.vo.TvMemberRechargeTypeVO;

import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/10/27 09:45
 */
public interface ITvMemberRechargeTypeService {

    TvMemberRechargeType getByProductId(String productId);

    List<TvMemberRechargeTypeVO> getSubscribeList(SubscribeListRequest subscribeListRequest);
}
