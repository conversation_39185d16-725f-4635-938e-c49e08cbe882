package com.lhx.birthday.service;

import com.lhx.birthday.request.operate.CdkeyExchangeRequest;
import com.lhx.birthday.request.operate.CdkeyGenerateRequest;
import com.lhx.birthday.request.operate.ExpireRequest;
import com.lhx.birthday.request.operate.RegisterRequest;
import com.lhx.birthday.response.operate.CdkeyGenerateResponse;
import com.lhx.birthday.vo.Result;

/**
 * @Description: 反馈
 * @author: lhx
 */
public interface ICdkeyService {

    Result<CdkeyGenerateResponse> generate(CdkeyGenerateRequest cdkeyGenerateRequest);

    Result exchange(CdkeyExchangeRequest cdkeyExchangeRequest);

    Result expire(ExpireRequest expireRequest);

    Result register(RegisterRequest registerRequest);

}
