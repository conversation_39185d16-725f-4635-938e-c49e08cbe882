package com.lhx.birthday.service;

import cn.jpush.api.push.model.PushPayload;
import com.lhx.birthday.entity.Push;

public interface IJPushService {

    /**
     * 广播 (所有平台，所有设备, 不支持附加信息)
     * @param Push 推送内容
     * @return
     */
    boolean pushAll(Push Push);

    /**
     * ios广播
     * @param Push 推送内容
     * @return
     */
    boolean pushIos(Push Push);


    PushPayload buildPushIos(Push Push, String sound, String... alias);
    /**
     * ios通过alias推送 (一次推送最多 1000 个)
     * @param Push 推送内容
     * @param alias 推送id
     * @return
     */
    boolean pushIos(Push Push, String... alias);

    /**
     * ios通过alias推送 (一次推送最多 1000 个)
     * @param Push
     * @param sound
     * @param interruptionLevel
     * @param alias
     * @return
     */
    boolean pushIos(Push Push, String sound, String interruptionLevel, String... alias);

    /**
     * android广播
     * @param Push 推送内容
     * @return
     */
    boolean pushAndroid(Push Push);

    /**
     * android通过registid推送 (一次推送最多 1000 个)
     * @param Push 推送内容
     * @param alias 推送id
     * @return
     */
    boolean pushAndroid(Push Push, String ... alias);

    /**
     * 调用api推送
     * @param pushPayload 推送实体
     * @return
     */
    boolean sendPush(PushPayload pushPayload);

    boolean registerPush(String registrationId, String alias);
}
