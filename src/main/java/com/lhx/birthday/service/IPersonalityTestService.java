package com.lhx.birthday.service;

import com.lhx.birthday.entity.Personality;
import com.lhx.birthday.entity.PersonalityTestQuestion;
import com.lhx.birthday.entity.PersonalityTestResult;

import java.util.List;
import java.util.Map;

/**
 * 性格测试服务接口
 * <AUTHOR> lhx
 */
public interface IPersonalityTestService {

    /**
     * 获取所有测试题目
     *
     * @return 题目列表
     */
    Map<Integer, List<PersonalityTestQuestion>> getAllQuestions();
    
    /**
     * 根据问题ID获取题目及选项
     *
     * @param questionId 问题ID
     * @return 题目及选项列表
     */
    List<PersonalityTestQuestion> getQuestionById(Integer questionId);
    
    /**
     * 计算测试结果
     *
     * @param answerMap 答案Map，键为问题ID，值为选择的选项字母
     * @return 性格类型代码
     */
    String calculateResult(Map<Integer, Character> answerMap);
    
    /**
     * 保存测试结果
     *
     * @param userId 用户ID
     * @param answerMap 答案Map，键为问题ID，值为选择的选项字母
     * @return 保存的测试结果
     */
    PersonalityTestResult saveTestResult(Long userId, Map<Integer, Character> answerMap);
    
    /**
     * 获取用户最近的测试结果
     *
     * @param userId 用户ID
     * @return 最近的测试结果
     */
    PersonalityTestResult getUserLatestResult(Long userId);
    
    /**
     * 根据类型代码获取性格类型详情
     *
     * @param typeCode 类型代码
     * @return 性格类型详情
     */
    Personality getPersonalityByTypeCode(String typeCode);
    
    /**
     * 获取所有性格类型
     *
     * @return 所有性格类型列表
     */
    List<Personality> getAllPersonalities();
} 