package com.lhx.birthday.service.impl;

import com.lhx.birthday.entity.TvMemberRechargeType;
import com.lhx.birthday.mapper.TvMemberRechargeTypeMapper;
import com.lhx.birthday.request.subscribe.SubscribeListRequest;
import com.lhx.birthday.request.subscribe.TvMemberRechargeTypeMapperBaseRequest;
import com.lhx.birthday.service.ITvMemberRechargeTypeService;
import com.lhx.birthday.vo.TvMemberRechargeTypeVO;
import org.springframework.beans.BeanUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> lhx
 * @date 2023/10/27 09:45
 */
@Service
public class TvMemberRechargeTypeServiceImpl implements ITvMemberRechargeTypeService {

    @Resource
    private TvMemberRechargeTypeMapper tvMemberRechargeTypeMapper;

    @Override
    public TvMemberRechargeType getByProductId(String productId) {
        TvMemberRechargeTypeMapperBaseRequest tvMemberRechargeTypeMapperBaseRequest = new TvMemberRechargeTypeMapperBaseRequest();
        tvMemberRechargeTypeMapperBaseRequest.setProductId(productId);
        Optional<TvMemberRechargeType> tvMemberRechargeTypeOptional = tvMemberRechargeTypeMapper.findOne(this.build(tvMemberRechargeTypeMapperBaseRequest));
        return tvMemberRechargeTypeOptional.orElse(null);
    }

    @Override
    public List<TvMemberRechargeTypeVO> getSubscribeList(SubscribeListRequest subscribeListRequest) {
        List<TvMemberRechargeTypeVO> tvMemberRechargeTypeVOList = new ArrayList<>();
        TvMemberRechargeTypeMapperBaseRequest tvMemberRechargeTypeMapperBaseRequest = new TvMemberRechargeTypeMapperBaseRequest();
        tvMemberRechargeTypeMapperBaseRequest.setDevice(subscribeListRequest.getDevice());
        tvMemberRechargeTypeMapperBaseRequest.setUserCenter(subscribeListRequest.getUserCenter());
        List<TvMemberRechargeType> tvMemberRechargeTypeMapperAll = tvMemberRechargeTypeMapper.findAll(this.build(tvMemberRechargeTypeMapperBaseRequest));
        for (TvMemberRechargeType tvMemberRechargeType : tvMemberRechargeTypeMapperAll) {
            TvMemberRechargeTypeVO tvMemberRechargeTypeVO = new TvMemberRechargeTypeVO();
            BeanUtils.copyProperties(tvMemberRechargeType,tvMemberRechargeTypeVO);
            tvMemberRechargeTypeVOList.add(tvMemberRechargeTypeVO);
        }
        return tvMemberRechargeTypeVOList;
    }

    private Specification<TvMemberRechargeType> build(TvMemberRechargeTypeMapperBaseRequest tvMemberRechargeTypeMapperBaseRequest) {
        return (root, cquery, cbuild) -> {
            List<Predicate> predicates = new ArrayList<>();
            // 产品id
            if(tvMemberRechargeTypeMapperBaseRequest.getProductId()!=null) {
                predicates.add(cbuild.equal(root.get("code"), tvMemberRechargeTypeMapperBaseRequest.getProductId()));
            }
            // 客户端 0: ios 1:android
            if(tvMemberRechargeTypeMapperBaseRequest.getDevice()!=null){
                if(tvMemberRechargeTypeMapperBaseRequest.getDevice()==0){
                    predicates.add(cbuild.equal(root.get("iosShow"), 1));
                }else if(tvMemberRechargeTypeMapperBaseRequest.getDevice()==1){
                    predicates.add(cbuild.equal(root.get("androidShow"), 1));
                }
            }
            if(tvMemberRechargeTypeMapperBaseRequest.getUserCenter()!=null){
                predicates.add(cbuild.equal(root.get("userCenter"), tvMemberRechargeTypeMapperBaseRequest.getUserCenter()));
            }
            Predicate[] p = predicates.toArray(new Predicate[predicates.size()]);
            return p.length == 0 ? null : p.length == 1 ? p[0] : cbuild.and(p);
        };
    }
}
