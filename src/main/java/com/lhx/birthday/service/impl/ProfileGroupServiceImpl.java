package com.lhx.birthday.service.impl;

import com.lhx.birthday.entity.ProfileGroup;
import com.lhx.birthday.entity.ProfileGroupRel;
import com.lhx.birthday.mapper.ProfileGroupMapper;
import com.lhx.birthday.mapper.ProfileGroupRelMapper;
import com.lhx.birthday.request.profilegroup.*;
import com.lhx.birthday.service.IProfileGroupService;
import com.lhx.birthday.util.StringUtils;
import com.lhx.birthday.vo.profile.ProfileGroupVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProfileGroupServiceImpl implements IProfileGroupService {

    @Autowired
    private ProfileGroupMapper profileGroupMapper;

    @Autowired
    private ProfileGroupRelMapper profileGroupRelMapper;

    @Override
    public ProfileGroupVO addProfileGroup(ProfileGroupBaseRequest profileGroupBaseRequest) {
        ProfileGroup addProfileGroup = new ProfileGroup();
        addProfileGroup.setSort(100);
        addProfileGroup.setCreateTime(LocalDateTime.now());
        addProfileGroup.setModifyTime(LocalDateTime.now());
        addProfileGroup.setGroupName(profileGroupBaseRequest.getGroupName());
        addProfileGroup.setUserId(profileGroupBaseRequest.getUserId());
        ProfileGroup profileGroup = profileGroupMapper.saveAndFlush(addProfileGroup);
        ProfileGroupVO profileGroupVO = new ProfileGroupVO();
        BeanUtils.copyProperties(profileGroup, profileGroupVO);
        return profileGroupVO;
    }

    @Override
    @Transactional
    public ProfileGroupVO updateProfileGroup(ProfileGroupBaseRequest profileGroupBaseRequest) {
        Optional<ProfileGroup> profileGroupOptional = profileGroupMapper.findById(profileGroupBaseRequest.getId());
        if(profileGroupOptional.isPresent()){
            ProfileGroup profileGroup = profileGroupOptional.get();
            profileGroup.setGroupName(profileGroupBaseRequest.getGroupName());
            profileGroupMapper.saveAndFlush(profileGroup);
            ProfileGroupVO profileGroupVO = new ProfileGroupVO();
            BeanUtils.copyProperties(profileGroup, profileGroupVO);
            return profileGroupVO;
        }
        return null;
    }

    @Override
    public ProfileGroupVO updateProfileGroupRel(ProfileGroupRelUpdateRequest profileGroupRelUpdateRequest,Long userId) {
        // 保存分组信息
        if(Objects.nonNull(profileGroupRelUpdateRequest.getProfileIds()) && !StringUtils.isEmpty(profileGroupRelUpdateRequest.getProfileIds())){
            // 获取当前用户的已有分组
            List<ProfileGroupRel> existingGroups = profileGroupRelMapper.findByProfileGroupId(profileGroupRelUpdateRequest.getProfileGroupId());

            // 将新分组ID转换为集合
            Set<Long> newGroupIds = Arrays.stream(profileGroupRelUpdateRequest.getProfileIds().split(","))
                    .map(Long::valueOf)
                    .collect(Collectors.toSet());
            // 处理新增和删除的分组
            for (Long id : newGroupIds) {
                // 如果分组不存在，则创建新的关系
                if (existingGroups.stream().noneMatch(group -> group.getProfileGroupId().equals(id))) {
                    ProfileGroupRel profileGroupRel = ProfileGroupRel.builder()
                            .profileGroupId(profileGroupRelUpdateRequest.getProfileGroupId())
                            .profileId(id)
                            .userId(userId)
                            .createTime(LocalDateTime.now())
                            .modifyTime(LocalDateTime.now())
                            .build();
                    profileGroupRelMapper.saveAndFlush(profileGroupRel);
                }
            }

            // 删除不再存在的分组
            for (ProfileGroupRel existingGroup : existingGroups) {
                if (!newGroupIds.contains(existingGroup.getProfileGroupId())) {
                    profileGroupRelMapper.delete(existingGroup);
                }
            }
        }else{
            // 如果没有分组ID，考虑清空当前用户的分组关系
            profileGroupRelMapper.deleteByProfileGroupId(profileGroupRelUpdateRequest.getProfileGroupId());
        }
        return null;
    }

    @Override
    public int updateSort(ProfileGroupUpdateSortRequest profileGroupUpdateSortRequest) {
        String idsStr = profileGroupUpdateSortRequest.getIds();
        List<Long> idOrderList = Arrays.stream(idsStr.split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        List<ProfileGroup> profileGroups = profileGroupMapper.findAllById(idOrderList);

        Map<Long, ProfileGroup> profileGroupMap = new LinkedHashMap<>();
        for (ProfileGroup profileGroup : profileGroups) {
            profileGroupMap.put(profileGroup.getId(), profileGroup);
        }

        List<ProfileGroup> sortedProfileGroups = idOrderList.stream()
                .map(profileGroupMap::get)
                .filter(Objects::nonNull) // 过滤掉可能在EmergencyContactes中不存在的id
                .collect(Collectors.toList());

        for (int i = 0; i < sortedProfileGroups.size(); i++) {
            ProfileGroup profileGroup = sortedProfileGroups.get(i);
            profileGroup.setSort(i);
            profileGroupMapper.saveAndFlush(profileGroup);
        }
        return 1;
    }

    @Override
    public int delById(Long id) {
        profileGroupMapper.deleteById(id);
        profileGroupRelMapper.deleteByProfileGroupId(id);
        return 1;
    }

    @Override
    public int delByProfileAndProfileGroup(ProfileGroupRelDelRequest profileGroupRelDelRequest) {
        profileGroupRelMapper.deleteByProfileGroupIdAndProfileId(profileGroupRelDelRequest.getProfileGroupId(),profileGroupRelDelRequest.getProfileId());
        return 1;
    }

    @Override
    public List<ProfileGroupVO> getProfileList(ProfileGroupBaseRequest profileGroupBaseRequest) {
        List<ProfileGroupVO> profileGroupVOList = new ArrayList<>();
        List<ProfileGroup> profileGroupList = profileGroupMapper.findAll(this.build(profileGroupBaseRequest));
        profileGroupList.sort(Comparator.comparingInt(ProfileGroup::getSort)
                .thenComparing(ProfileGroup::getCreateTime));
        List<ProfileGroupRel> profileGroupRels = profileGroupRelMapper.findByUserId(profileGroupBaseRequest.getUserId());
        Map<Long, List<ProfileGroupRel>> relMap = profileGroupRels.stream().collect(Collectors.groupingBy(ProfileGroupRel::getProfileGroupId));
        for (ProfileGroup profileGroup : profileGroupList) {
            ProfileGroupVO profileGroupVO = new ProfileGroupVO();
            BeanUtils.copyProperties(profileGroup, profileGroupVO);
            // 查询分组档案数量
            int size = relMap.getOrDefault(profileGroup.getId(),new ArrayList<>()).size();
            profileGroupVO.setCount(size);
            profileGroupVOList.add(profileGroupVO);
        }
        return profileGroupVOList;
    }

    private Specification<ProfileGroup> build(ProfileGroupBaseRequest profileGroupBaseRequest) {
        return (root, cquery, cbuild) -> {
            List<Predicate> predicates = new ArrayList<>();
            // 用户id
            if(profileGroupBaseRequest.getUserId()!=null) {
                predicates.add(cbuild.equal(root.get("userId"), profileGroupBaseRequest.getUserId()));
            }
            Predicate[] p = predicates.toArray(new Predicate[predicates.size()]);
            return p.length == 0 ? null : p.length == 1 ? p[0] : cbuild.and(p);
        };
    }
}
