package com.lhx.birthday.service.impl;

import com.aliyuncs.sts.model.v20150401.AssumeRoleResponse;
import com.lhx.birthday.entity.Attachment;
import com.lhx.birthday.enums.DeleteFlag;
import com.lhx.birthday.mapper.AttachmentMapper;
import com.lhx.birthday.request.oss.OssAddRequest;
import com.lhx.birthday.response.StsResponse;
import com.lhx.birthday.response.UploadResponse;
import com.lhx.birthday.service.IOssFileService;
import com.lhx.birthday.util.CommonUtil;
import com.lhx.birthday.util.oConvertUtils;
import com.lhx.birthday.util.oss.OssBootUtil;
import com.lhx.birthday.vo.UploadVO;
import com.lhx.birthday.vo.UserInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * @Description: OSS云存储实现类
 * @author: jeecg-boot
 */
@Service("ossFileService")
public class OssFileServiceImpl implements IOssFileService {

	@Autowired
	private AttachmentMapper attachmentMapper;

	@Override
	public UploadVO upload(MultipartFile multipartFile, UserInfoVO userInfoVO) throws Exception {
		String fileName = multipartFile.getOriginalFilename();
		fileName = CommonUtil.getFileName(fileName);

		// 定义日期格式
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
		// 将当前日期格式化为 "年/月/日" 的格式
		String dateStr = LocalDate.now().format(formatter);
		String url = OssBootUtil.upload(multipartFile,"attach/" + dateStr);
		if(oConvertUtils.isEmpty(url)){
			throw new Exception("上传文件失败! ");
		}
		String fullUrl = OssBootUtil.getOriginalUrl(url);

		// 保存附件信息之前，检查是否为图片并获取尺寸
		String fileExtension = getFileExtension(fileName).toLowerCase();
		Integer width = null, height = null;
		if ("jpg".equals(fileExtension) || "jpeg".equals(fileExtension) || "png".equals(fileExtension) || "gif".equals(fileExtension)) {
			try {
				URL imageUrl = new URL(fullUrl);
				BufferedImage image = ImageIO.read(imageUrl.openStream());
				width = image.getWidth();
				height = image.getHeight();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}

		// 保存附件信息
		Attachment attachment = attachmentMapper.saveAndFlush(Attachment.builder()
				.userId(userInfoVO.getUserId())
				.fileName(fileName)
				.type(getFileExtension(fileName))
				.url(fullUrl)
				.width(width)
				.height(height)
				.delFlag(DeleteFlag.NO)
				.build());

		return UploadVO.builder()
				.id(attachment.getId())
				.url(fullUrl)
				.fileName(fileName)
				.build();
	}

	@Override
	public UploadVO add(OssAddRequest ossAddRequest, UserInfoVO userInfoVO) {

		String[] fileNameAndExtension = getFileNameAndExtension(ossAddRequest.getUrl());
		// 保存附件信息
		Attachment attachment = attachmentMapper.saveAndFlush(Attachment.builder()
				.userId(userInfoVO.getUserId())
				.fileName(fileNameAndExtension[0])
				.type(fileNameAndExtension[1])
				.url(ossAddRequest.getUrl())
				.width(Integer.valueOf(fileNameAndExtension[2]))
				.height(Integer.valueOf(fileNameAndExtension[3]))
				.delFlag(DeleteFlag.NO)
				.build());

		return UploadVO.builder()
				.id(attachment.getId())
				.build();
	}

	@Override
	public StsResponse sts() {
		AssumeRoleResponse assumeRoleResponse = OssBootUtil.getTemporaryCredentials();
		AssumeRoleResponse.Credentials credentials = assumeRoleResponse.getCredentials();
		return StsResponse.builder()
				.AccessKeyId(credentials.getAccessKeyId())
				.AccessKeySecret(credentials.getAccessKeySecret())
				.securityToken(credentials.getSecurityToken())
				.Expiration(credentials.getExpiration())
				.build();
	}

	public static String[] getFileNameAndExtension(String urlString) {
		try {
			URL url = new URL(urlString);
			String filePath = url.getPath();
			Path path = Paths.get(filePath);
			String fileName = path.getFileName().toString(); // 获取文件名

			int lastIndexOfDot = fileName.lastIndexOf('.');
			if (lastIndexOfDot > 0 && lastIndexOfDot < fileName.length() - 1) {
				// 获取文件扩展名
				String fileExtension = fileName.substring(lastIndexOfDot + 1);
				try (InputStream in = url.openStream()) {
					BufferedImage image = ImageIO.read(in);
					if (image != null) {
						return new String[]{fileName, fileExtension, String.valueOf(image.getWidth()), String.valueOf(image.getHeight())};
					} else {
						throw new IOException("Could not read image");
					}
				}
			} else {
				// 如果没有找到'.'，则返回文件名和null或空字符串作为扩展名
				// 或者你可以返回 "" 代替 null
				return new String[]{fileName, null, null, null};
			}
		} catch (Exception e) {
			// 处理可能的异常，例如MalformedURLException
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 获取文件的扩展名
	 * @param filePath 文件的完整路径
	 * @return 文件的扩展名（不带点），如果文件名没有扩展名则返回null
	 */
	public static String getFileExtension(String filePath) {
		if (filePath == null || filePath.isEmpty()) {
			return null;
		}

		int dotIndex = filePath.lastIndexOf('.');
		// 检查是否有'.'并且它不是字符串的第一个字符（防止像/.gitignore这样的路径）
		if (dotIndex > 0 && dotIndex < filePath.length() - 1) {
			return filePath.substring(dotIndex + 1);
		} else {
			// 如果没有找到'.'，说明文件名没有扩展名
			return null;
		}
	}
}
