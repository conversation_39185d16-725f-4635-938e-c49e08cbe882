package com.lhx.birthday.service.impl;

import com.lhx.birthday.entity.UserPaySubscribe;
import com.lhx.birthday.mapper.UserPaySubscribeMapper;
import com.lhx.birthday.request.subscribe.UserPaySubscribeBaseRequest;
import com.lhx.birthday.service.IUserPaySubscribeService;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> lhx
 * @date 2023/11/1 10:30
 */
@Service
public class UserPaySubscribeServiceImpl implements IUserPaySubscribeService {

    @Resource
    private UserPaySubscribeMapper userPaySubscribeMapper;

    @Override
    public UserPaySubscribe getByTransactionId(String transactionId) {
        UserPaySubscribeBaseRequest userPaySubscribeBaseRequest = new UserPaySubscribeBaseRequest();
        userPaySubscribeBaseRequest.setTransactionId(transactionId);
        Optional<UserPaySubscribe> userPaySubscribeOptional = userPaySubscribeMapper.findOne(this.build(userPaySubscribeBaseRequest));
        return userPaySubscribeOptional.orElse(null);
    }

    @Override
    public UserPaySubscribe addUserPaySubscribe(UserPaySubscribe userPaySubscribe) {
        return userPaySubscribeMapper.save(userPaySubscribe);
    }

    @Override
    public int updateUserIdById(Integer id, Long userId) {
        return userPaySubscribeMapper.updateUserIdById(id, userId);
    }

    private Specification<UserPaySubscribe> build(UserPaySubscribeBaseRequest userPaySubscribeBaseRequest) {
        return (root, cquery, cbuild) -> {
            List<Predicate> predicates = new ArrayList<>();
            // 交易id
            if(userPaySubscribeBaseRequest.getTransactionId()!=null) {
                predicates.add(cbuild.equal(root.get("transactionId"), userPaySubscribeBaseRequest.getTransactionId()));
            }
            Predicate[] p = predicates.toArray(new Predicate[predicates.size()]);
            return p.length == 0 ? null : p.length == 1 ? p[0] : cbuild.and(p);
        };
    }
}
