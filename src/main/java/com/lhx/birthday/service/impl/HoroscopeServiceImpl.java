package com.lhx.birthday.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lhx.birthday.constant.ApiConstant;
import com.lhx.birthday.constant.CommonConstant;
import com.lhx.birthday.entity.ConstellationProfile;
import com.lhx.birthday.entity.ProfileData;
import com.lhx.birthday.enums.LanguageType;
import com.lhx.birthday.mapper.ConstellationMapper;
import com.lhx.birthday.mapper.ProfileDataMapper;
import com.lhx.birthday.request.horoscope.BaziBaseRequest;
import com.lhx.birthday.service.IHoroscopeService;
import com.lhx.birthday.util.CommonErrorCode;
import com.lhx.birthday.util.converUtil.ConverChinese;
import com.lhx.birthday.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Year;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashSet;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Slf4j
public class HoroscopeServiceImpl implements IHoroscopeService {

    @Autowired
    private ProfileDataMapper profileDataMapper;

    @Autowired
    private ConstellationMapper constellationMapper;

    @Value("${yuanfenju.appKey}")
    private String appKey;

    @Override
    public Result JingsuanHoroscope(BaziBaseRequest baziBaseRequest) {
        Optional<ConstellationProfile> profileOptional = constellationMapper.findById(baziBaseRequest.getProFileId());
        if(profileOptional.isPresent()){
            ConstellationProfile profile = profileOptional.get();
            String key = "sect=" + baziBaseRequest.getSect() + ":zhen=" + baziBaseRequest.getZhen() + ":province=" + baziBaseRequest.getProvince() + ":city=" + baziBaseRequest.getCity() + ":lang=" + (LanguageType.EN.equals(baziBaseRequest.getLanguageType()) ? "en-us" : "zh-cn");
            ProfileData profileData = profileDataMapper.findByProfileIdAndTypeAndProfileKey(baziBaseRequest.getProFileId(), CommonConstant.BAZI_JINGSUAN,key);
            if(Objects.isNull(profileData)){
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime birthday = LocalDateTime.parse(profile.getBirthdayTime(), formatter);
                String requestData = "api_key=" + appKey +
                        "&name=" + profile.getName() +
                        "&sex=" + profile.getGender().toValue() +
                        "&type=1" +
                        "&year=" + birthday.getYear() +
                        "&month=" + birthday.getMonthValue() +
                        "&day=" + birthday.getDayOfMonth() +
                        "&hours=" + birthday.getHour() +
                        "&minute=" + birthday.getMinute() +
                        "&sect="+ baziBaseRequest.getSect() +
                        (LanguageType.EN.equals(baziBaseRequest.getLanguageType()) ? "&lang=en-us" : "");
                if(Objects.nonNull(baziBaseRequest.getZhen())){
                    requestData += "&zhen="+ baziBaseRequest.getZhen();
                }
                if(Objects.nonNull(baziBaseRequest.getProvince())){
                    requestData += "&province="+ baziBaseRequest.getProvince();
                }
                if(Objects.nonNull(baziBaseRequest.getCity())){
                    requestData += "&city="+ baziBaseRequest.getCity();
                }
                HttpResponse response = sendPostRequest(ApiConstant.API_BAZI_JINGSUAN, requestData);
                String result = response.body();
                response.close();
                JSONObject jsonObject = JSONObject.parseObject(result);
                if (jsonObject.getInteger("errcode").equals(0)) {
                    JSONObject dataObj = jsonObject.getJSONObject("data");
                    profileData = ProfileData.builder()
                            .profileId(baziBaseRequest.getProFileId())
                            .type(CommonConstant.BAZI_JINGSUAN)
                            .userId(profile.getUserId())
                            .profileKey(key)
                            .infoData(dataObj.toJSONString())
                            .build();
                    profileDataMapper.saveAndFlush(profileData);
                }else{
                    return Result.error(jsonObject.getInteger("errcode"),jsonObject.getString("errmsg"));
                }
            }
            JSONObject resultObj;
            if(baziBaseRequest.getLanguageType().equals(LanguageType.ZH_HANT)){
                resultObj = ConverChinese.convertValuesToTraditionalChinese(profileData.getInfoData());
            }else{
                resultObj = JSONObject.parseObject(profileData.getInfoData());
            }
            return Result.ok(resultObj);
        }
        return Result.error(CommonErrorCode.INVALID_NOT_FOUND_PROFILE,"未查询到档案信息");
    }

    @Override
    public Result WeilaiHoroscope(BaziBaseRequest baziBaseRequest) {
        if(!isValidYunshiYear(baziBaseRequest.getYunshiYear())){
            return Result.error(CommonErrorCode.INVALID_YUNSHI_YEAR,"运势年无效");
        }
        Optional<ConstellationProfile> profileOptional = constellationMapper.findById(baziBaseRequest.getProFileId());
        if(profileOptional.isPresent()){
            ConstellationProfile profile = profileOptional.get();
            String key = "yunshiYear=" + baziBaseRequest.getYunshiYear() + "computeDaily=" + baziBaseRequest.getComputeDaily() + "sect=" + baziBaseRequest.getSect() + ":zhen=" + baziBaseRequest.getZhen() + ":province=" + baziBaseRequest.getProvince() + ":city=" + baziBaseRequest.getCity() + ":lang=" + (LanguageType.EN.equals(baziBaseRequest.getLanguageType()) ? "en-us" : "zh-cn");
            ProfileData profileData = profileDataMapper.findByProfileIdAndTypeAndProfileKey(baziBaseRequest.getProFileId(), CommonConstant.BAZI_WEILAI,key);
            if(Objects.isNull(profileData)){
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime birthday = LocalDateTime.parse(profile.getBirthdayTime(), formatter);
                String requestData = "api_key=" + appKey +
                        "&name=" + profile.getName() +
                        "&sex=" + profile.getGender().toValue() +
                        "&type=1" +
                        "&yunshi_year=" + baziBaseRequest.getYunshiYear() +
                        "&compute_daily=" + baziBaseRequest.getComputeDaily() +
                        "&year=" + birthday.getYear() +
                        "&month=" + birthday.getMonthValue() +
                        "&day=" + birthday.getDayOfMonth() +
                        "&hours=" + birthday.getHour() +
                        "&minute=" + birthday.getMinute() +
                        "&sect="+ baziBaseRequest.getSect() +
                        (LanguageType.EN.equals(baziBaseRequest.getLanguageType()) ? "&lang=en-us" : "");
                if(Objects.nonNull(baziBaseRequest.getZhen())){
                    requestData += "&zhen="+ baziBaseRequest.getZhen();
                }
                if(Objects.nonNull(baziBaseRequest.getProvince())){
                    requestData += "&province="+ baziBaseRequest.getProvince();
                }
                if(Objects.nonNull(baziBaseRequest.getCity())){
                    requestData += "&city="+ baziBaseRequest.getCity();
                }
                HttpResponse response = sendPostRequest(ApiConstant.API_BAZI_WEILAI, requestData);
                String result = response.body();
                response.close();
                JSONObject jsonObject = JSONObject.parseObject(result);
                if (jsonObject.getInteger("errcode").equals(0)) {
                    JSONObject dataObj = jsonObject.getJSONObject("data");
                    profileData = ProfileData.builder()
                            .profileId(baziBaseRequest.getProFileId())
                            .type(CommonConstant.BAZI_WEILAI)
                            .userId(profile.getUserId())
                            .profileKey(key)
                            .infoData(dataObj.toJSONString())
                            .build();
                    profileDataMapper.saveAndFlush(profileData);
                }else{
                    return Result.error(jsonObject.getInteger("errcode"),jsonObject.getString("errmsg"));
                }
            }
            JSONObject resultObj;
            if(baziBaseRequest.getLanguageType().equals(LanguageType.ZH_HANT)){
                resultObj = ConverChinese.convertValuesToTraditionalChinese(profileData.getInfoData());
            }else{
                resultObj = JSONObject.parseObject(profileData.getInfoData());
            }
            return Result.ok(resultObj);
        }
        return Result.error(CommonErrorCode.INVALID_NOT_FOUND_PROFILE,"未查询到档案信息");
    }

    private HttpResponse sendPostRequest(String url, String requestData) {
        // 发送POST请求并获取响应
        HttpResponse response = HttpRequest.post(url)
                .header("Content-Type", "application/x-www-form-urlencoded")
                .body(requestData)
                .timeout(7000)
                .execute();

        if (response.getStatus() != 200) {
            // 如果不是200 OK状态，可以在这里处理异常情况
            throw new RuntimeException("请求失败，HTTP状态码：" + response.getStatus());
        }

        return response;
    }

    public boolean isValidYunshiYear(int yesr) {
        LocalDate currentDate = LocalDate.now(); // 获取当前日期
        int currentYear = Year.from(currentDate).getValue();

        // 判断年份是否为今年或明年
        return (yesr == currentYear || yesr == currentYear + 1);
    }

    /**
     * 提取指定字符前的数字
     * @param text 需要处理的文本
     * @param keyword 关键字，此处为"分"
     * @return 关键字前的数字，如果找不到则返回null
     */
    public static String extractNumberBeforeWord(String text, String keyword) {
        if (text == null || keyword == null) {
            return null;
        }

        // 使用正则表达式找到关键字前的数字
        // \d+ 匹配一个或多个数字
        // (?=关键词) 是一个正向前瞻断言，确保匹配的是关键字前面的内容
        String patternString = "(\\d+)(?=" + Pattern.quote(keyword) + ")";
        Pattern pattern = Pattern.compile(patternString);
        Matcher matcher = pattern.matcher(text);

        if (matcher.find()) {
            // 找到匹配项后，转换为整数并返回
            return matcher.group(1);
        } else {
            // 未找到匹配项，返回null
            return null;
        }
    }


}
