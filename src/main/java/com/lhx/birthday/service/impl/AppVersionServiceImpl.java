package com.lhx.birthday.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.lhx.birthday.entity.AppVersion;
import com.lhx.birthday.entity.Attachment;
import com.lhx.birthday.mapper.AppVersionMapper;
import com.lhx.birthday.response.version.AppVersionResponse;
import com.lhx.birthday.service.IAppVersionService;
import com.lhx.birthday.vo.version.AppVersionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 发现模块服务
 */
@Slf4j
@Service
public class AppVersionServiceImpl implements IAppVersionService {

    @Autowired
    private AppVersionMapper appVersionMapper;

    @Override
    public AppVersionResponse getList() {
        List<AppVersion> versions = appVersionMapper.findByState(1);
        List<AppVersionVO> versionVOList = new ArrayList<>();
        versions.stream().sorted(Comparator.comparing(AppVersion::getSortNo)).forEach(version -> {
            versionVOList.add(AppVersionVO.builder()
                    .sourceChannel(version.getSourceChannel())
                    .targetChannel(version.getTargetChannel())
                    .version(version.getVersion())
                    .url(version.getUrl())
                    .build());
        });
        return AppVersionResponse.builder()
                .versions(versionVOList)
                .build();
    }

}
