package com.lhx.birthday.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSONObject;
import com.lhx.birthday.entity.MarketRewardVip;
import com.lhx.birthday.entity.MarketStarRate;
import com.lhx.birthday.entity.SystemConfig;
import com.lhx.birthday.entity.UserInfo;
import com.lhx.birthday.enums.DefaultFlag;
import com.lhx.birthday.enums.RewardReason;
import com.lhx.birthday.enums.RewardType;
import com.lhx.birthday.enums.RewardVipUnit;
import com.lhx.birthday.mapper.MarketRewardVipMapper;
import com.lhx.birthday.mapper.MarketStarRateMapper;
import com.lhx.birthday.mapper.SystemConfigMapper;
import com.lhx.birthday.mapper.UserInfoMapper;
import com.lhx.birthday.service.IMarketRewardVipService;
import com.lhx.birthday.service.IMarketService;
import com.lhx.birthday.util.NumUtil;
import com.lhx.birthday.util.SpringContextUtil;
import com.lhx.birthday.vo.UserInfoVO;
import com.lhx.birthday.vo.market.Market5StarReviewVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;

import static com.lhx.birthday.constant.SettingConstant.IS_VIP;
import static com.lhx.birthday.constant.SettingConstant.NOT_VIP;

@Service
@Slf4j
public class MarketRewardVipServiceImpl implements IMarketRewardVipService {

    @Autowired
    private MarketRewardVipMapper marketRewardVipMapper;

    @Autowired
    private UserInfoMapper userInfoMapper;

    @Autowired
    private UserInfoServiceImpl userInfoService;

    public MarketRewardVip createEntity(UserInfoVO user, RewardReason reason, RewardVipUnit unit, Integer value) {
        Integer afterValue = NumUtil.add(user.getVipHours(), RewardVipUnit.toHours(unit, value));
        MarketRewardVip reward = MarketRewardVip.builder()
                .reason(reason)
                .userId(user.getUserId())
                .unit(unit)
                .value(value)
                .beforeValue(user.getVipHours())
                .afterValue(afterValue)
                .build();
        marketRewardVipMapper.save(reward);
        return reward;
    }

    public void updateUserVip(UserInfoVO user, MarketRewardVip reward) {
        LocalDateTime iapExpiryDate = user.autoIapExpiryDateNullable();
        LocalDateTime expiryDate = user.autoExpiryDateNullable();
        LocalDateTime now = LocalDateTime.now();
        Integer addHours = reward.toHours();

        // 计算需要补差的时间
        UserInfo userInfo = userInfoMapper.findById(user.getUserId()).get();
        userInfo.setStar5State(user.getStar5State());
        userInfo.setTotalVipHours(NumUtil.add(user.getTotalVipHours(), addHours));
        if (iapExpiryDate != null) {
            if(Objects.isNull(userInfo.getRewardVipBeginDate())){
                userInfo.setRewardVipBeginDate(now);
            }
            Duration duration = Duration.between(userInfo.getRewardVipBeginDate(),now);
            if(duration.toMillis()<0 && userInfo.getRewardVipBeginDate().isBefore(iapExpiryDate)){
                userInfo.setRewardVipBeginDate(iapExpiryDate.plusHours(addHours).plusMinutes(Math.abs(duration.toMinutes())));
            }else if(userInfo.getRewardVipBeginDate().isAfter(iapExpiryDate)){
                userInfo.setRewardVipBeginDate(userInfo.getRewardVipBeginDate().plusHours(addHours));
            }else{
                userInfo.setRewardVipBeginDate(iapExpiryDate.plusHours(addHours));
            }
        } else {
            if (expiryDate == null) {
                    userInfo.setRewardVipBeginDate(now.plusHours(addHours));
            } else {
                if (user.getRewardVipBeginDate() == null) {
                    userInfo.setRewardVipBeginDate(now.plusHours(addHours));
                }else{
                    userInfo.setRewardVipBeginDate(userInfo.getRewardVipBeginDate().plusHours(addHours));
                }
            }
        }
        if(userInfo.getVip().equals(NOT_VIP)){
            userInfo.setVip(IS_VIP);
        }
        userInfoMapper.saveAndFlush(userInfo);
    }

    public void resetUserVip(UserInfoVO userVo) {
        LocalDateTime iapExpiryDate = userVo.autoIapExpiryDateNullable();
        LocalDateTime expiryDate = userVo.autoExpiryDateNullable();
        LocalDateTime now = LocalDateTime.now();

        UserInfo userInfo = userInfoMapper.findById(userVo.getUserId()).get();
        if (iapExpiryDate != null) {
            userInfo.initRewardVip(userInfo, iapExpiryDate, userInfo.getVipHours());
            userInfoMapper.save(userInfo);
            return;
        }

        if (expiryDate == null && NumUtil.lte(userVo.getVipHours(), 0)) {
            userInfo.clearRewardVip(userInfo);
            userInfoMapper.save(userInfo);
            return;
        }

        if (userVo.getRewardVipBeginDate() == null) {
            userInfo.setRewardVipBeginDate(now);
            userInfo.setRewardVipBeginDate(now);
            userInfoMapper.save(userInfo);
            return;
        }

        // 假设 user.getRewardVipBeginDate() 返回的是一个 LocalDateTime 类型的值
        LocalDateTime rewardVipBeginDate = userVo.getRewardVipBeginDate();

        // 计算两个日期之间的时间差（以小时为单位）
        Long passHours = Duration.between(rewardVipBeginDate, now).toHours();

//        Long passHours = DateUtil.between(user.getRewardVipBeginDate(), now, DateUnit.HOUR);
        Integer validHours = NumUtil.sub(userVo.getVipHours(), passHours.intValue());
        if (validHours < 0 || validHours > userVo.getVipHours()) {
            validHours = 0;
        }
        userInfo.initRewardVip(userInfo, now, validHours);
        userInfoMapper.save(userInfo);
    }
}
