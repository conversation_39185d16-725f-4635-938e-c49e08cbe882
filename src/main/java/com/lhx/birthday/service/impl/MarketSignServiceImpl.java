package com.lhx.birthday.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.lhx.birthday.constant.SystemConfigConstant;
import com.lhx.birthday.entity.*;
import com.lhx.birthday.enums.RewardReason;
import com.lhx.birthday.enums.RewardVipUnit;
import com.lhx.birthday.mapper.MarketRewardVipMapper;
import com.lhx.birthday.mapper.MarketSignMapper;
import com.lhx.birthday.mapper.SystemConfigMapper;
import com.lhx.birthday.mapper.UserInfoMapper;
import com.lhx.birthday.request.market.SignHisRequest;
import com.lhx.birthday.request.market.SignRequest;
import com.lhx.birthday.service.IMarketRewardVipService;
import com.lhx.birthday.service.IMarketSignService;
import com.lhx.birthday.util.DtUtil;
import com.lhx.birthday.util.NumUtil;
import com.lhx.birthday.util.SafeUtil;
import com.lhx.birthday.vo.Result;
import com.lhx.birthday.vo.UserInfoVO;
import com.lhx.birthday.vo.market.MarketSettingVO;
import com.lhx.birthday.vo.market.SignInHistoryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.*;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.date.LocalDateTimeUtil.isSameDay;
import static com.lhx.birthday.constant.SystemConfigConstant.MARKET_CONFIG_KEY;
import static com.lhx.birthday.util.CommonErrorCode.INVITE_CODE;
import static com.lhx.birthday.util.CommonErrorCode.SIGN_DUP;

@Service
@Slf4j
public class MarketSignServiceImpl implements IMarketSignService {

    @Autowired
    private MarketSignMapper marketSignMapper;

    @Autowired
    private MarketRewardPointServiceImpl marketRewardPointService;

    @Autowired
    private SystemConfigMapper systemConfigMapper;

    @Override
    public Result signIn(UserInfoVO user, SignRequest signRequest) {
        String configKey = SystemConfigConstant.MARKET_CONFIG_TYPE_IOS;
        if(Objects.nonNull(signRequest.getDevice()) && signRequest.getDevice().equals(1)){
            configKey = SystemConfigConstant.MARKET_CONFIG_TYPE_ANDROID;
        }
        SystemConfig systemConfig = systemConfigMapper.findByConfigKeyAndConfigType(MARKET_CONFIG_KEY, configKey);
        JSONObject jsonObject = JSONObject.parseObject(systemConfig.getContext());

        MarketSign lastOne = marketSignMapper.getLastOne(user.getUserId());

        LocalDateTime today = LocalDateTime.now();
        if (lastOne != null && today.toLocalDate().equals(lastOne.getCreateTime().toLocalDate())) {
//            return Result.error(SIGN_DUP,"今日已经打卡");
            return Result.error("今日已经打卡");
        }

        MarketSign newOne = null;
        if (isTodayMonday(today)) {
            newOne = createSignIn(user, jsonObject, today, 1);
        } else {
            if (lastOne != null && isSameDay(LocalDateTime.now().minusDays(1),lastOne.getCreateTime())) {
                newOne = createSignIn(user, jsonObject, today, lastOne.getSeq() + 1);
            } else {
                newOne = createSignIn(user, jsonObject, today, 1);
            }
        }

        MarketRewardPoint reward = marketRewardPointService.createAdd(user, RewardReason.SIGN, newOne.getValue());
        marketRewardPointService.updateUserPoint(user, reward);
        return Result.ok();
    }

    @Override
    public SignInHistoryVO getSignInHistory(UserInfoVO user, SignHisRequest signHisRequest) {
        Date todayDt = new Date();
        Date mondayDt = DtUtil.getMonday(todayDt);
        String today = DateUtil.formatDate(todayDt);
        List<String> dateList = DtUtil.dateListOf(mondayDt, 7);
        List<SignInHistoryVO.Item> weeks = new ArrayList<>(7);

        List<MarketSign> entityList = marketSignMapper.getLastWeek(user.getUserId(),
                DateUtil.formatDateTime(mondayDt));
        MarketSign lastOne = SafeUtil.last(entityList, null);
        Map<String, List<MarketSign>> entityMap = entityList.stream().collect(Collectors.groupingBy(MarketSign::getDate));

        int week = 1;
        for (String d : dateList) {
            SignInHistoryVO.Item item = SignInHistoryVO.Item.builder()
                    .date(d)
                    .week(week)
                    .state(0)
                    .integral(0)
                    .seq(0)
                    .build();
            MarketSign dEntity = SafeUtil.at(entityMap.get(d), 0);
            if (dEntity != null) {
                item.setState(1);
                item.setIntegral(dEntity.getValue());
                item.setSeq(dEntity.getSeq());
            }

            weeks.add(item);
            week++;
        }

        boolean todayHas = lastOne != null && lastOne.getDate().equals(today);
        int cnt = getContinueCount(weeks, today);
        updateWeekItems(weeks, today, lastOne,signHisRequest);
        return SignInHistoryVO.builder()
                .count(cnt)
                .state(todayHas ? 1 : 0)
                .weeks(weeks)
                .build();
    }

    void updateWeekItems(List<SignInHistoryVO.Item> weeks, String today, MarketSign lastOne,SignHisRequest signHisRequest) {
        String configKey = SystemConfigConstant.MARKET_CONFIG_TYPE_IOS;
        if(Objects.nonNull(signHisRequest.getDevice()) && signHisRequest.getDevice().equals(1)){
            configKey = SystemConfigConstant.MARKET_CONFIG_TYPE_ANDROID;
        }
        SystemConfig systemConfig = systemConfigMapper.findByConfigKeyAndConfigType(MARKET_CONFIG_KEY, configKey);
        JSONObject jsonObject = JSONObject.parseObject(systemConfig.getContext());
        int idx = indexOfDate(weeks, today);
        SignInHistoryVO.Item todayItem = weeks.get(idx);
        int lastSeq = lastOne == null ? 0 : lastOne.getSeq();
        if (todayItem.checkSign()) {
            updateContinueWeekItems(weeks, idx + 1, lastSeq + 1, jsonObject);
        } else {
            SignInHistoryVO.Item yesterdayItem = SafeUtil.at(weeks, idx - 1);
            if (yesterdayItem != null && yesterdayItem.checkSign()) {
                updateContinueWeekItems(weeks, idx, yesterdayItem.getSeq() + 1, jsonObject);
            } else {
                updateContinueWeekItems(weeks, idx, 1, jsonObject);
            }
        }
    }

    void updateContinueWeekItems(List<SignInHistoryVO.Item> weeks, int idx, int seq, JSONObject jsonObject) {
        for (int i = idx; i < weeks.size(); i++) {
            SignInHistoryVO.Item curItem = weeks.get(i);
            curItem.setSeq(seq);
            curItem.setIntegral(signPointN(seq,jsonObject));
            seq++;
        }
    }

    int getContinueCount(List<SignInHistoryVO.Item> weeks, String today) {
        int cnt = 0;
        int idx = indexOfDate(weeks, today);
        SignInHistoryVO.Item todayItem = weeks.get(idx);
        if (todayItem.checkSign()) {
            for (int i = idx; i >= 0; i--) {
                SignInHistoryVO.Item curItem = weeks.get(i);
                if (curItem.checkSign()) {
                    cnt++;
                } else {
                    break;
                }
            }
            return cnt;
        }

        SignInHistoryVO.Item yesterdayItem = SafeUtil.at(weeks, idx - 1);
        if (yesterdayItem != null) {
            for (int i = idx - 1; i >= 0; i--) {
                SignInHistoryVO.Item curItem = weeks.get(i);
                if (curItem.checkSign()) {
                    cnt++;
                } else {
                    break;
                }
            }
            return cnt;
        }
        return 0;
    }

    int indexOfDate(List<SignInHistoryVO.Item> weeks, String d) {
        for (int i = 0; i < weeks.size(); i++) {
            if (d.equals(weeks.get(i).getDate())) {
                return i;
            }
        }
        return -1;
    }

    MarketSign createSignIn(UserInfoVO user, JSONObject jsonObject, LocalDateTime today, Integer seq) {
        Integer point = signPointN(seq,jsonObject);
        MarketSign sign = MarketSign.builder()
                .userId(user.getUserId())
                .value(point)
                .seq(seq)
                .date(DateUtil.formatDate(convertToLocalDate(today)))
                .createTime(LocalDateTime.now())
                .build();
        marketSignMapper.save(sign);
        return sign;
    }

    public Date convertToLocalDate(LocalDateTime dateTime) {
        // 将LocalDateTime转换为ZonedDateTime，这里假设使用系统默认时区
        ZonedDateTime zonedDateTime = dateTime.atZone(ZoneId.systemDefault());
        // 将ZonedDateTime转换为Instant
        Instant instant = zonedDateTime.toInstant();
        // 使用Instant构建一个java.util.Date对象
        Date date = Date.from(instant);
        return date;
    }

    public final Integer signPointN(Integer n,JSONObject jsonObject) {
        n = SafeUtil.of(n);
        switch (n) {
            case 2:
                return jsonObject.getIntValue("sign_point_d2");
            case 3:
                return jsonObject.getIntValue("sign_point_d3");
            case 4:
                return jsonObject.getIntValue("sign_point_d4");
            case 5:
                return jsonObject.getIntValue("sign_point_d5");
            case 6:
                return jsonObject.getIntValue("sign_point_d6");
            case 7:
                return jsonObject.getIntValue("sign_point_d7");
            case 1:
            default:
                return jsonObject.getIntValue("sign_point_d1");
        }
    }

    public boolean isTodayMonday(LocalDateTime today) {
        DayOfWeek dayOfWeek = today.getDayOfWeek();
        return dayOfWeek == DayOfWeek.MONDAY;
    }

    public boolean isSameDay(LocalDateTime today,LocalDateTime createTime) {
        DayOfWeek dayOfWeek = today.getDayOfWeek();
        DayOfWeek createTimeDayOfWeek = createTime.getDayOfWeek();
        return dayOfWeek == createTimeDayOfWeek;
    }

}
