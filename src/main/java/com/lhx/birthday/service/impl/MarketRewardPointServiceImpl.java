package com.lhx.birthday.service.impl;

import com.lhx.birthday.entity.MarketRewardPoint;
import com.lhx.birthday.entity.MarketRewardVip;
import com.lhx.birthday.entity.UserInfo;
import com.lhx.birthday.enums.RewardReason;
import com.lhx.birthday.enums.RewardVipUnit;
import com.lhx.birthday.mapper.MarketRewardPointMapper;
import com.lhx.birthday.mapper.MarketRewardVipMapper;
import com.lhx.birthday.mapper.UserInfoMapper;
import com.lhx.birthday.service.IMarketRewardVipService;
import com.lhx.birthday.util.NumUtil;
import com.lhx.birthday.util.SafeUtil;
import com.lhx.birthday.vo.UserInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
@Slf4j
public class MarketRewardPointServiceImpl implements IMarketRewardVipService {

    @Autowired
    private MarketRewardPointMapper marketRewardPointMapper;

    @Autowired
    private UserInfoMapper userInfoMapper;

    public void updateUserPoint(UserInfoVO user, MarketRewardPoint reward) {
        Integer totalPoint = SafeUtil.of(user.getTotalPoint());
        Integer point = SafeUtil.of(user.getPoint());
        if (NumUtil.gt0(reward.getInitValue())) {
            totalPoint += SafeUtil.of(reward.getInitValue());
        }

        if (user.getPoint() < 0) {
            point = 0;
        } else {
            point += SafeUtil.of(reward.getInitValue());
        }
        UserInfo userInfo = userInfoMapper.findById(user.getUserId()).get();
        userInfo.setPoint(point);
        userInfo.setTotalPoint(totalPoint);
        userInfoMapper.save(userInfo);
    }


    public MarketRewardPoint createAdd(UserInfoVO user, RewardReason reason, Integer value) {
        MarketRewardPoint reward = MarketRewardPoint.builder()
                .reason(reason)
                .type(MarketRewardPoint.Type.ADD)
                .userId(user.getUserId())
                .initValue(value)
                .validValue(value)
                .beforeValue(user.getPoint())
                .afterValue(user.getPoint() + value)
                .createTime(LocalDateTime.now())
                .build();
        marketRewardPointMapper.save(reward);
        return reward;
    }

    public void usePoint(UserInfoVO user, Integer point, Integer pointExpiryMonth) {
        List<MarketRewardPoint> validList = marketRewardPointMapper.getValidList(user.getUserId(), pointExpiryMonth);
        Integer p = point;
        for (MarketRewardPoint entity : validList) {
            Integer decPoint = 0;
            if (NumUtil.gt(p, entity.getValidValue())) {
                decPoint = entity.getValidValue();
            } else {
                decPoint = p;
            }
            Integer newValidPoint = entity.getValidValue() - decPoint;
            entity.setValidValue(newValidPoint);
            marketRewardPointMapper.save(entity);
//            this.updateSimple(entity, MarketRewardPoint::getValidValue, newValidPoint);
            p -= decPoint;
            if (p <= 0) {
                break;
            }
        }
    }

    public MarketRewardPoint createExpiryEntity(UserInfoVO user, Integer point) {
        if (point > 0) {
            point = -point;
        }
        Integer afterValue = NumUtil.add(user.getPoint(), point);
        afterValue = Math.max(afterValue, 0);
        MarketRewardPoint newEntity = MarketRewardPoint.builder()
                .userId(user.getUserId())
                .reason(RewardReason.NONE)
                .type(MarketRewardPoint.Type.EXPIRY)
                .initValue(point)
                .validValue(0)
                .beforeValue(user.getPoint())
                .afterValue(afterValue)
                .build();
        marketRewardPointMapper.save(newEntity);
        return newEntity;
    }

    public MarketRewardPoint createUseEntity(UserInfoVO user, Integer point) {
        if (point > 0) {
            point = -point;
        }
        Integer afterValue = NumUtil.add(user.getPoint(), point);
        afterValue = Math.max(afterValue, 0);
        MarketRewardPoint newEntity = MarketRewardPoint.builder()
                .userId(user.getUserId())
                .reason(RewardReason.NONE)
                .type(MarketRewardPoint.Type.USE)
                .initValue(point)
                .validValue(0)
                .beforeValue(user.getPoint())
                .afterValue(afterValue)
                .createTime(LocalDateTime.now())
                .build();
        marketRewardPointMapper.save(newEntity);
        return newEntity;
    }

    public List<MarketRewardPoint> listByUserId(Long userId){
        return marketRewardPointMapper.findByUserId(userId);
    }

}
