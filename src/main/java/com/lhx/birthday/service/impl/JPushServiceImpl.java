package com.lhx.birthday.service.impl;

import cn.jiguang.common.resp.APIConnectionException;
import cn.jiguang.common.resp.APIRequestException;
import cn.jiguang.common.resp.DefaultResult;
import cn.jpush.api.push.PushResult;
import cn.jpush.api.push.model.Options;
import cn.jpush.api.push.model.Platform;
import cn.jpush.api.push.model.PushPayload;
import cn.jpush.api.push.model.audience.Audience;
import cn.jpush.api.push.model.notification.AndroidNotification;
import cn.jpush.api.push.model.notification.IosAlert;
import cn.jpush.api.push.model.notification.IosNotification;
import cn.jpush.api.push.model.notification.Notification;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import com.lhx.birthday.config.JPushConfig;
import com.lhx.birthday.entity.Push;
import com.lhx.birthday.service.IJPushService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class JPushServiceImpl implements IJPushService {

    @Autowired
    private JPushConfig jPushConfig;

    /**
     * 广播 (所有平台，所有设备, 不支持附加信息)
     * @param Push 推送内容
     * @return
     */
    @Override
    public boolean pushAll(Push Push){
        return sendPush(PushPayload.newBuilder()
                .setPlatform(Platform.all())
                .setAudience(Audience.all())
                .setNotification(Notification.alert(Push.getAlert()))
                .build());
    }

    /**
     * ios广播
     * @param Push 推送内容
     * @return
     */
    @Override
    public boolean pushIos(Push Push){
        return sendPush(PushPayload.newBuilder()
                .setPlatform(Platform.ios())
                .setAudience(Audience.all())
                .setNotification(Notification.ios(Push.getAlert(), Push.getExtras()))
                .build());
    }


    @Override
    public PushPayload buildPushIos(Push Push, String sound, String... alias){
        IosAlert alert = IosAlert.newBuilder()
                .setTitleAndBody(Push.getTitle(),"", Push.getAlert())
                .build();
        IosNotification iosNotification = IosNotification.newBuilder()
                .setAlert(alert)
                .setSound(sound)
                .setBadge(1)
                .addExtras(Push.getExtras())
                .setInterruptionLevel(Push.getInterruptionLevel())
                .build();
        Notification ios = Notification.newBuilder()
                .addPlatformNotification(iosNotification)
                .build();
        Options options = Options.newBuilder()
                .setApnsProduction(true)
                .build();
        return PushPayload.newBuilder()
                .setPlatform(Platform.ios())
                .setOptions(options)
                .setAudience(Audience.alias(alias))
                .setNotification(ios)
                .build();
    }

    /**
     * ios通过alias推送 (一次推送最多 1000 个)
     * @param Push 推送内容
     * @param alias 推送id
     * @return
     */
    @Override
    public boolean pushIos(Push Push, String... alias){
        IosAlert alert = IosAlert.newBuilder()
                .setTitleAndBody(Push.getTitle(),"", Push.getAlert())
                .build();
        IosNotification iosNotification = IosNotification.newBuilder()
                .setAlert(alert)
//                .setSound(sound)
                .setInterruptionLevel("active")
//                .setBadge(1)
                .addExtras(Push.getExtras())
                .setInterruptionLevel(Push.getInterruptionLevel())
                .build();
        Notification ios = Notification.newBuilder()
                .addPlatformNotification(iosNotification)
                .build();
        Options options = Options.newBuilder()
                .setApnsProduction(true)
                .build();
        return sendPush(PushPayload.newBuilder()
                .setPlatform(Platform.ios())
                .setOptions(options)
                .setAudience(Audience.alias(alias))
                .setNotification(ios)
                .build());
    }

    /**
     * ios通过alias推送 (一次推送最多 1000 个)
     * @param Push
     * @param sound
     * @param interruptionLevel
     * @param alias
     * @return
     */
    @Override
    public boolean pushIos(Push Push, String sound, String interruptionLevel, String... alias){
        IosAlert alert = IosAlert.newBuilder()
                .setTitleAndBody(Push.getTitle(),"", Push.getAlert())
                .build();
        IosNotification iosNotification = IosNotification.newBuilder()
                .setAlert(alert)
                .setSound(sound)
                .setInterruptionLevel(interruptionLevel)
//                .setBadge(1)
                .addExtras(Push.getExtras())
                .setInterruptionLevel(Push.getInterruptionLevel())
                .build();
        Notification ios = Notification.newBuilder()
                .addPlatformNotification(iosNotification)
                .build();
        Options options = Options.newBuilder()
                .setApnsProduction(true)
                .build();
        return sendPush(PushPayload.newBuilder()
                .setPlatform(Platform.ios())
                .setOptions(options)
                .setAudience(Audience.alias(alias))
                .setNotification(ios)
                .build());
    }

    /**
     * android广播
     * @param Push 推送内容
     * @return
     */
    @Override
    public boolean pushAndroid(Push Push){
        return sendPush(PushPayload.newBuilder()
                .setPlatform(Platform.android())
                .setAudience(Audience.all())
                .setNotification(Notification.android(Push.getAlert(), Push.getTitle(), Push.getExtras()))
                .build());
    }

    /**
     * android通过registid推送 (一次推送最多 1000 个)
     * @param Push 推送内容
     * @param alias 推送id
     * @return
     */
    @Override
    public boolean pushAndroid(Push Push, String ... alias){
//        IosAlert alert = IosAlert.newBuilder()
//                .setTitleAndBody(Push.getTitle(),"", Push.getAlert())
//                .build();
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("url","intent:#Intent;action=android.intent.action.MAIN;end");
        AndroidNotification androidNotification = AndroidNotification.newBuilder()
                .setAlert(Push.getAlert())
                .setTitle( Push.getTitle())
                .addExtras(Push.getExtras())
                .setIntent(jsonObject)
                .build();
        Notification android = Notification.newBuilder()
                .addPlatformNotification(androidNotification)
                .build();
//        Notification android = Notification.android(Push.getAlert(), Push.getTitle(), Push.getExtras());
        Options options = Options.newBuilder()
                .setApnsProduction(true)
                .build();
        return sendPush(PushPayload.newBuilder()
                .setPlatform(Platform.android())
                .setOptions(options)
                .setAudience(Audience.alias(alias))
                .setNotification(android)
                .build());
//        return sendPush(PushPayload.newBuilder()
//                .setPlatform(Platform.android())
//                .setAudience(Audience.alias(alias))
//                .setNotification(Notification.android(Push.getAlert(), Push.getTitle(), Push.getExtras()))
//                .build());
    }

    /**
     * 调用api推送
     * @param pushPayload 推送实体
     * @return
     */
    @Override
    public boolean sendPush(PushPayload pushPayload){
        log.info("发送极光推送请求: {}", pushPayload);
        PushResult result = null;
        try{
            result = jPushConfig.getJPushClient().sendPush(pushPayload);
        } catch (APIConnectionException e) {
            log.error("极光推送连接异常: ", e);
        } catch (APIRequestException e) {
            log.error("极光推送请求异常: ", e);
        }
        if (result!=null && result.isResultOK()) {
            log.info("极光推送请求成功: {}", result);
            return true;
        }else {
            log.info("极光推送请求失败: {}", result);
            return false;
        }
    }

    @Override
    public boolean registerPush(String registrationId, String alias) {
        try {
            DefaultResult defaultResult =  jPushConfig.getJPushClient().updateDeviceTagAlias(registrationId, alias, null, null);
            log.info("设置别名结果：" + defaultResult.isResultOK());
        } catch (APIConnectionException e) {
            e.printStackTrace();
        } catch (APIRequestException e) {
            e.printStackTrace();
            log.error("错误", e);
        }
        return false;
    }
}
