package com.lhx.birthday.service.impl;

import com.lhx.birthday.entity.CountryCode;
import com.lhx.birthday.entity.UserInfo;
import com.lhx.birthday.mapper.CountryCodeMapper;
import com.lhx.birthday.mapper.UserInfoMapper;
import com.lhx.birthday.request.profile.RegionRequest;
import com.lhx.birthday.service.CountryCodeService;
import com.lhx.birthday.service.IFeedbackService;
import com.lhx.birthday.vo.UserInfoVO;
import com.lhx.birthday.vo.profile.RegionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class ICountryCodeServiceImpl implements CountryCodeService {

    @Autowired
    private CountryCodeMapper countryCodeMapper;

    @Autowired
    private UserInfoMapper userInfoMapper;

    @Override
    public List<RegionVO> getCountryCodeList(RegionRequest regionRequest) {
        List<CountryCode> countryCodes;
        if(Objects.isNull(regionRequest.getIsoCountryCode())){
            // 获取国家列表
            countryCodes = countryCodeMapper.getCountryList();
            // 将中国移到第一位
            List<CountryCode> sortedCodes = new ArrayList<>();
            CountryCode chinaCode = null;
            for (CountryCode code : countryCodes) {
                if ("CHN".equals(code.getIsoCountryCode())) {
                    chinaCode = code;
                } else {
                    sortedCodes.add(code);
                }
            }
            if (chinaCode != null) {
                sortedCodes.add(0, chinaCode);
            }
            countryCodes = sortedCodes;
        } else {
            // 按层级查询
            countryCodes = countryCodeMapper.findAll(this.build(regionRequest));
        }
        
        List<RegionVO> regionVOS = new ArrayList<>();
        for (CountryCode code : countryCodes) {
            regionVOS.add(RegionVO.builder()
                    .countryChn(code.getCountryChn())
                    .countryEng(code.getCountryEng())
                    .level1Chn(code.getLevel1Chn())
                    .level1Eng(code.getLevel1Eng())
                    .level2Chn(code.getLevel2Chn())
                    .level2Eng(code.getLevel2Eng())
                    .level3Chn(code.getLevel3Chn())
                    .level3Eng(code.getLevel3Eng())
                    .isoCountryCode(code.getIsoCountryCode())
                    .lat(code.getLat())
                    .lon(code.getLon())
                    .id(code.getId())
                    .build());
        }
        
        // 根据层级排序
        return regionVOS.stream()
                .sorted((a, b) -> {
                    // 如果是国家级别的查询，确保中国在第一位
                    if (Objects.isNull(regionRequest.getIsoCountryCode())) {
                        if ("CHN".equals(a.getIsoCountryCode())) return -1;
                        if ("CHN".equals(b.getIsoCountryCode())) return 1;
                        return a.getCountryEng().compareTo(b.getCountryEng());
                    }
                    // 如果是省级查询
                    if (Objects.isNull(regionRequest.getLevel1Eng())) {
                        return a.getLevel1Eng().compareTo(b.getLevel1Eng());
                    }
                    // 如果是市级查询
                    if (Objects.isNull(regionRequest.getLevel2Eng())) {
                        return a.getLevel2Eng().compareTo(b.getLevel2Eng());
                    }
                    // 如果是区级查询
                    return a.getLevel3Eng().compareTo(b.getLevel3Eng());
                })
                .collect(Collectors.toList());
    }

    public void setUserRegion(Double lat, Double lon, Long userId){
        CountryCode countryCode = countryCodeMapper.findDis(lat, lon);
        if(Objects.nonNull(countryCode)){
            UserInfo userInfo = userInfoMapper.findById(userId).get();
            userInfo.setCountryCodeId(countryCode.getId());
            userInfoMapper.saveAndFlush(userInfo);
        }
    }

    public void setIp(String ip,Long userId){
        UserInfo userInfo = userInfoMapper.findById(userId).get();
        userInfo.setIpAddr(ip);
        userInfoMapper.saveAndFlush(userInfo);
    }

    public void setInfo(UserInfoVO userInfoVO, String osType, String channel, String version, String deviceBrand, String systemName){
        UserInfo userInfo = userInfoMapper.findById(userInfoVO.getUserId()).get();
        if(Objects.isNull(userInfoVO.getDeviceBrand())){
            userInfoVO.setDeviceBrand("");
        }
        if(Objects.isNull(userInfoVO.getOsType())){
            userInfoVO.setOsType("");
        }
        if(Objects.isNull(userInfoVO.getChannel())){
            userInfoVO.setChannel("");
        }
        if(Objects.isNull(userInfoVO.getVersion())){
            userInfoVO.setVersion("");
        }
//        if(Objects.isNull(userInfoVO.getSystemName())){
//            userInfoVO.setSystemName("");
//        }
        if(Objects.isNull(deviceBrand)){
            deviceBrand = "";
        }
        if(Objects.isNull(osType)){
            osType = "";
        }
        if(Objects.isNull(channel)){
            channel = "";
        }
        if(Objects.isNull(version)){
            version = "";
        }
        if(Objects.isNull(systemName)){
            systemName = "";
        }
        boolean needsUpdate =
                (userInfoVO.getOsType() != null && !userInfoVO.getOsType().equals(osType) && !osType.isEmpty()) ||
                        (userInfoVO.getChannel() != null && !userInfoVO.getChannel().equals(channel) && !channel.isEmpty()) ||
                        (userInfoVO.getDeviceBrand() != null && !userInfoVO.getDeviceBrand().equals(deviceBrand) && !deviceBrand.isEmpty()) ||
                        (userInfoVO.getVersion() != null && !userInfoVO.getVersion().equals(version) && !version.isEmpty());
        if(needsUpdate){
            userInfo.setOsType(osType);
            userInfo.setVersion(version);
            userInfo.setChannel(channel);
            userInfo.setDeviceBrand(deviceBrand);
        }
//        if(Objects.isNull(userInfoVO.getSystemName()) && Objects.nonNull(systemName)){
//            userInfo.setSystemName(systemName);
//        }
        userInfoMapper.saveAndFlush(userInfo);
    }

    private Specification<CountryCode> build(RegionRequest regionRequest) {
        return (root, cquery, cbuild) -> {
            List<Predicate> predicates = new ArrayList<>();
            if(regionRequest.getIsoCountryCode()!=null) {
                predicates.add(cbuild.equal(root.get("isoCountryCode"), regionRequest.getIsoCountryCode()));
                if (regionRequest.getLevel1Eng() == null && regionRequest.getLevel2Eng() == null && regionRequest.getLevel3Eng() == null) {
                    cquery.multiselect(root.get("level1Eng"), cbuild.count(root)).groupBy(root.get("level1Eng"));
                }
            }
            if(regionRequest.getLevel1Eng()!=null) {
                predicates.add(cbuild.equal(root.get("level1Eng"), regionRequest.getLevel1Eng()));
                if (regionRequest.getLevel2Eng() == null && regionRequest.getLevel3Eng() == null) {
                    cquery.multiselect(root.get("level2Eng"), cbuild.count(root)).groupBy(root.get("level2Eng"));
                }
            }
            if(regionRequest.getLevel2Eng()!=null) {
                predicates.add(cbuild.equal(root.get("level2Eng"), regionRequest.getLevel2Eng()));
                if (regionRequest.getLevel3Eng() == null) {
                    cquery.multiselect(root.get("level3Eng"), cbuild.count(root)).groupBy(root.get("level3Eng"));
                }
            }
            if(regionRequest.getLevel3Eng()!=null) {
                predicates.add(cbuild.equal(root.get("level3Eng"), regionRequest.getLevel3Eng()));
            }

            Predicate[] p = predicates.toArray(new Predicate[predicates.size()]);
            return p.length == 0 ? null : p.length == 1 ? p[0] : cbuild.and(p);
        };
    }

}
