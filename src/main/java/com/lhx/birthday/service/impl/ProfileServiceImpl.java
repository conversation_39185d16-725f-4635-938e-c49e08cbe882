package com.lhx.birthday.service.impl;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.Strings;
import com.github.stuxuhai.jpinyin.PinyinHelper;
import com.lhx.birthday.entity.*;
import com.lhx.birthday.enums.*;
import com.lhx.birthday.mapper.*;
import com.lhx.birthday.request.profile.ProfileBaseRequest;
import com.lhx.birthday.request.profile.ProfileUpdateSortRequest;
import com.lhx.birthday.response.profile.ProfileGroupResponse;
import com.lhx.birthday.service.IProfileService;
import com.lhx.birthday.service.IUserInfoService;
import com.lhx.birthday.util.StringUtils;
import com.lhx.birthday.vo.profile.ProfileGroupVO;
import com.lhx.birthday.vo.profile.ProfileVO;
import com.tyme.culture.Constellation;
import com.tyme.culture.Zodiac;
import com.tyme.lunar.LunarDay;
import com.tyme.lunar.LunarHour;
import com.tyme.lunar.LunarMonth;
import com.tyme.sixtycycle.EarthBranch;
import com.tyme.solar.SolarDay;
import com.xkzhangsan.time.LunarDate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import javax.persistence.criteria.Predicate;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.lhx.birthday.constant.SettingConstant.*;

@Service
@Slf4j
public class ProfileServiceImpl implements IProfileService {

    @Autowired
    private ProfileMapper profileMapper;

    @Autowired
    private ProfilePromptMapper profilePromptMapper;

    @Autowired
    private ProfileGroupRelMapper profileGroupRelMapper;

    @Autowired
    private ProfileGroupMapper profileGroupMapper;

    @Autowired
    private BirthdayInfoMapper birthdayInfoMapper;

    @Autowired
    private IUserInfoService userInfoService;

    @Value("${oss.staticDomain}")
    private String domain;

    @Override
    public ProfileVO addProfile(ProfileBaseRequest profileBaseRequest) {
        Profile addProfile = new Profile();
        BeanUtils.copyProperties(profileBaseRequest,addProfile);
        addProfile.setSort(0);
        addProfile.setCreateTime(LocalDateTime.now());
        addProfile.setModifyTime(LocalDateTime.now());
        if(Objects.isNull(addProfile.getAvatar()) || StringUtils.isEmpty(addProfile.getAvatar())){
            addProfile.setAvatar(domain+DEFAULT_PROFILE_AVATAR);
        }
        // 公历
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime birthdayTime = LocalDateTime.parse(profileBaseRequest.getBirthdayTime(),formatter);
        addProfile.setSolarTime(birthdayTime);
        // 公历转换农历
        SolarDay solarDay = SolarDay.fromYmd(birthdayTime.getYear(),birthdayTime.getMonthValue(), birthdayTime.getDayOfMonth());
        LunarDay lunarDay = solarDay.getLunarDay();
        String lunarStr = lunarDay.getYear() + "/" + lunarDay.getMonth() + "/" + lunarDay.getDay() + " "+birthdayTime.toLocalTime().toString();
        addProfile.setLunarTime(lunarStr);
        // 计算生肖星座
        Zodiac zodiac = lunarDay.getYearSixtyCycle().getEarthBranch().getZodiac();
        Constellation constellation = lunarDay.getSolarDay().getConstellation();
        addProfile.setZodiacSignType(ZodiacSignType.values()[constellation.getIndex()]);
        addProfile.setZodiacType(ZodiacType.values()[zodiac.getIndex()]);
        Profile profile = profileMapper.saveAndFlush(addProfile);
        // 保存档案通知信息
        ProfilePrompt addProfilePrompt = new ProfilePrompt();
        BeanUtils.copyProperties(profileBaseRequest,addProfilePrompt);
        addProfilePrompt.setProfileId(profile.getId());
        // 计算出今年生日
        addProfilePrompt.setSolarTime(addProfile.getSolarTime());
        addProfilePrompt.setLunarTime(addProfile.getLunarTime());
        addProfilePrompt.setCreateTime(LocalDateTime.now());
        addProfilePrompt.setModifyTime(LocalDateTime.now());
        // 保存下次生日信息
        Map<String, String> solarMap = calculateNextBirthday(profile.getSolarTime().toLocalDate(),LocalDate.now());
        String solarNextBirthday = solarMap.get("nextBirthday");
        addProfilePrompt.setNextSolarTime(solarNextBirthday);
        String[] lunarSpl = profile.getLunarTime().split(" ")[0].split("/");
        Map<String, String> lunarMap = calculateNextLunarBirthday(Integer.parseInt(lunarSpl[0]),Integer.parseInt(lunarSpl[1]),Integer.parseInt(lunarSpl[2]),LocalDate.now());
        String lunarNextBirthday = lunarMap.get("nextBirthday");
        addProfilePrompt.setNextLunarTime(lunarNextBirthday);
        profilePromptMapper.saveAndFlush(addProfilePrompt);
        // 保存分组信息
        if(Objects.nonNull(profileBaseRequest.getGroupIds()) && !StringUtils.isEmpty(profileBaseRequest.getGroupIds())){
            for (String id : profileBaseRequest.getGroupIds().split(",")) {
                ProfileGroupRel profileGroupRel = ProfileGroupRel.builder()
                        .profileGroupId(Long.valueOf(id))
                        .profileId(profile.getId())
                        .userId(profile.getUserId())
                        .createTime(LocalDateTime.now())
                        .modifyTime(LocalDateTime.now())
                        .build();
                profileGroupRelMapper.saveAndFlush(profileGroupRel);
            }
        }
        ProfileVO profileVO = new ProfileVO();
        BeanUtils.copyProperties(profile, profileVO);
        // 新增档案推送
        if(Objects.nonNull(profileBaseRequest.getRegistrationId())){
            userInfoService.initPushData(Collections.singletonList(addProfilePrompt), ActionUnit.ADD,null);
        }
        return profileVO;
    }

    @Override
    @Transactional
    public ProfileVO updateProfile(ProfileBaseRequest profileBaseRequest) {
        Optional<Profile> profileOptional = profileMapper.findById(profileBaseRequest.getId());
        if(profileOptional.isPresent()){
            Profile profile = profileOptional.get();
            profile.setModifyTime(LocalDateTime.now());
            profile.setName(profileBaseRequest.getName());
            profile.setGender(profileBaseRequest.getGender());
            profile.setAvatar(profileBaseRequest.getAvatar());
            profile.setBirthdayType(profileBaseRequest.getBirthdayType());
            // 公历
            LunarDay lunarDay;
//            if(profileBaseRequest.getBirthdayType().equals(BirthdayType.SOLAR)){
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime birthdayTime = LocalDateTime.parse(profileBaseRequest.getBirthdayTime(),formatter);
                profile.setSolarTime(birthdayTime);
                // 公历转换农历
                SolarDay solarDay = SolarDay.fromYmd(birthdayTime.getYear(),birthdayTime.getMonthValue(), birthdayTime.getDayOfMonth());
                lunarDay = solarDay.getLunarDay();
                String lunarStr = lunarDay.getYear() + "/"+lunarDay.getMonth()+"/"+lunarDay.getDay()+" "+birthdayTime.toLocalTime().toString();
                profile.setLunarTime(lunarStr);
//            }
            // 农历
//            else{
//                profile.setLunarTime(profileBaseRequest.getBirthdayTime());
                // 农历转公历
//                String[] lunarSpl = profileBaseRequest.getBirthdayTime().split(" ")[0].split("-");
//                String[] timeSpl = profileBaseRequest.getBirthdayTime().split(" ")[1].split(":");
//                lunarDay = LunarDay.fromYmd(Integer.parseInt(lunarSpl[0]),Integer.parseInt(lunarSpl[1]),Integer.parseInt(lunarSpl[2]));
//                SolarDay solarDay = lunarDay.getSolarDay();
//                profile.setSolarTime(LocalDateTime.of(LocalDate.of(solarDay.getYear(),solarDay.getMonth(),solarDay.getDay()),LocalTime.of(Integer.parseInt(timeSpl[0]),Integer.parseInt(timeSpl[1]))));
//            }
            profile.setTimeZone(profileBaseRequest.getTimeZone());
            profile.setRemarkInfo(profileBaseRequest.getRemarkInfo());
            if(Objects.nonNull(profileBaseRequest.getRelationship())){
                profile.setRelationship(profileBaseRequest.getRelationship());
            }
            // 计算生肖星座
            Zodiac zodiac = lunarDay.getYearSixtyCycle().getEarthBranch().getZodiac();
            Constellation constellation = lunarDay.getSolarDay().getConstellation();
            profile.setZodiacSignType(ZodiacSignType.values()[constellation.getIndex()]);
            profile.setZodiacType(ZodiacType.values()[zodiac.getIndex()]);
            profileMapper.saveAndFlush(profile);
            // 保存档案通知信息
            ProfilePrompt profilePrompt = profilePromptMapper.findByProfileId(profileBaseRequest.getId());
            ProfilePrompt oldProfilePrompt = new ProfilePrompt();
            BeanUtils.copyProperties(profilePrompt,oldProfilePrompt);
            Long profileId = profilePrompt.getId();
            BeanUtils.copyProperties(profileBaseRequest,profilePrompt);
            profilePrompt.setSolarTime(profile.getSolarTime());
            profilePrompt.setLunarTime(profile.getLunarTime());
            profilePrompt.setModifyTime(LocalDateTime.now());
            profilePrompt.setId(profileId);
            // 保存下次生日信息
            Map<String, String> solarMap = calculateNextBirthday(profile.getSolarTime().toLocalDate(),LocalDate.now());
            String solarNextBirthday = solarMap.get("nextBirthday");
            profilePrompt.setNextSolarTime(solarNextBirthday);
            String[] lunarSpl = profile.getLunarTime().split(" ")[0].split("/");
            Map<String, String> lunarMap = calculateNextLunarBirthday(Integer.parseInt(lunarSpl[0]),Integer.parseInt(lunarSpl[1]),Integer.parseInt(lunarSpl[2]),LocalDate.now());
            String lunarNextBirthday = lunarMap.get("nextBirthday");
            profilePrompt.setNextLunarTime(lunarNextBirthday);
            profilePromptMapper.saveAndFlush(profilePrompt);
            // 保存分组信息
            if(Objects.nonNull(profileBaseRequest.getGroupIds()) && !StringUtils.isEmpty(profileBaseRequest.getGroupIds())){
                // 获取当前用户的已有分组
                List<ProfileGroupRel> existingGroups = profileGroupRelMapper.findByProfileId(profile.getId());

                // 将新分组ID转换为集合
                Set<Long> newGroupIds = Arrays.stream(profileBaseRequest.getGroupIds().split(","))
                        .map(Long::valueOf)
                        .collect(Collectors.toSet());
                // 处理新增和删除的分组
                for (Long id : newGroupIds) {
                    // 如果分组不存在，则创建新的关系
                    if (existingGroups.stream().noneMatch(group -> group.getProfileGroupId().equals(id))) {
                        ProfileGroupRel profileGroupRel = ProfileGroupRel.builder()
                                .profileGroupId(id)
                                .profileId(profile.getId())
                                .userId(profile.getUserId())
                                .createTime(LocalDateTime.now())
                                .modifyTime(LocalDateTime.now())
                                .build();
                        profileGroupRelMapper.saveAndFlush(profileGroupRel);
                    }
                }

                // 删除不再存在的分组
                for (ProfileGroupRel existingGroup : existingGroups) {
                    if (!newGroupIds.contains(existingGroup.getProfileGroupId())) {
                        profileGroupRelMapper.delete(existingGroup);
                    }
                }
            }else{
                // 如果没有分组ID，考虑清空当前用户的分组关系
                profileGroupRelMapper.deleteByProfileId(profile.getId());
            }
            ProfileVO profileVO = new ProfileVO();
            BeanUtils.copyProperties(profile, profileVO);
            // 更新档案推送
            if(Objects.nonNull(profileBaseRequest.getRegistrationId())){
                userInfoService.initPushData(Collections.singletonList(oldProfilePrompt), ActionUnit.DELETE,null);
                userInfoService.initPushData(Collections.singletonList(profilePrompt), ActionUnit.ADD,null);
            }
            return profileVO;
        }
        return null;
    }

    @Override
    public int updateSort(ProfileUpdateSortRequest profileUpdateSortRequest) {
        String idsStr = profileUpdateSortRequest.getIds();
        List<Long> idOrderList = Arrays.stream(idsStr.split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        List<Profile> profiles = profileMapper.findAllById(idOrderList);

        Map<Long, Profile> profileMap = new LinkedHashMap<>();
        for (Profile profile : profiles) {
            profileMap.put(profile.getId(), profile);
        }

        List<Profile> sortedProfiles = idOrderList.stream()
                .map(profileMap::get)
                .filter(Objects::nonNull) // 过滤掉可能在EmergencyContactes中不存在的id
                .collect(Collectors.toList());

        for (int i = 0; i < sortedProfiles.size(); i++) {
            Profile profile = sortedProfiles.get(i);
            profile.setSort(i);
            profileMapper.saveAndFlush(profile);
        }
        return 1;
    }

    @Override
    public int delById(ProfileBaseRequest profileBaseRequest) {
        profileMapper.deleteById(profileBaseRequest.getId());
        profileGroupRelMapper.deleteByProfileId(profileBaseRequest.getId());
        ProfilePrompt profilePrompt = profilePromptMapper.findByProfileId(profileBaseRequest.getId());
        // 更新档案推送
        if(Objects.nonNull(profileBaseRequest.getRegistrationId())){
            userInfoService.initPushData(Collections.singletonList(profilePrompt), ActionUnit.DELETE,null);
        }
        profilePromptMapper.deleteById(profilePrompt.getId());
        return 1;
    }

    @Override
    public List<ProfileVO> getProfileList(ProfileBaseRequest profileBaseRequest) {
        List<ProfileVO> profileVOList = new ArrayList<>();
        // 查询分组
        List<Profile> profileList = profileMapper.findAll(this.build(profileBaseRequest));

        List<Long> profileIds = new ArrayList<>();
        if(Objects.nonNull(profileBaseRequest.getGroupId())){
            List<ProfileGroupRel> profileGroupRels = profileGroupRelMapper.findByProfileGroupId(profileBaseRequest.getGroupId());
            profileIds = profileGroupRels.stream().map(ProfileGroupRel::getProfileId).collect(Collectors.toList());
        }
        profileList.sort(Comparator.comparingInt((Profile profile) ->
                        profile.getRelationship().toValue() == 0 ? -1 : // 如果Relationship为0，则返回-1以优先排序
                                profile.getSort())
                .thenComparingInt(Profile::getSort)
                .thenComparing((p1, p2) ->
                        p2.getCreateTime().compareTo(p1.getCreateTime()))); // 注意这里的reverse是通过交换比较对象实现的
        // 查询提醒数据
        List<ProfilePrompt> profilePromptList = profilePromptMapper.findByUserId(profileBaseRequest.getUserId());
        Map<Long, ProfilePrompt> profilePromptMap = profilePromptList.stream().collect(Collectors.toMap(ProfilePrompt::getProfileId, profilePrompt -> profilePrompt));
        // 查询分组数据
        List<ProfileGroupRel> profileGroupRelList = profileGroupRelMapper.findByUserId(profileBaseRequest.getUserId());
        Map<Long, List<ProfileGroupRel>> profileGroupRelListMap = profileGroupRelList.stream().collect(Collectors.groupingBy(ProfileGroupRel::getProfileId));

        for (Profile profile : profileList) {
            ProfileVO profileVO = new ProfileVO();
            BeanUtils.copyProperties(profile, profileVO);
            profileVO.setZodiacTypeStr(profile.getZodiacType().getValue());
            profileVO.setZodiacTypePic(domain+DEFAULT_ZODIAC_PIC_URL+profile.getZodiacType().toValue()+".png");
            profileVO.setZodiacSignTypePic(domain+DEFAULT_ZODIAC_SIGN_PIC_URL+profile.getZodiacSignType().toValue()+".png");
            profileVO.setZodiacSignTypeStr(profile.getZodiacSignType().getValue());
            profileVO.setRelationshipStr(profile.getRelationship().getValue());
            // 农历转换
            String[] lunarSpl = profile.getLunarTime().split(" ")[0].split("/");
            String[] lunarTimeSpl = profile.getLunarTime().split(" ")[1].split(":");
            LunarDate lunarDate = LunarDate.from(profile.getSolarTime());
            LunarHour lunarHour = LunarHour.fromYmdHms(Integer.parseInt(lunarSpl[0]), Integer.parseInt(lunarSpl[1]), Integer.parseInt(lunarSpl[2]), Integer.parseInt(lunarTimeSpl[0]), Integer.parseInt(lunarTimeSpl[1]), 0);
            profileVO.setLunarStr(lunarDate.getlMonthCn()+"月"+lunarDate.getlDayCn());
            profileVO.setLunarYearStr(lunarDate.getlYearCn()+"年"+lunarDate.getLeapMonthCn()+lunarDate.getlMonthCn()+"月"+lunarDate.getlDayCn());
            profileVO.setLunarFullStr(lunarDate.getlYearCn()+"年"+lunarDate.getLeapMonthCn()+lunarDate.getlMonthCn()+"月"+lunarDate.getlDayCn()+" " + lunarHour.getHour()+"点"+lunarHour.getMinute()+"分");
            profileVO.setSolarStr(profileVO.getSolarTime().getMonthValue()+"月"+profileVO.getSolarTime().getDayOfMonth()+"日");
            profileVO.setSolarYearStr(profileVO.getSolarTime().getYear()+"年"+profileVO.getSolarTime().getMonthValue()+"月"+profileVO.getSolarTime().getDayOfMonth()+"日");
            profileVO.setSolarFullStr(profileVO.getSolarTime().getYear()+"年"+profileVO.getSolarTime().getMonthValue()+"月"+profileVO.getSolarTime().getDayOfMonth()+"日"+ " "+profileVO.getSolarTime().getHour()+"点"+ profileVO.getSolarTime().getMinute()+"分");
            // 计算距离生日天数
            Map<String, String> solarMap = calculateNextBirthday(profileVO.getSolarTime().toLocalDate(),LocalDate.now());
            profileVO.setSolarNextAge(Integer.parseInt(solarMap.get("age")));
            profileVO.setSolarNextAgeDay(Integer.parseInt(solarMap.get("day")));

            Map<String, String> lunarMap = calculateNextLunarBirthday(Integer.parseInt(lunarSpl[0]),Integer.parseInt(lunarSpl[1]),Integer.parseInt(lunarSpl[2]),LocalDate.now());
            profileVO.setLunarNextAge(Integer.parseInt(lunarMap.get("age")));
            profileVO.setLunarNextAgeDay(Integer.parseInt(lunarMap.get("day")));
            if (profileIds.contains(profileVO.getId())) {
                profileVO.setInGroup(DefaultFlag.YES);
            } else {
                profileVO.setInGroup(DefaultFlag.NO);
            }
            // 查询单个填充星座解读数据
            if(Objects.nonNull(profileBaseRequest.getId())){
                BirthdayInfo birthdayInfo = birthdayInfoMapper.findByMonthAndDay(profileVO.getSolarTime().getMonthValue(), profileVO.getSolarTime().getDayOfMonth());
                profileVO.setBirthdayInfo(JSONObject.parseObject(birthdayInfo.getInfo()));
            }
            // 查询提醒信息
            ProfilePrompt profilePrompt = profilePromptMap.get(profileVO.getId());
            profileVO.setOnTheDay(profilePrompt.getOnTheDay());
            profileVO.setOneDayBefore(profilePrompt.getOneDayBefore());
            profileVO.setThreeDaysBefore(profilePrompt.getThreeDaysBefore());
            profileVO.setSevenDaysBefore(profilePrompt.getSevenDaysBefore());
            profileVO.setFifteenDaysBefore(profilePrompt.getFifteenDaysBefore());
            profileVO.setThirtyDaysBefore(profilePrompt.getThirtyDaysBefore());
            profileVO.setPromptTime(profilePrompt.getPromptTime());
            profileVO.setPromptFlag(profilePrompt.getPromptFlag());
            // 所属分组
            List<ProfileGroupRel> profileGroupRels = profileGroupRelListMap.get(profileVO.getId());
            if(Objects.nonNull(profileGroupRels)){
                List<String> stringList = profileGroupRels.stream().map(pg -> Long.toString(pg.getProfileGroupId())).collect(Collectors.toList());
                profileVO.setGroupIds(String.join(",", stringList));
            }else{
                profileVO.setGroupIds("");
            }

            // 填充数据
            if(Objects.nonNull(profileBaseRequest.getInGroup())){
                if(profileBaseRequest.getInGroup().equals(profileVO.getInGroup())){
                    profileVOList.add(profileVO);
                }
            }else{
                profileVOList.add(profileVO);
            }
        }
        if(Objects.isNull(profileBaseRequest.getInGroup())){
            profileVOList.sort(Comparator.comparing(ProfileServiceImpl::getSortKey));
        }else{
            profileVOList.sort(Comparator.comparing((ProfileVO p) -> p.getBirthdayType().equals(BirthdayType.SOLAR) ? p.getSolarNextAgeDay() : p.getLunarNextAgeDay()));
        }
        return profileVOList;
    }

    private static String getSortKey(ProfileVO p) {
        String name = p.getName();
        if (name == null || name.isEmpty()) {
            return "\uFFFF"; // 空或空白名称排在最后
        }
        char firstChar = name.charAt(0);


        // 如果是中文字符，则返回其拼音首字母
        if (Character.toString(firstChar).matches("[\\u4E00-\\u9FA5]")) { // 判断是否为中文字符
            try {
                String pinyin = PinyinHelper.getShortPinyin(name.substring(0,1));
                return pinyin.substring(0, 1).toLowerCase(Locale.ROOT);
            }catch (Exception e){
                e.printStackTrace();
                return "";
            }
        }

        // 如果是字母，则直接返回小写形式
        if (Character.isLetter(firstChar)) {
            return name.toLowerCase(Locale.ROOT);
        }

        // 非字母也非中文字符，排在最后
        return "\uFFFF" + name;
    }

    @Override
    public ProfileGroupResponse getGroupList(ProfileBaseRequest profileBaseRequest) {
        List<ProfileVO> profileList = getProfileList(profileBaseRequest);
        // 查询分组数据
        List<ProfileGroupVO> profileGroupVOList = new ArrayList<>();
        List<ProfileGroup> profileGroupList = profileGroupMapper.findByUserId(profileBaseRequest.getUserId());
        profileGroupList.sort(Comparator.comparingInt(ProfileGroup::getSort)
                .thenComparing(ProfileGroup::getCreateTime));
        List<ProfileGroupRel> profileGroupRels = profileGroupRelMapper.findByUserId(profileBaseRequest.getUserId());
        Map<Long, List<ProfileGroupRel>> relMap = profileGroupRels.stream().collect(Collectors.groupingBy(ProfileGroupRel::getProfileGroupId));
        // 加入默认分组
        ProfileGroupVO defaultGroupVO = new ProfileGroupVO();
        List<ProfileVO> defProfiles = profileList.stream()
                .filter(profile -> profile.getRelationship().toValue() == 1)
                .collect(Collectors.toList());
        defaultGroupVO.setCount(defProfiles.size());
        defaultGroupVO.setGroupName("默认分组");
        defProfiles.sort(Comparator.comparing((ProfileVO p) -> p.getBirthdayType().equals(BirthdayType.SOLAR) ? p.getSolarNextAgeDay() : p.getLunarNextAgeDay()));
        defaultGroupVO.setProfiles(defProfiles);
        profileGroupVOList.add(defaultGroupVO);

        for (ProfileGroup profileGroup : profileGroupList) {
            ProfileGroupVO profileGroupVO = new ProfileGroupVO();
            BeanUtils.copyProperties(profileGroup, profileGroupVO);
            // 查询分组档案数量
            int size = relMap.getOrDefault(profileGroup.getId(),new ArrayList<>()).size();
            profileGroupVO.setCount(size);
            // 获取该组内关系为1的档案
            List<ProfileVO> profiles = profileList.stream()
                    .filter(profile -> profile.getRelationship().toValue() == 1)
                    .filter(profile -> relMap.get(profileGroup.getId()) != null &&
                            relMap.get(profileGroup.getId()).stream()
                                    .anyMatch(rel -> rel.getProfileId().equals(profile.getId())))
                    .collect(Collectors.toList());
            profiles.sort(Comparator.comparing((ProfileVO p) -> p.getBirthdayType().equals(BirthdayType.SOLAR) ? p.getSolarNextAgeDay() : p.getLunarNextAgeDay()));
            profileGroupVO.setProfiles(profiles);
            profileGroupVOList.add(profileGroupVO);
        }
        return ProfileGroupResponse.builder()
                .selfProfile(profileList.stream()
                        .filter(profile -> profile.getRelationship().toValue() == 0)
                        .findFirst() // 获取第一个匹配的结果
                        .orElse(null))
                .profileGroups(profileGroupVOList)
                .build();
    }

    /**
     * 计算距离下一次生日的年龄和天数.
     * 对于2月29日出生者，按下一个闰年的2月29日作为生日.
     * @param birthDate 出生日期时间
     * @return 一个字符串，包含年龄和距离下一次生日的天数
     */
    public static Map<String,String> calculateNextBirthday(LocalDate birthDate,LocalDate currentDate) {
        // 计算年龄calculateNextBirthday
        int age = Period.between(birthDate, currentDate).getYears();
        if (birthDate.isBefore(currentDate)) {
            age++; // 如果今年的生日已过，则年龄增加
        }

        LocalDate nextBirthday;
        if (birthDate.getDayOfMonth() == 29 && birthDate.getMonthValue() == 2) {
            // 查找下一个闰年的2月29日作为生日
            int year = currentDate.getYear();
            while (!Year.isLeap(year + 1)) { // 寻找下一个闰年
                year++;
            }
            nextBirthday = LocalDate.of(year + 1, 2, 29); // 跳过当前年，直接到下一个闰年
        } else {
            // 对于其他日期，直接计算下一年的相同月份和日期
            LocalDate thisYearsBirthday = currentDate.withMonth(birthDate.getMonthValue()).withDayOfMonth(birthDate.getDayOfMonth());
            if (currentDate.isBefore(thisYearsBirthday) || currentDate.isEqual(thisYearsBirthday)) {
                nextBirthday = thisYearsBirthday;
            } else {
                nextBirthday = thisYearsBirthday.plusYears(1);
            }
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 计算距离下次生日的天数
        long daysUntilNextBirthday = ChronoUnit.DAYS.between(currentDate, nextBirthday);
        Map<String,String> map = new LinkedHashMap<>();
        map.put("age", String.valueOf(age));
        map.put("day", String.valueOf(daysUntilNextBirthday));
        map.put("nextBirthday",nextBirthday.format(formatter));
//        System.out.println("距离下一次生日: " + age + "岁，" + daysUntilNextBirthday + "天");
        return map;
    }


    public static Map<String,String> calculateNextLunarBirthday(int lunarYear, int lunarMonth, int lunarDay,LocalDate today) {

        // 创建农历生日日期对象
        LunarDate lunarBirthday = LunarDate.of(lunarYear, lunarMonth, lunarDay);

        // 获取当前日期对应的农历日期
        LunarDate todayLunar = LunarDate.from(today);

        // 确定农历生日的年份
        int nextYear = today.getYear();
        LocalDate date = LunarDate.of(nextYear, lunarMonth, lunarDay).getLocalDate();
        int newYear = (nextYear-1);
        LocalDate lastDate = LunarDate.of(newYear, lunarMonth, lunarDay).getLocalDate();
        if(today.isBefore(lastDate) || today.equals(lastDate)){
            nextYear = newYear;
        }
        if (!todayLunar.getLocalDate().isBefore(date) && !todayLunar.getLocalDate().equals(date)) {
            // 如果当前的农历日期已经超过了生日日期，则计算下一年的生日
            nextYear += 1;
        }

        // 创建下一次农历生日日期对象
        LunarDate nextLunarBirthday = findValidLunarBirthday(nextYear, lunarMonth, lunarDay);

        // 将农历生日转换为公历日期
        LocalDate nextSolarDate = nextLunarBirthday.getLocalDate();

        // 计算年龄
        int age = Period.between(lunarBirthday.getLocalDate(), nextSolarDate).getYears();
        if (todayLunar.getLocalDate().isBefore(nextLunarBirthday.getLocalDate())) {
            age++; // 如果今年的生日已过，则年龄增加
        }

        // 计算距离下一个农历生日的天数
        long daysUntilNextBirthday = ChronoUnit.DAYS.between(today, nextSolarDate);
        Map<String,String> map = new LinkedHashMap<>();
        map.put("age", String.valueOf(age));
        map.put("day", String.valueOf( daysUntilNextBirthday));
        map.put("nextBirthday",nextYear + "/" + lunarMonth + "/" + lunarDay);
        return map;
    }

    private static LunarDate findValidLunarBirthday(int year, int month, int day) {
        LunarDate nextLunarBirthday = null;
        while (nextLunarBirthday == null) {
            try {
                nextLunarBirthday = LunarDate.of(year, month, day);
            } catch (Exception e) {
                year++;
            }
        }
        return nextLunarBirthday;
    }

    public static void main(String[] args) {
        LocalDate birthDate = LocalDate.of(1989, 1, 28);
//        calculateNextBirthday(birthDate,1996);
        // 示例输入
//        LocalDateTime birthDateTime = LocalDateTime.of(2004, 3, 20, 0, 0); // 假设1990年2月29日出生
//        String result = calculateNextBirthdayAgeAndDays(birthDateTime);
//        System.out.println(result);
        // 示例农历生日
        int lunarYear = 2000;
        int lunarMonth = 9; // 农历二月
        int lunarDay = 9;  // 农历三十

//        System.out.println(lunarDay);
        // 计算下一次农历生日的信息
        Map<String, String> stringStringMap = calculateNextBirthday(birthDate, LocalDate.now().minusDays(1));
        calculateNextLunarBirthday(1988,12,21, LocalDate.now().plusDays(17));
        for (Map.Entry<String, String> stringStringEntry : stringStringMap.entrySet()) {
//            System.out.println(stringStringEntry.getKey()+"  "+stringStringEntry.getValue());
        }
//        System.out.println(result);
//        LunarDay lunarDay = LunarDay.fromYmd(2024,10,11);
//        Zodiac zodiac = lunarDay.getMonthSixtyCycle().getEarthBranch().getZodiac();
//        Constellation constellation = lunarDay.getSolarDay().getConstellation();
//        System.out.println(zodiac.getName());
//        System.out.println(zodiac.getIndex());
//        System.out.println(ZodiacType.values()[zodiac.getIndex()]);
//
//        System.out.println(constellation.getName());
//        System.out.println(constellation.getIndex());
//        System.out.println(ZodiacSignType.values()[constellation.getIndex()]);
    }

    private Specification<Profile> build(ProfileBaseRequest profileBaseRequest) {
        return (root, cquery, cbuild) -> {
            List<Predicate> predicates = new ArrayList<>();
            // id
            if(profileBaseRequest.getId()!=null) {
                predicates.add(cbuild.equal(root.get("Id"), profileBaseRequest.getId()));
            }
            // 用户id
            if(profileBaseRequest.getUserId()!=null) {
                predicates.add(cbuild.equal(root.get("userId"), profileBaseRequest.getUserId()));
            }
            // 性别
            if(profileBaseRequest.getGender()!=null){
                predicates.add(cbuild.equal(root.get("gender"), profileBaseRequest.getGender()));
            }
            if(profileBaseRequest.getRelationship()!=null){
                predicates.add(cbuild.equal(root.get("relationship"), profileBaseRequest.getRelationship()));
            }
            Predicate[] p = predicates.toArray(new Predicate[predicates.size()]);
            return p.length == 0 ? null : p.length == 1 ? p[0] : cbuild.and(p);
        };
    }

    public static ZodiacSignType getZodiacSign(LocalDateTime birthday) {
        int month = birthday.getMonthValue();
        int day = birthday.getDayOfMonth();

        // 根据生日计算星座
        if ((month == 3 && day >= 21) || (month == 4 && day <= 19)) {
            return ZodiacSignType.ARIES;
        } else if ((month == 4 && day >= 20) || (month == 5 && day <= 20)) {
            return ZodiacSignType.TAURUS;
        } else if ((month == 5 && day >= 21) || (month == 6 && day <= 21)) {
            return ZodiacSignType.GEMINI;
        } else if ((month == 6 && day >= 22) || (month == 7 && day <= 22)) {
            return ZodiacSignType.CANCER;
        } else if ((month == 7 && day >= 23) || (month == 8 && day <= 22)) {
            return ZodiacSignType.LEO;
        } else if ((month == 8 && day >= 23) || (month == 9 && day <= 22)) {
            return ZodiacSignType.VIRGO;
        } else if ((month == 9 && day >= 23) || (month == 10 && day <= 23)) {
            return ZodiacSignType.LIBRA;
        } else if ((month == 10 && day >= 24) || (month == 11 && day <= 22)) {
            return ZodiacSignType.SCORPIO;
        } else if ((month == 11 && day >= 23) || (month == 12 && day <= 21)) {
            return ZodiacSignType.SAGITTARIUS;
        } else if ((month == 12 && day >= 22) || (month == 1 && day <= 19)) {
            return ZodiacSignType.CAPRICORN;
        } else if ((month == 1 && day >= 20) || (month == 2 && day <= 18)) {
            return ZodiacSignType.AQUARIUS;
        } else {
            return ZodiacSignType.PISCES; // 剩下的是双鱼座
        }
    }

    public static String formatDateString(String dateString) {
        if (dateString == null || dateString.isEmpty()) {
            return null;
        }

        // 分割原始字符串
        String[] parts = dateString.split("[\\- :]");

        // 检查分割后的数组长度是否正确
        if (parts.length != 5) {
            System.err.println("输入的日期格式错误!");
            return null;
        }

        // 补全月份和日期不足两位的部分
        String year = parts[0];
        String month = parts[1].length() == 1 ? "0" + parts[1] : parts[1];
        String day = parts[2].length() == 1 ? "0" + parts[2] : parts[2];
        String hour = parts[3];
        String minute = parts[4].length() == 1 ? "0" + parts[4] : parts[4];
        String second = "00"; // 默认补全为00秒

        // 构造新的格式化后的字符串
        return year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + second;
    }
}
