package com.lhx.birthday.service.impl;

import com.lhx.birthday.entity.Feedback;
import com.lhx.birthday.mapper.FeedbackMapper;
import com.lhx.birthday.request.userinfo.FeedbackAddRequest;
import com.lhx.birthday.service.IFeedbackService;
import com.lhx.birthday.vo.FeedbackVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/10/23 14:28
 */
@Service
public class FeedbackServiceImpl implements IFeedbackService {

    @Resource
    private FeedbackMapper feedbackMapper;

    @Override
    public void addFeedback(Long userId, FeedbackAddRequest feedbackAddRequest) {
        Feedback feedback = Feedback.builder()
                .userId(userId)
                .system(feedbackAddRequest.getSystem())
                .contact(feedbackAddRequest.getContact())
                .content(feedbackAddRequest.getContent())
                .machine(feedbackAddRequest.getMachine())
                .systemVersion(feedbackAddRequest.getSystemVersion())
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();
        feedbackMapper.saveAndFlush(feedback);
    }

    @Override
    public List<FeedbackVO> getList(Long userId) {
        List<FeedbackVO> feedbackVOS = new ArrayList<>();
        List<Feedback> feedbackList = feedbackMapper.getByUserId(userId);
        for (Feedback feedback : feedbackList) {
            FeedbackVO feedbackVO = new FeedbackVO();
            BeanUtils.copyProperties(feedback, feedbackVO);
            feedbackVOS.add(feedbackVO);
        }
        return feedbackVOS;
    }
}
