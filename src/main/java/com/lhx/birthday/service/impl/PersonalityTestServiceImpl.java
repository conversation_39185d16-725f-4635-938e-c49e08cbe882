package com.lhx.birthday.service.impl;

import com.lhx.birthday.entity.Personality;
import com.lhx.birthday.entity.PersonalityTestQuestion;
import com.lhx.birthday.entity.PersonalityTestResult;
import com.lhx.birthday.mapper.PersonalityMapper;
import com.lhx.birthday.mapper.PersonalityTestQuestionMapper;
import com.lhx.birthday.mapper.PersonalityTestResultMapper;
import com.lhx.birthday.service.IPersonalityTestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 性格测试服务实现类
 * <AUTHOR> lhx
 */
@Service
@Slf4j
public class PersonalityTestServiceImpl implements IPersonalityTestService {

    @Resource
    private PersonalityTestQuestionMapper personalityTestQuestionMapper;

    @Resource
    private PersonalityMapper personalityMapper;
    
    @Resource
    private PersonalityTestResultMapper personalityTestResultMapper;

    @Override
    public Map<Integer, List<PersonalityTestQuestion>> getAllQuestions() {
        List<PersonalityTestQuestion> allQuestions = personalityTestQuestionMapper.findAll();
        return allQuestions.stream()
                .collect(Collectors.groupingBy(PersonalityTestQuestion::getQuestionId));
    }

    @Override
    public List<PersonalityTestQuestion> getQuestionById(Integer questionId) {
        return personalityTestQuestionMapper.findByQuestionId(questionId);
    }

    @Override
    public String calculateResult(Map<Integer, Character> answerMap) {
        Map<String, Integer> scores = calculateScores(answerMap);
        
        // 确定每个维度的结果
        StringBuilder result = new StringBuilder();
        result.append(scores.get("E") > scores.get("I") ? "E" : "I");
        result.append(scores.get("S") > scores.get("N") ? "S" : "N");
        result.append(scores.get("T") > scores.get("F") ? "T" : "F");
        result.append(scores.get("J") > scores.get("P") ? "J" : "P");

        return result.toString();
    }
    
    /**
     * 计算各维度分数
     *
     * @param answerMap 答案Map
     * @return 各维度分数
     */
    private Map<String, Integer> calculateScores(Map<Integer, Character> answerMap) {
        int eScore = 0, iScore = 0;
        int sScore = 0, nScore = 0;
        int tScore = 0, fScore = 0;
        int jScore = 0, pScore = 0;

        for (Map.Entry<Integer, Character> entry : answerMap.entrySet()) {
            Integer questionId = entry.getKey();
            Character chosenOption = entry.getValue();
            
            // 获取对应问题的选项
            List<PersonalityTestQuestion> options = personalityTestQuestionMapper.findByQuestionId(questionId);
            
            // 寻找选择的选项
            for (PersonalityTestQuestion option : options) {
                if (option.getOptionLetter().equals(chosenOption)) {
                    // 累加各维度分数
                    eScore += option.getE();
                    iScore += option.getI();
                    sScore += option.getS();
                    nScore += option.getN();
                    tScore += option.getT();
                    fScore += option.getF();
                    jScore += option.getJ();
                    pScore += option.getP();
                    break;
                }
            }
        }
        
        Map<String, Integer> scores = new HashMap<>();
        scores.put("E", eScore);
        scores.put("I", iScore);
        scores.put("S", sScore);
        scores.put("N", nScore);
        scores.put("T", tScore);
        scores.put("F", fScore);
        scores.put("J", jScore);
        scores.put("P", pScore);
        
        return scores;
    }
    
    @Override
    public PersonalityTestResult saveTestResult(Long userId, Map<Integer, Character> answerMap) {
        Map<String, Integer> scores = calculateScores(answerMap);
        String typeCode = calculateResult(answerMap);
        
        // 先查询是否已存在该用户的测试结果
        PersonalityTestResult existingResult = personalityTestResultMapper.findTopByUserIdOrderByTestTimeDesc(userId);
        
        if (existingResult != null) {
            // 更新已存在的记录
            existingResult.setTypeCode(typeCode);
            existingResult.setEScore(scores.get("E"));
            existingResult.setIScore(scores.get("I"));
            existingResult.setSScore(scores.get("S"));
            existingResult.setNScore(scores.get("N"));
            existingResult.setTScore(scores.get("T"));
            existingResult.setFScore(scores.get("F"));
            existingResult.setJScore(scores.get("J"));
            existingResult.setPScore(scores.get("P"));
            existingResult.setTestTime(LocalDateTime.now());
            
            return personalityTestResultMapper.save(existingResult);
        } else {
            // 创建新记录
            PersonalityTestResult result = PersonalityTestResult.builder()
                    .userId(userId)
                    .typeCode(typeCode)
                    .eScore(scores.get("E"))
                    .iScore(scores.get("I"))
                    .sScore(scores.get("S"))
                    .nScore(scores.get("N"))
                    .tScore(scores.get("T"))
                    .fScore(scores.get("F"))
                    .jScore(scores.get("J"))
                    .pScore(scores.get("P"))
                    .testTime(LocalDateTime.now())
                    .build();
                    
            return personalityTestResultMapper.save(result);
        }
    }
    
    @Override
    public PersonalityTestResult getUserLatestResult(Long userId) {
        return personalityTestResultMapper.findTopByUserIdOrderByTestTimeDesc(userId);
    }

    @Override
    public Personality getPersonalityByTypeCode(String typeCode) {
        return personalityMapper.findByTypeCode(typeCode);
    }

    @Override
    public List<Personality> getAllPersonalities() {
        return personalityMapper.findAll();
    }
} 