package com.lhx.birthday.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.lhx.birthday.constant.LockManager;
import com.lhx.birthday.entity.Attachment;
import com.lhx.birthday.entity.CdKey;
import com.lhx.birthday.enums.DefaultFlag;
import com.lhx.birthday.enums.DeleteFlag;
import com.lhx.birthday.enums.OperateUnit;
import com.lhx.birthday.mapper.CdkeyMapper;
import com.lhx.birthday.request.operate.CdkeyExchangeRequest;
import com.lhx.birthday.request.operate.CdkeyGenerateRequest;
import com.lhx.birthday.request.operate.ExpireRequest;
import com.lhx.birthday.request.operate.RegisterRequest;
import com.lhx.birthday.response.operate.CdkeyGenerateResponse;
import com.lhx.birthday.service.ICdkeyService;
import com.lhx.birthday.service.IUserInfoService;
import com.lhx.birthday.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.Predicate;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 发现模块服务
 */
@Slf4j
@Service
public class CdkeyServiceImpl implements ICdkeyService {

    @Autowired
    private CdkeyMapper cdkeyMapper;

    @Autowired
    private IUserInfoService userInfoService;

    @Value("${operate.pwd}")
    private String pwd;

    @Override
    public Result<CdkeyGenerateResponse> generate(CdkeyGenerateRequest cdkeyGenerateRequest) {
        if(cdkeyGenerateRequest.getNum()>500){
            return Result.error("生成数量超过500");
        }
        if(!cdkeyGenerateRequest.getPwd().equals(pwd)){
            return Result.error("密码错误");
        }
        OperateUnit operateUnit = cdkeyGenerateRequest.getOperateUnit();
        String cdKey;
        CdKey code;
        List<String> codes = new ArrayList<>();
        for (Integer i = 0; i < cdkeyGenerateRequest.getNum(); i++) {
            do {
                cdKey = generateCDKey();
                code = cdkeyMapper.findByCode(cdKey);
            } while (Objects.nonNull(code));
            codes.add(cdKey);
            cdkeyMapper.saveAndFlush(CdKey.builder()
                    .code(cdKey)
                    .type(operateUnit.toValue())
                    .usePhone("")
                    .agentName(cdkeyGenerateRequest.getAgentName())
                    .deleteFlag(DeleteFlag.NO)
                    .status(DefaultFlag.NO)
                    .createTime(LocalDateTime.now())
                    .build());
        }

        return Result.ok(CdkeyGenerateResponse.builder()
                .cdkeys(codes)
                .build());
    }

    /**
     * 兑换激活码
     * @param cdkeyExchangeRequest
     * @return
     */
    @Override
    public Result exchange(CdkeyExchangeRequest cdkeyExchangeRequest) {
        String message;
        LockManager.lock.lock();
        try {
            String phone = cdkeyExchangeRequest.getPhone();
            CdKey cdKey = cdkeyMapper.findByCode(cdkeyExchangeRequest.getCdkey());
            if(Objects.isNull(cdKey)){
                return Result.error("激活码错误");
            } else if(cdKey.getStatus().equals(DefaultFlag.YES)){
                return Result.error("激活码已被使用");
            }else if(cdKey.getDeleteFlag().equals(DeleteFlag.YES)){
                return Result.error("激活码已失效");
            }
            // 手机号兑换
            CdKey exchange = userInfoService.exchange(phone, cdKey.getType(), null);
            cdKey.setUsePhone(phone);
            cdKey.setUseDate(exchange.getUseDate());
            cdKey.setExpiryDate(exchange.getExpiryDate());
            cdKey.setStartDate(exchange.getStartDate());
            cdKey.setStatus(DefaultFlag.YES);
            cdkeyMapper.saveAndFlush(cdKey);

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            message = exchange.getExpiryDate().format(formatter);
        } finally {
            LockManager.lock.unlock();
        }
        return Result.ok(message);
    }

    /**
     * 会员过期
     * @param expireRequest
     * @return
     */
    @Override
    public Result expire(ExpireRequest expireRequest) {
        // 验证密码
        if(!expireRequest.getPwd().equals(pwd)){
            return Result.error("密码错误");
        }

        // 通过兑换码查找相关信息
        CdKey cdKey = cdkeyMapper.findByCode(expireRequest.getCdkey());
        if(cdKey == null) {
            return Result.error("兑换码不存在");
        }

        // 检查兑换码是否已使用
        if(cdKey.getStatus() != DefaultFlag.YES) {
            return Result.error("兑换码未使用，无法过期");
        }

        // 获取关联的手机号
        String usePhone = cdKey.getUsePhone();
        if(StringUtils.isEmpty(usePhone)) {
            return Result.error("兑换码未关联手机号");
        }

        // 调用过期会员服务
        Result result = userInfoService.expire(usePhone);

        // 如果过期成功，将兑换码标记为作废
        if(result.isSuccess()) {
//            cdKey.setStatus(DefaultFlag.NO); // 将状态改为未使用（作废）
            cdKey.setDeleteFlag(DeleteFlag.YES); // 标记为已删除
            cdkeyMapper.saveAndFlush(cdKey);
        }

        return result;
    }

    /**
     * 注册账户
     * @param registerRequest
     * @return
     */
    @Override
    public Result register(RegisterRequest registerRequest) {
        if(!registerRequest.getPwd().equals(pwd)){
            return Result.error("密码错误");
        }
        userInfoService.exchange(registerRequest.getPhone(), null, registerRequest.getTime());
        return Result.ok();
    }

    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final SecureRandom random = new SecureRandom();

    public static String generateCDKey() {
        StringBuilder sb = new StringBuilder(8);
        for (int i = 0; i < 8; i++) {
            // 从CHARACTERS中随机选择一个字符添加到StringBuilder中
            int randomIndex = random.nextInt(CHARACTERS.length());
            sb.append(CHARACTERS.charAt(randomIndex));
        }
        return sb.toString();
    }

}
