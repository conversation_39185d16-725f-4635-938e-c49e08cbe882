package com.lhx.birthday.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lhx.birthday.constant.ApiConstant;
import com.lhx.birthday.entity.SolarTerms;
import com.lhx.birthday.entity.SystemConfig;
import com.lhx.birthday.entity.Tool;
import com.lhx.birthday.enums.LanguageType;
import com.lhx.birthday.mapper.SolarTermsMapper;
import com.lhx.birthday.mapper.SystemConfigMapper;
import com.lhx.birthday.mapper.ToolMapper;
import com.lhx.birthday.request.horoscope.JieqiRequest;
import com.lhx.birthday.request.horoscope.LaohuangliRequest;
import com.lhx.birthday.request.horoscope.ZeshiRequest;
import com.lhx.birthday.service.IToolService;
import com.lhx.birthday.util.DateRangeUtil;
import com.lhx.birthday.util.converUtil.ConverChinese;
import com.lhx.birthday.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.lhx.birthday.constant.SystemConfigConstant.MOBILE_CONFIG_KEY;
import static com.lhx.birthday.constant.SystemConfigConstant.MOBILE_ZESHI_CONFIG_TYPE;


/**
 * <AUTHOR> lhx
 * @date 2023/10/23 14:28
 */
@Service
public class ToolServiceImpl implements IToolService {

    @Resource
    private ToolMapper toolMapper;

    @Autowired
    private SystemConfigMapper systemConfigMapper;

    @Autowired
    private SolarTermsMapper solarTermsMapper;

    @Value("${yuanfenju.appKey}")
    private String appKey;

    /**
     * 老黄历
     */
    @Override
    public Result LaohuangliHoroscope(LaohuangliRequest laohuangliRequest) {
        LocalDate time = LocalDate.now();
        if(Objects.nonNull(laohuangliRequest.getTime())){
            time = laohuangliRequest.getTime();
        }
        Tool tool = toolMapper.findByTime(time);
        if(Objects.nonNull(tool)){
            JSONObject resultObj = JSONObject.parseObject(tool.getLaohuangliDetail());
            if(Objects.nonNull(laohuangliRequest.getLanguageType()) && laohuangliRequest.getLanguageType().equals(LanguageType.ZH_HANT)){
                resultObj = ConverChinese.convertValuesToTraditionalChinese(tool.getLaohuangliDetail());
            }
            return Result.ok(resultObj);
        }else{
            String huangLiRequestData = "api_key=" + appKey + "&title_laohuangli=" + getCurrentFormattedDate(time.getYear(),time.getMonthValue(),time.getDayOfMonth());
            HttpResponse response = sendPostRequest(ApiConstant.API_LAO_HANG_LI, huangLiRequestData);
            // 读取响应结果
            String result = response.body();
            // 关闭连接
            response.close();
            JSONObject jsonObject = JSONObject.parseObject(result);
            if (jsonObject.getInteger("errcode").equals(0)) {
                JSONObject dataObj = jsonObject.getJSONObject("data");
                tool = Tool.builder()
                        .laohuangliDetail(dataObj.toJSONString())
                        .ji(dataObj.getString("ji"))
                        .yi(dataObj.getString("yi"))
                        .time(time)
                        .build();
                toolMapper.saveAndFlush(tool);
            }else{
                return Result.ok(new JSONObject());
            }
            JSONObject resultObj = JSONObject.parseObject(tool.getLaohuangliDetail());
            if(Objects.nonNull(laohuangliRequest.getLanguageType()) && laohuangliRequest.getLanguageType().equals(LanguageType.ZH_HANT)){
                resultObj = ConverChinese.convertValuesToTraditionalChinese(resultObj.toJSONString());
            }
            return Result.ok(resultObj);
        }
    }

    @Override
    public Result LaohuangliList() {
        List<String> dateList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate today = LocalDate.now();

        for (int i = -90; i <= 90; i++) {
            LocalDate date = today.plusDays(i);
            dateList.add(date.format(formatter));
        }

        return Result.ok(dateList);
    }

    @Override
    public Result ZeshiHoroscope(ZeshiRequest zeshiRequest, LanguageType languageType) {
        if(Objects.isNull(zeshiRequest.getIncident())) {
            zeshiRequest.setIncident(0);
        }
        if(Objects.isNull(zeshiRequest.getFuture())){
            zeshiRequest.setFuture(0);
        }
        SystemConfig systemConfig = systemConfigMapper.findByConfigKeyAndConfigType(MOBILE_CONFIG_KEY, MOBILE_ZESHI_CONFIG_TYPE);
        JSONObject jsonObject = JSONObject.parseObject(systemConfig.getContext());
        JSONArray jsonArray = jsonObject.getJSONArray("source");
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject sourceObj = jsonArray.getJSONObject(i);
            if(sourceObj.getInteger("value").equals(zeshiRequest.getIncident())){
                String incident = sourceObj.getString("incident");
                // 获取时间范围
                DateRangeUtil.FutureDateRange futureDateRange = DateRangeUtil.getFutureDateRange(zeshiRequest.getFuture());
                List<Tool> toolList = toolMapper.findAll(this.build(incident,futureDateRange.getStartDate(),futureDateRange.getEndDate()));
                JSONArray dataArr = new JSONArray();
                for (Tool tool : toolList) {
                    JSONObject laohuangliObject = JSONObject.parseObject(tool.getLaohuangliDetail());
                    laohuangliObject.remove("detail_info");
                    dataArr.add(laohuangliObject);
                }
                JSONObject resultObj = new JSONObject();
                resultObj.put("detail_info",dataArr);
                JSONObject baseInfoObj = new JSONObject();
                baseInfoObj.put("summarize","吉日区间："+formatDate(futureDateRange.getStartDate(),"yyyy-MM-dd")+"——"+formatDate(futureDateRange.getEndDate(),"yyyy-MM-dd")+"，共计"+futureDateRange.getTotalDays()+"天，其中适合"+incident+"的吉日有 " + dataArr.size() + " 天");
                resultObj.put("base_info",baseInfoObj);
                if(languageType.equals(LanguageType.ZH_HANT)){
                    resultObj = ConverChinese.convertValuesToTraditionalChinese(resultObj.toJSONString());
                }
                return Result.ok(resultObj);
            }
        }
        return null;
    }

    @Override
    public Result JieqiHoroscope(JieqiRequest jieqiRequest) {
        Integer year = jieqiRequest.getYear();

        SolarTerms solarTerms = solarTermsMapper.findByYear(year);
        if(Objects.isNull(solarTerms)){
            String requestData = "api_key=" + appKey + "&type=1&month=1&day=1&year=" + year;
            HttpResponse response = sendPostRequest(ApiConstant.API_JIEQI, requestData);
            // 读取响应结果
            String result = response.body();
            // 关闭连接
            response.close();
            JSONObject jsonObject = JSONObject.parseObject(result);
            if (jsonObject.getInteger("errcode").equals(0)) {
                JSONArray dataObj = jsonObject.getJSONArray("data");
                solarTerms = SolarTerms.builder()
                        .dataInfo(dataObj.toJSONString())
                        .year(year)
                        .build();
                solarTermsMapper.saveAndFlush(solarTerms);
            }else{
                return Result.error("error");
            }
        }
        JSONArray jsonArray = (JSONArray) JSONArray.parse(solarTerms.getDataInfo());
        return Result.ok(jsonArray);
    }

    private Specification<Tool> build(String incident, LocalDate start, LocalDate end) {
        return (root, cquery, cbuild) -> {
            List<Predicate> predicates = new ArrayList<>();
            // 宜
            String[] incidentArr = incident.split("\\|");
            Predicate[] pInc = new Predicate[incidentArr.length];
            for (int i = 0; i < incidentArr.length; i++) {
                String inc = incidentArr[i];
                pInc[i] = cbuild.like(root.get("yi"), "%" + inc + "%");
            }
            Predicate result = cbuild.or(pInc);
            predicates.add(result);
            // 时间范围
            predicates.add(cbuild.between(root.get("time"), start, end));
            Predicate[] p = predicates.toArray(new Predicate[predicates.size()]);
            return p.length == 0 ? null : p.length == 1 ? p[0] : cbuild.and(p);
        };
    }

    private HttpResponse sendPostRequest(String url, String requestData) {
        // 发送POST请求并获取响应
        HttpResponse response = HttpRequest.post(url)
                .header("Content-Type", "application/x-www-form-urlencoded")
                .body(requestData)
                .timeout(5000)
                .execute();

        if (response.getStatus() != 200) {
            // 如果不是200 OK状态，可以在这里处理异常情况
            throw new RuntimeException("请求失败，HTTP状态码：" + response.getStatus());
        }

        return response;
    }

    public static String formatDate(LocalDate date, String pattern) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return date.format(formatter);
    }

    public static String getCurrentFormattedDate(int year,int month,int dayOfMonth) {
        LocalDate currentDate = LocalDate.of(year,month,dayOfMonth);
        return formatDate(currentDate, "yyyy-MM-dd");
    }

}
