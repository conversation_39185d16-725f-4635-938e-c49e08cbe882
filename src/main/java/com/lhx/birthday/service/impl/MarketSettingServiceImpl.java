package com.lhx.birthday.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.jpush.api.push.PushResult;
import cn.jpush.api.push.model.Platform;
import cn.jpush.api.push.model.PushPayload;
import cn.jpush.api.push.model.audience.Audience;
import cn.jpush.api.push.model.notification.Notification;
import com.alibaba.fastjson.JSONObject;
import com.lhx.birthday.config.JPushConfig;
import com.lhx.birthday.entity.MarketPointVip;
import com.lhx.birthday.entity.Push;
import com.lhx.birthday.entity.SystemConfig;
import com.lhx.birthday.entity.TvMemberRechargeType;
import com.lhx.birthday.enums.RewardType;
import com.lhx.birthday.enums.RewardVipUnit;
import com.lhx.birthday.mapper.MarketPointVipMapper;
import com.lhx.birthday.mapper.SystemConfigMapper;
import com.lhx.birthday.mapper.TvMemberRechargeTypeMapper;
import com.lhx.birthday.service.IJPushService;
import com.lhx.birthday.service.IMarketSettingService;
import com.lhx.birthday.util.IfconfigUtil;
import com.lhx.birthday.util.NumUtil;
import com.lhx.birthday.util.SafeUtil;
import com.lhx.birthday.vo.market.MarketSettingVO;
import com.lhx.birthday.vo.market.PointVipVO;
import com.lhx.birthday.vo.market.ProductMarketingVO;
import com.lhx.birthday.vo.systemConfig.RewardSettingVO;
import com.lhx.birthday.vo.systemConfig.SystemConfigVO;
import com.sun.org.apache.xpath.internal.operations.Bool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.zip.CRC32;

import static cn.hutool.json.XMLTokener.entity;
import static com.lhx.birthday.constant.SystemConfigConstant.MARKET_CONFIG_KEY;

@Service
@Slf4j
public class MarketSettingServiceImpl implements IMarketSettingService {

    @Autowired
    private MarketPointVipMapper marketPointVipMapper;

    @Autowired
    private SystemConfigMapper systemConfigMapper;

    @Autowired
    private TvMemberRechargeTypeMapper tvMemberRechargeTypeMapper;

    @Override
    public List<PointVipVO> getPointVipList(Integer device) {
        List<MarketPointVip> marketPointVipList = marketPointVipMapper.findByDevice(device);
        return marketPointVipList.stream().map(marketPointVip ->
                PointVipVO.builder()
                        .productId(marketPointVip.getId().toString())
                        .vipUnit(marketPointVip.getVipUnit())
                        .vipValue(marketPointVip.getVipValue())
                        .pointValue(marketPointVip.getPointValue())
                        .build()
        ).collect(Collectors.toList());
    }

    @Override
    public MarketSettingVO getMarketing(String configKey, HttpServletRequest request) {
        SystemConfigVO systemConfigVO = new SystemConfigVO();
        SystemConfig systemConfig = systemConfigMapper.findByConfigKeyAndConfigType(MARKET_CONFIG_KEY, configKey);
        BeanUtils.copyProperties(systemConfig,systemConfigVO);
        JSONObject jsonObject = JSONObject.parseObject(systemConfig.getContext());

        int starReviewHidden = 0;
        String beginClock = jsonObject.getString("begin_clock");
        String endClock = jsonObject.getString("end_clock");
        if (!CharSequenceUtil.hasBlank(beginClock, endClock)) {
            String clockNow = DateUtil.format(new Date(), "HH:mm");
            boolean isBetween = NumUtil.between(clockNow, beginClock, endClock);
            starReviewHidden = isBetween ? 0 : 1;
        }
        Boolean showStar5Page = jsonObject.getBoolean("show_star5_page");
        if(!showStar5Page){
            starReviewHidden = 1;
        }
        if(starReviewHidden==0){
            IfconfigUtil ifconfigUtil = new IfconfigUtil();
            String ipAddr = ifconfigUtil.getIpAddr(request);
            Double pct = jsonObject.getDouble("pct");
            boolean checkPctByIp = checkPctByIp(pct, ipAddr);
            if(!checkPctByIp){
                starReviewHidden = 1;
            }
        }

        MarketSettingVO settingVO = MarketSettingVO.builder()
                .showMarketing(jsonObject.getBoolean("show_marketing") ? 0 : 1)
                .invitationHidden(jsonObject.getBoolean("show_invite_page") ? 0 : 1)
                .signHidden(jsonObject.getBoolean("show_sign_page") ? 0 : 1)
                .starReviewHidden(starReviewHidden)
                .beginClock(jsonObject.getString("begin_clock"))
                .endClock(jsonObject.getString("end_clock"))
                .star5(RewardSettingVO.builder()
                        .type(RewardType.valueOf(jsonObject.getString("star5_reward_type")))
                        .unit(RewardVipUnit.valueOf(jsonObject.getString("star5_reward_unit")))
                        .value(jsonObject.getIntValue("star5_reward_value"))
                        .build())
                .invite(RewardSettingVO.builder()
                        .type(RewardType.valueOf(jsonObject.getString("invite_reward_type")))
                        .unit(RewardVipUnit.valueOf(jsonObject.getString("invite_reward_unit")))
                        .value(jsonObject.getIntValue("invite_reward_value"))
                        .build())
                .invited(RewardSettingVO.builder()
                        .type(RewardType.valueOf(jsonObject.getString("invited_reward_type")))
                        .unit(Objects.isNull(jsonObject.getString("invited_reward_unit"))? null : RewardVipUnit.valueOf(jsonObject.getString("invited_reward_unit")))
                        .value(jsonObject.getIntValue("invited_reward_value"))
                        .build())
                .build();
        return settingVO;
    }

    public static boolean checkPctByIp(Double pct, String ip) {
        Double d = pct * 1000;

        CRC32 crc32 = new CRC32();
        crc32.update(ip.getBytes());

        long h = crc32.getValue() % 1000;
        return h <= d.longValue();
    }

    @Override
    public ProductMarketingVO getProductMarketing(String configKey) {
        SystemConfigVO systemConfigVO = new SystemConfigVO();
        SystemConfig systemConfig = systemConfigMapper.findByConfigKeyAndConfigType(MARKET_CONFIG_KEY, configKey);
        BeanUtils.copyProperties(systemConfig,systemConfigVO);
        JSONObject jsonObject = JSONObject.parseObject(systemConfig.getContext());

        Long marketingPageNormalProductId = jsonObject.getLong("marketing_page_normal_shangpin_id");
        Long marketingPageFreeProductId = jsonObject.getLong("marketing_page_free_shangpin_id");
        Boolean showMarketingPage = jsonObject.getBoolean("show_marketing_page");

        ProductMarketingVO.builder()
                .isEnable(false)
                .build();

        ProductMarketingVO.Normal normal = null;
        if (NumUtil.isNotBlank(marketingPageNormalProductId)) {
            Optional<TvMemberRechargeType> tvMemberRechargeType = tvMemberRechargeTypeMapper.findById(marketingPageNormalProductId);
            if (tvMemberRechargeType.isPresent() && tvMemberRechargeType.get().getIosShow()) {
                normal = ProductMarketingVO.Normal.builder()
                        .productId(tvMemberRechargeType.get().getCode())
                        .cycleUnit(tvMemberRechargeType.get().getCycleUnit())
                        .build();
            }
        }

        ProductMarketingVO.Free free = null;
        if (NumUtil.isNotBlank(marketingPageFreeProductId)) {
            Optional<TvMemberRechargeType> tvMemberRechargeType = tvMemberRechargeTypeMapper.findById(marketingPageFreeProductId);
            if (tvMemberRechargeType.isPresent() && tvMemberRechargeType.get().getIosShow()) {
                free = ProductMarketingVO.Free.builder()
                        .productId(tvMemberRechargeType.get().getCode())
                        .cycleUnit(tvMemberRechargeType.get().getCycleUnit())
                        .freeDay(SafeUtil.of(tvMemberRechargeType.get().getAppleFreeDay()).toString())
                        .build();
            }
        }

        return ProductMarketingVO.builder()
                .isEnable(showMarketingPage)
                .free(free)
                .normal(normal)
                .build();
    }
}
