// This file is auto-generated, don't edit it. Thanks.
package com.lhx.birthday.service.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.lhx.birthday.request.userinfo.SmsRequest;
import com.lhx.birthday.service.IAliyunSmsService;
import com.lhx.birthday.util.CommonErrorCode;
import com.lhx.birthday.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @desc 阿里云发送短信验证码的接口封装
 * @date 2023-11-25
 */
@Slf4j
@Service
public class AliyunSmsServiceImpl implements IAliyunSmsService {

    public static final String ALIYUN_ENDPOINT_SMS = "dysmsapi.aliyuncs.com";

    @Value("${ali.sms.access-key-id}")
    String smsAliyunAccessKeyId;

    @Value("${ali.sms.access-key-secret}")
    String smsAliyunAccessKeySecret;

    @Value("#{T(java.lang.Integer).parseInt('${app.sms-aliyun.code-len:6}')}")
    Integer smsCodeLen;

    @Value("${ali.sms.code-chars}")
    String smsCodeChars;

    @Value("#{T(java.lang.Integer).parseInt('${app.sms-aliyun.code-minutes:5}')}")
    Integer smsCodeMinutes;

    @Value("${ali.sms.sign}")
    String smsSign;

    @Value("${ali.sms.template-code}")
    String smsTemplateCode;

    @Value("${ali.sms.en-sign}")
    String enSmsSign;

    @Value("${ali.sms.en-template-code}")
    String enSmsTemplateCode;

    @Override
    public Result sendSms(SmsRequest sendSmsRequest,String verificationCode) {
        try {
            Client client = this.createClient(smsAliyunAccessKeyId, smsAliyunAccessKeySecret);
            SendSmsRequest request = new SendSmsRequest();
            String sign;
            String templateCode;
            if(sendSmsRequest.getAreaCode().equals("86")){
                sign = smsSign;
                templateCode = smsTemplateCode;
            }else{
                sign = enSmsSign;
                templateCode = enSmsTemplateCode;
            }
            request.setPhoneNumbers(sendSmsRequest.getAreaCode() + sendSmsRequest.getPhone())
                    .setSignName(sign)
                    .setTemplateCode(templateCode)
                    .setTemplateParam(String.format("{\"code\":\"%s\"}", verificationCode));
            log.info("[AliyunSmsService.sendSms] request={}", JSON.toJSONString(request));

            SendSmsResponse response = client.sendSms(request);
            String code = response.getBody().getCode();
            if (!"OK".equals(code)) {
                if ("isv.BUSINESS_LIMIT_CONTROL".equals(code)) {
                    return Result.error("操作频繁，请稍后重试！");
                }
                return Result.error(code,response.getBody().getMessage());
            }
        } catch (Exception e) {
            log.error("[AliyunSmsService.sendSms][ERROR] client.sendSms e={}", e.getMessage());
            e.printStackTrace();
        }
        return Result.OK();
    }


    /**
     * 使用AK&SK初始化账号Client
     *
     * @param accessKeyId
     * @param accessKeySecret
     * @return Client
     * @throws Exception
     */
    private Client createClient(String accessKeyId, String accessKeySecret) throws Exception {
        try {
            com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                    .setAccessKeyId(accessKeyId)
                    .setAccessKeySecret(accessKeySecret);
            config.endpoint = ALIYUN_ENDPOINT_SMS;
            return new Client(config);
        } catch (Exception e) {
            log.error("[AliyunSmsService.createClient][ERROR] e={}", e.getMessage());
        }
        throw new Exception();
    }

}
