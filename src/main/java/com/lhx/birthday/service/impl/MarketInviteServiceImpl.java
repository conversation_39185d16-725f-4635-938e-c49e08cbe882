package com.lhx.birthday.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSONObject;
import com.lhx.birthday.constant.SystemConfigConstant;
import com.lhx.birthday.entity.MarketInvite;
import com.lhx.birthday.entity.MarketRewardVip;
import com.lhx.birthday.entity.SystemConfig;
import com.lhx.birthday.entity.UserInfo;
import com.lhx.birthday.enums.RewardReason;
import com.lhx.birthday.enums.RewardType;
import com.lhx.birthday.enums.RewardVipUnit;
import com.lhx.birthday.mapper.MarketInviteMapper;
import com.lhx.birthday.mapper.MarketRewardVipMapper;
import com.lhx.birthday.mapper.SystemConfigMapper;
import com.lhx.birthday.mapper.UserInfoMapper;
import com.lhx.birthday.request.market.InvitationHisRequest;
import com.lhx.birthday.request.market.InvitationRequest;
import com.lhx.birthday.service.IMarketInviteService;
import com.lhx.birthday.service.IMarketRewardVipService;
import com.lhx.birthday.util.NumUtil;
import com.lhx.birthday.util.SafeUtil;
import com.lhx.birthday.util.SensitiveUtil;
import com.lhx.birthday.vo.Result;
import com.lhx.birthday.vo.UserInfoVO;
import com.lhx.birthday.vo.UserPushStrategyVO;
import com.lhx.birthday.vo.market.InvitationHistoryVO;
import com.lhx.birthday.vo.market.InvitationSettingVO;
import com.lhx.birthday.vo.market.MarketSettingVO;
import com.lhx.birthday.vo.systemConfig.RewardSettingVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.lhx.birthday.constant.SystemConfigConstant.MARKET_CONFIG_KEY;
import static com.lhx.birthday.util.CommonErrorCode.*;

@Service
@Slf4j
public class MarketInviteServiceImpl implements IMarketInviteService {

    @Autowired
    private UserInfoMapper userInfoMapper;

    @Autowired
    private MarketServiceImpl marketService;

    @Autowired
    private MarketInviteMapper marketInviteMapper;

    @Autowired
    private SystemConfigMapper systemConfigMapper;

    @Override
    public Result invitation(InvitationRequest invitationRequest,UserInfoVO user ) {
        String code = invitationRequest.getCode().toLowerCase();
        if(code.equals(user.getInvitationCode())){
//            return Result.error(INVITE_SELF,"不能邀请自己");
            return Result.error("不能邀请自己");
        }
        MarketInvite marketInvite = marketInviteMapper.findByInvitedUserId(user.getUserId());
        if(Objects.nonNull(marketInvite)){
//            return Result.error(INVITE_DUP,"已兑换过");
            return Result.error("已兑换过");
        }
        UserInfo otherUserInfo = userInfoMapper.findByInvitationCode(code);
        if(Objects.isNull(otherUserInfo)){
//            return Result.error(INVITE_CODE,"邀请码错误");
            return Result.error("邀请码错误");
        }
        UserInfoVO otherUserInfoVO = new UserInfoVO();
        BeanUtils.copyProperties(otherUserInfo ,otherUserInfoVO);

        String configKey = SystemConfigConstant.MARKET_CONFIG_TYPE_IOS;
        if(Objects.nonNull(invitationRequest.getDevice()) && invitationRequest.getDevice().equals(1)){
            configKey = SystemConfigConstant.MARKET_CONFIG_TYPE_ANDROID;
        }
        SystemConfig systemConfig = systemConfigMapper.findByConfigKeyAndConfigType(MARKET_CONFIG_KEY, configKey);
        JSONObject jsonObject = JSONObject.parseObject(systemConfig.getContext());

        MarketInvite invite = MarketInvite.builder()
                .inviteUserId(otherUserInfo.getUserId())
                .invitedUserId(user.getUserId())
                .code(code)
                .invitedUserPhone(user.getPhone())
                .invitedUserUuid(user.getUuid())
                .invitedUserName(user.getNickname())
                .inviteRewardType(RewardType.valueOf(jsonObject.getString("invite_reward_type")))
                .inviteRewardValue(jsonObject.getIntValue("invite_reward_value"))
                .invitedRewardType(RewardType.valueOf(jsonObject.getString("invited_reward_type")))
                .invitedRewardValue(jsonObject.getIntValue("invited_reward_value"))
                .createTime(LocalDateTime.now())
                .build();
        if(Objects.nonNull(jsonObject.getString("invite_reward_unit"))){
            invite.setInviteRewardUnit(RewardVipUnit.valueOf(jsonObject.getString("invite_reward_unit")));
        }
        if(Objects.nonNull(jsonObject.getString("invited_reward_unit"))){
            invite.setInvitedRewardUnit(RewardVipUnit.valueOf(jsonObject.getString("invited_reward_unit")));
        }
        marketInviteMapper.save(invite);
        if (RewardSettingVO.checkStaticValid(invite.getInviteRewardType(), invite.getInviteRewardUnit(), invite.getInviteRewardValue())) {
            rewardUser(RewardReason.INVITE, otherUserInfoVO, invite.getInviteRewardType(), invite.getInviteRewardUnit(), invite.getInviteRewardValue());
        }
        if (RewardSettingVO.checkStaticValid(invite.getInvitedRewardType(), invite.getInvitedRewardUnit(), invite.getInvitedRewardValue())) {
            rewardUser(RewardReason.INVITED, user, invite.getInvitedRewardType(), invite.getInvitedRewardUnit(), invite.getInvitedRewardValue());
        }
        return Result.ok(MarketSettingVO.builder()
                .invite(RewardSettingVO.builder()
                        .type(RewardType.valueOf(jsonObject.getString("invite_reward_type")))
                        .unit(RewardVipUnit.valueOf(jsonObject.getString("invite_reward_unit")))
                        .value(jsonObject.getIntValue("invite_reward_value"))
                        .build())
                .invited(RewardSettingVO.builder()
                        .type(RewardType.valueOf(jsonObject.getString("invited_reward_type")))
                        .unit(Objects.isNull(jsonObject.getString("invited_reward_unit"))? null : RewardVipUnit.valueOf(jsonObject.getString("invited_reward_unit")))
                        .value(jsonObject.getIntValue("invited_reward_value"))
                        .build())
                .build());
    }

    @Override
    public InvitationHistoryVO getInvitationHistory(UserInfoVO user, InvitationHisRequest invitationHisRequest) {
        List<InvitationHistoryVO.Item> itemList = new ArrayList<>();
        List<MarketInvite> inviteList = marketInviteMapper.findByInviteUserId(user.getUserId());
        Integer inviteHours = 0;
        for (MarketInvite invite : inviteList) {
            String phone = "";
            if (CharSequenceUtil.isNotBlank(invite.getInvitedUserPhone())) {
                phone = SensitiveUtil.phone(invite.getInvitedUserPhone());
            } else {
                phone = invite.getInvitedUserName();
            }
            itemList.add(
                    InvitationHistoryVO.Item.builder()
                            .date(invite.getCreateTime())
                            .phone(phone)
                            .build()
            );
            RewardSettingVO reward = invite.buildInviteRewardSetting();
            if (reward.checkValidVip()) {
                inviteHours += SafeUtil.of(reward.toHours());
            }
        }
        String configKey = SystemConfigConstant.MARKET_CONFIG_TYPE_IOS;
        if(Objects.nonNull(invitationHisRequest.getDevice()) && invitationHisRequest.getDevice().equals(1)){
            configKey = SystemConfigConstant.MARKET_CONFIG_TYPE_ANDROID;
        }
        SystemConfig systemConfig = systemConfigMapper.findByConfigKeyAndConfigType(MARKET_CONFIG_KEY, configKey);
        JSONObject jsonObject = JSONObject.parseObject(systemConfig.getContext());
        return InvitationHistoryVO.builder()
                .peopleCount(itemList.size())
                .vipDays(inviteHours / 24)
                .list(itemList)
                .welfare(InvitationSettingVO.from(jsonObject))
                .build();
    }

    public void rewardUser(RewardReason reason, UserInfoVO user, RewardType type, RewardVipUnit unit, Integer value) {
        marketService.rewardUser(reason, user, type, unit, value);
    }
}
