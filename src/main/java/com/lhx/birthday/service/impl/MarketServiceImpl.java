package com.lhx.birthday.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.lhx.birthday.constant.SystemConfigConstant;
import com.lhx.birthday.constant.TriggerTypeConstant;
import com.lhx.birthday.entity.*;
import com.lhx.birthday.enums.DefaultFlag;
import com.lhx.birthday.enums.RewardReason;
import com.lhx.birthday.enums.RewardType;
import com.lhx.birthday.enums.RewardVipUnit;
import com.lhx.birthday.mapper.*;
import com.lhx.birthday.request.market.IntegralPayRequest;
import com.lhx.birthday.request.market.Star5Request;
import com.lhx.birthday.service.IMarketService;
import com.lhx.birthday.service.IMarketSettingService;
import com.lhx.birthday.util.CommonErrorCode;
import com.lhx.birthday.util.CommonUtil;
import com.lhx.birthday.util.NumUtil;
import com.lhx.birthday.util.SafeUtil;
import com.lhx.birthday.vo.Result;
import com.lhx.birthday.vo.TvMemberRechargeTypeVO;
import com.lhx.birthday.vo.UserInfoVO;
import com.lhx.birthday.vo.market.*;
import com.lhx.birthday.vo.systemConfig.RewardSettingVO;
import com.lhx.birthday.vo.systemConfig.SystemConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.lhx.birthday.constant.SystemConfigConstant.MARKET_CONFIG_KEY;

@Service
@Slf4j
public class MarketServiceImpl implements IMarketService {

    @Autowired
    private MarketStarRateMapper marketStarRateMapper;

    @Autowired
    private SystemConfigMapper systemConfigMapper;

    @Autowired
    private MarketRewardVipServiceImpl marketRewardVipService;

    @Autowired
    private MarketRewardPointServiceImpl marketRewardPointService;

    @Autowired
    private MarketPointVipMapper marketPointVipMapper;

    @Autowired
    private TvMemberRechargeTypeMapper tvMemberRechargeTypeMapper;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private UserRewardRecordMapper userRewardRecordMapper;

    @Value("#{T(java.lang.Integer).parseInt('${app.market.point-expiry-month:12}')}")
    private Integer pointExpiryMonth;

    @Override
    public boolean checkStar5Review(Long userId){
        MarketStarRate marketStarRate = marketStarRateMapper.findByUserId(userId);
        if(Objects.nonNull(marketStarRate)){
            return true;
        }
        return false;
    }

    @Override
    public Market5StarReviewVO star5Review(UserInfoVO userInfoVO, Star5Request star5Request) {
        String configKey = SystemConfigConstant.MARKET_CONFIG_TYPE_IOS;
        if(Objects.nonNull(star5Request.getDevice()) && star5Request.getDevice().equals(1)){
            configKey = SystemConfigConstant.MARKET_CONFIG_TYPE_ANDROID;
        }
        SystemConfig systemConfig = systemConfigMapper.findByConfigKeyAndConfigType(MARKET_CONFIG_KEY, configKey);
        JSONObject jsonObject = JSONObject.parseObject(systemConfig.getContext());

        taskExecutor.execute(() -> {
            try {
                log.info("1分钟后开始进行5星好评");
                TimeUnit.MINUTES.sleep(1);
                MarketStarRate marketStarRate = MarketStarRate.builder()
                        .userId(userInfoVO.getUserId())
                        .createTime(LocalDateTime.now())
                        .rewardType(RewardType.valueOf(jsonObject.getString("star5_reward_type")))
                        .rewardUnit(RewardVipUnit.valueOf(jsonObject.getString("star5_reward_unit")))
                        .rewardValue(Integer.valueOf(jsonObject.getString("star5_reward_value")))
                        .build();
                marketStarRateMapper.save(marketStarRate);
                if(checkStaticValid(marketStarRate.getRewardType(),marketStarRate.getRewardUnit(),marketStarRate.getRewardValue())){
                    rewardUser(RewardReason.START5,
                            userInfoVO,
                            marketStarRate.getRewardType(),
                            marketStarRate.getRewardUnit(),
                            marketStarRate.getRewardValue());
                }
                log.info("5星好评处理完成");
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return Market5StarReviewVO.builder()
                .type(RewardType.valueOf(jsonObject.getString("star5_reward_type")))
                .value(Integer.valueOf(jsonObject.getString("star5_reward_value")))
                .build();
    }

    @Override
    public PointHistoryVO getIntegralHistory(UserInfoVO user) {

        List<PointHistoryVO.Item> itemList = new ArrayList<>();
        List<MarketRewardPoint> entityList = marketRewardPointService.listByUserId(user.getUserId());
        if (CollUtil.isNotEmpty(entityList)) {
            for (MarketRewardPoint entity : entityList) {
                PointHistoryVO.Item item = PointHistoryVO.itemFrom(entity);
                itemList.add(item);
            }
        }
        return PointHistoryVO.builder()
                .list(itemList)
                .build();
    }

    @Override
    public Result pointPay(IntegralPayRequest integralPayRequest, UserInfoVO user) {

        Optional<MarketPointVip> marketPointVipOptional = marketPointVipMapper.findById(integralPayRequest.getProductId());
        if(marketPointVipOptional.isPresent()){
            MarketPointVip pointVip = marketPointVipOptional.get();
            if (NumUtil.lt(user.getPoint(), pointVip.getPointValue())) {
                return Result.error("用户积分不足");
//                return Result.error(CommonErrorCode.FAILED,"用户积分不足");
            }

            marketRewardPointService.usePoint(user, pointVip.getPointValue(), pointExpiryMonth);

            MarketRewardPoint usePointEntity = marketRewardPointService.createUseEntity(user, -pointVip.getPointValue());
            marketRewardPointService.updateUserPoint(user, usePointEntity);

            MarketRewardVip entity = marketRewardVipService.createEntity(user, RewardReason.EXCHANGE, pointVip.getVipUnit(), pointVip.getVipValue());
            marketRewardVipService.updateUserVip(user, entity);
        }else{
//            return Result.error(CommonErrorCode.FAILED,"兑换商品不存在");
            return Result.error("兑换商品不存在");
        }
        return Result.OK();
    }

    public void rewardUser(RewardReason reason, UserInfoVO user, RewardType type, RewardVipUnit unit, Integer value) {
        if (reason == null || user == null || type == null || value == null) {
            log.error("[MarketService.rewardUser][ERROR] reason={}, user={},type={},}valu={}", reason, user, type, value);
            return;
        }
        if (reason == RewardReason.START5) {
            user.setStar5State(DefaultFlag.YES);
        }
        if (type == RewardType.VIP) {
            MarketRewardVip reward = marketRewardVipService.createEntity(user, reason, unit, value);
            marketRewardVipService.updateUserVip(user, reward);
        } else {
            MarketRewardPoint reward = marketRewardPointService.createAdd(user, reason, value);
            marketRewardPointService.updateUserPoint(user, reward);
        }
    }

    public static boolean checkStaticValid(RewardType t, RewardVipUnit u, Integer v) {
        if (t == null) {
            return false;
        }
        if (t == RewardType.VIP) {
            return u != null && NumUtil.gt0(v);
        }
        return NumUtil.gt0(v);
    }

    @Override
    public boolean checkStar5ReviewV2(Long userId){
        return userRewardRecordMapper.existsByUserIdAndOpType(userId, 1);
    }

    @Override
    public Market5StarReviewVO star5ReviewV2(UserInfoVO userInfoVO, Star5Request star5Request) {
        // 校验事件类型是否合法
        if (!TriggerTypeConstant.isValidTriggerType(star5Request.getTriggerType())) {
            log.error("Invalid trigger type: {}", star5Request.getTriggerType());
            return Market5StarReviewVO.builder().build();
        }

        String configKey = SystemConfigConstant.MARKET_CONFIG_TYPE_IOS;
        if(Objects.nonNull(star5Request.getDevice()) && star5Request.getDevice().equals(1)){
            configKey = SystemConfigConstant.MARKET_CONFIG_TYPE_ANDROID;
        }
        SystemConfig systemConfig = systemConfigMapper.findByConfigKeyAndConfigType(MARKET_CONFIG_KEY, configKey);
        JSONObject jsonObject = JSONObject.parseObject(systemConfig.getContext());

        Integer maxOpIdx = userRewardRecordMapper.findMaxOpIdxByUserId(userInfoVO.getUserId());
        Integer opIdx = (maxOpIdx == null) ? 0 : maxOpIdx + 1;

        UserRewardRecord userRewardRecord = UserRewardRecord.builder()
                .userId(userInfoVO.getUserId())
                .opType(star5Request.getOpType())
                .opIdx(opIdx)
                .triggerType(star5Request.getTriggerType())
                .createTime(LocalDateTime.now())
                .build();

        if (star5Request.getOpType() == 1) {
            userRewardRecord.setRewardType(jsonObject.getString("star5_reward_type"));
            userRewardRecord.setRewardUnit(jsonObject.getString("star5_reward_unit"));
            userRewardRecord.setRewardValue(Integer.valueOf(jsonObject.getString("star5_reward_value")));

            userRewardRecordMapper.save(userRewardRecord);

            taskExecutor.execute(() -> {
                try {
                    log.info("1分钟后开始进行5星好评奖励");
                    TimeUnit.MINUTES.sleep(1);
                    if(checkStaticValid(
                            RewardType.valueOf(userRewardRecord.getRewardType()),
                            RewardVipUnit.valueOf(userRewardRecord.getRewardUnit()),
                            userRewardRecord.getRewardValue())) {
                        rewardUser(RewardReason.START5,
                                userInfoVO,
                                RewardType.valueOf(userRewardRecord.getRewardType()),
                                RewardVipUnit.valueOf(userRewardRecord.getRewardUnit()),
                                userRewardRecord.getRewardValue());
                    }
                    log.info("5星好评处理完成");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        } else {
            userRewardRecordMapper.save(userRewardRecord);
        }

        if (star5Request.getOpType() == 1) {
            return Market5StarReviewVO.builder()
                    .type(RewardType.valueOf(jsonObject.getString("star5_reward_type")))
                    .value(Integer.valueOf(jsonObject.getString("star5_reward_value")))
                    .build();
        } else {
            return Market5StarReviewVO.builder().build();
        }
    }


    @Override
    public RecoverSettingVO getRecoverSetting(Integer deviceType) {
        SystemConfig systemConfig = systemConfigMapper.findByConfigKeyAndConfigType("mobile_setting", "recover_setting");
        if (systemConfig == null) {
            return RecoverSettingVO.builder().build();
        }

        JSONObject jsonObject = JSONObject.parseObject(systemConfig.getContext());
        if (jsonObject == null) {
            return RecoverSettingVO.builder().build();
        }

        // 根据设备类型获取对应的code
        String code = null;
        if (deviceType != null) {
            if (deviceType == 1) { // 安卓
                code = jsonObject.getString("androidCode");
            } else if (deviceType == 2) { // iOS
                code = jsonObject.getString("iosCode");
            }
        }

        // 如果设备类型为空或未找到对应的code，使用默认值
        if (code == null) {
            code = jsonObject.getString("code");
        }

        Integer countdown = jsonObject.getInteger("countdown");

        TvMemberRechargeTypeVO codeVO = null;

        if (code != null) {
            TvMemberRechargeType rechargeType = tvMemberRechargeTypeMapper.findByCode(code);
            if (rechargeType != null) {
                codeVO = new TvMemberRechargeTypeVO();
                BeanUtils.copyProperties(rechargeType,codeVO);
            }
        }



        return RecoverSettingVO.builder()
                .countdown(countdown)
                .code(codeVO)
                .build();
    }
}
