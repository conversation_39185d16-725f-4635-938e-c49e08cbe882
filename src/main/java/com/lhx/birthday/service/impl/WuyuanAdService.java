// This file is auto-generated, don't edit it. Thanks.
package com.lhx.birthday.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.lhx.birthday.request.WuyuanAdRequest;
import com.lhx.birthday.service.IWuyuanAdService;
import com.lhx.birthday.util.IfconfigUtil;
import com.lhx.birthday.util.SignatureAlgorithm;
import com.lhx.birthday.util.StringUtils;
import com.lhx.birthday.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 百度AD推广服务
 */
@Slf4j
@Service
public class WuyuanAdService implements IWuyuanAdService {

    @Value("${app.code.name}")
    private String app;

    @Value("${baidu.ad.prod.url}")
    private String proUrl;

    @Value("${baidu.ad.test.url}")
    private String testUrl;

    @Override
    public Result trigger(WuyuanAdRequest wuyuanAdRequest){
        String appleAdToken = wuyuanAdRequest.getAppleAdToken();
        String osType = wuyuanAdRequest.getOsType();
        String osVersion = wuyuanAdRequest.getOsVersion();
        String ipx = wuyuanAdRequest.getIpAddrV6();
        String idfa = wuyuanAdRequest.getIdfa();
        String imei = wuyuanAdRequest.getImei();
        String oaid = wuyuanAdRequest.getOaid();
        // 获取用户id
        String customerId = wuyuanAdRequest.getUserId();
        // 设置签名
        SignatureAlgorithm signatureAlgorithm = new SignatureAlgorithm();
        String timestamp = String.valueOf(signatureAlgorithm.getCurrentTimestampInSeconds());
        String nonce = signatureAlgorithm.generateNonce().toLowerCase();
        String signature = signatureAlgorithm.generateSignature(timestamp, nonce);

        HttpRequest httpRequest = HttpRequest.post(testUrl+"/api/marketing/baidu-ad/app/trigger")
                .timeout(5000)
                .header("timestamp", timestamp)
                .header("nonce", nonce)
                .header("signature", signature);
        // 请求体参数
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("app", app);
        bodyMap.put("type", wuyuanAdRequest.getWuyuanAdActionType().toString().toLowerCase());
        bodyMap.put("idfa", idfa);
        bodyMap.put("osType", osType);
        bodyMap.put("osVersion", osVersion);
        bodyMap.put("ip", wuyuanAdRequest.getIpAddr());
        bodyMap.put("ipx", ipx);
        bodyMap.put("appleAdToken", appleAdToken);
        bodyMap.put("userId",customerId);

        bodyMap.put("oaid;",oaid);
        bodyMap.put("imei",imei);
        bodyMap.put("imeiMd5","");

        System.out.println("========");
        System.out.println("app"+app);
        System.out.println("type"+ wuyuanAdRequest.getWuyuanAdActionType().toString().toLowerCase());
        System.out.println("idfa"+ idfa);
        System.out.println("osType"+ osType);
        System.out.println("osVersion"+ osVersion);
        System.out.println("ip"+ wuyuanAdRequest.getIpAddr());
        System.out.println("ipx"+ ipx);
        System.out.println("appleAdToken"+ appleAdToken);
        System.out.println("userId"+customerId);
        System.out.println("oaid"+oaid);
        System.out.println("imei"+imei);
        System.out.println("========");

        httpRequest.body(JSONUtil.toJsonStr(bodyMap), ContentType.APPLICATION_JSON.toString());

        HttpResponse response = httpRequest.execute();
        if (response.isOk()) {
            // 解析响应体为JSON对象
            JSONObject jsonObject = JSONUtil.parseObj(response.body());
            int status = jsonObject.getInt("status");
            String error = jsonObject.getStr("error");
            String time = jsonObject.getStr("time");

            System.out.println("Status: " + status);
            System.out.println("Error: " + error);
            System.out.println("Time: " + time);
        } else {
            System.out.println("请求失败，状态码：" + response.getStatus());
        }
        return Result.OK();
    }

}
