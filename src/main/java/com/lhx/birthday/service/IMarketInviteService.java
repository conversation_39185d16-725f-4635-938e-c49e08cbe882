package com.lhx.birthday.service;

import com.lhx.birthday.request.market.InvitationHisRequest;
import com.lhx.birthday.request.market.InvitationRequest;
import com.lhx.birthday.vo.Result;
import com.lhx.birthday.vo.UserInfoVO;
import com.lhx.birthday.vo.market.InvitationHistoryVO;

public interface IMarketInviteService {

    Result invitation(InvitationRequest invitationRequest, UserInfoVO userInfoVO );

    InvitationHistoryVO getInvitationHistory(UserInfoVO userInfoVO, InvitationHisRequest invitationHisRequest);
}
