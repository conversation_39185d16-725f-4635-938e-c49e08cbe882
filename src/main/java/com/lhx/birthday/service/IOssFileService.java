package com.lhx.birthday.service;

import com.lhx.birthday.request.oss.OssAddRequest;
import com.lhx.birthday.response.StsResponse;
import com.lhx.birthday.vo.UploadVO;
import com.lhx.birthday.vo.UserInfoVO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * @Description: OOS云存储service接口
 * @author: jeecg-boot
 */
public interface IOssFileService {

    /**
     * oss文件上传
     * @param multipartFile
     * @throws IOException
     */
    UploadVO upload(MultipartFile multipartFile, UserInfoVO userInfoVO) throws Exception;

    UploadVO add(OssAddRequest ossAddRequest, UserInfoVO userInfoVO);

    StsResponse sts();

}
