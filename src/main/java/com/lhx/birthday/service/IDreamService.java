package com.lhx.birthday.service;

import com.lhx.birthday.request.dream.DreamDetailRequest;
import com.lhx.birthday.request.dream.DreamRequest;
import com.lhx.birthday.response.dream.DreamDetailResponse;
import com.lhx.birthday.response.dream.DreamHotResponse;
import com.lhx.birthday.response.dream.DreamResponse;

public interface IDreamService {

    DreamHotResponse dreamHotList();

    DreamResponse search(DreamRequest dreamRequest);

    DreamDetailResponse detail(DreamDetailRequest dreamDetailRequest);

}
