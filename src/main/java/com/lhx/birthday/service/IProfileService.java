package com.lhx.birthday.service;

import com.lhx.birthday.request.profile.ProfileBaseRequest;
import com.lhx.birthday.request.profile.ProfileUpdateSortRequest;
import com.lhx.birthday.response.profile.ProfileGroupResponse;
import com.lhx.birthday.vo.profile.ProfileVO;

import java.util.List;

public interface IProfileService {

    ProfileVO addProfile(ProfileBaseRequest profileBaseRequest);

    ProfileVO updateProfile(ProfileBaseRequest profileBaseRequest);

    int updateSort(ProfileUpdateSortRequest profileUpdateSortRequest);

    int delById(ProfileBaseRequest profileBaseRequest);

    List<ProfileVO> getProfileList(ProfileBaseRequest profileBaseRequest);

    ProfileGroupResponse getGroupList(ProfileBaseRequest profileBaseRequest);

}
