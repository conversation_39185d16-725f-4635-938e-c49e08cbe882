// This file is auto-generated, don't edit it. Thanks.
package com.lhx.birthday.service;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.lhx.birthday.request.WuyuanAdRequest;
import com.lhx.birthday.util.IfconfigUtil;
import com.lhx.birthday.util.SignatureAlgorithm;
import com.lhx.birthday.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 百度AD推广服务
 */
public interface IWuyuanAdService {

    Result trigger(WuyuanAdRequest baiduAdRequest);

}
