package com.lhx.birthday.service;

import com.lhx.birthday.request.market.IntegralPayRequest;
import com.lhx.birthday.request.market.Star5Request;
import com.lhx.birthday.vo.Result;
import com.lhx.birthday.vo.UserInfoVO;
import com.lhx.birthday.vo.market.*;

import java.util.List;

public interface IMarketService {

    /**
     * 检查用户是否已经提交过5星好评
     * @param userId 用户ID
     * @return 是否已提交
     */
    boolean checkStar5Review(Long userId);
    boolean checkStar5ReviewV2(Long userId);

    /**
     * 处理5星好评提交
     * @param userInfoVO 用户信息
     * @param star5Request 好评请求
     * @return 处理结果
     */
    Market5StarReviewVO star5Review(UserInfoVO userInfoVO, Star5Request star5Request);
    Market5StarReviewVO star5ReviewV2(UserInfoVO userInfoVO, Star5Request star5Request);

    PointHistoryVO getIntegralHistory(UserInfoVO userInfoVO);

    Result pointPay(IntegralPayRequest integralPayRequest,UserInfoVO userInfoVO);

    /**
     * 获取挽回设置
     * @param deviceType 设备类型 1:安卓 2:iOS
     * @return 挽回设置内容
     */
    RecoverSettingVO getRecoverSetting(Integer deviceType);
}
