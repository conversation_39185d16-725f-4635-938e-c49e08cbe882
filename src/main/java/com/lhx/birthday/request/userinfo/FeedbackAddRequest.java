package com.lhx.birthday.request.userinfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> lhx
 * @date 2023/7/28 14:48
 */
@Getter
@Setter
@ApiModel(description = "添加反馈记录请求参数")
public class FeedbackAddRequest {

    @ApiModelProperty(value = "系统")
    private String system;

    @ApiModelProperty(value = "联系方式")
    private String contact;

    @ApiModelProperty(value = "反馈内容")
    private String content;

    @ApiModelProperty(value = "机器名")
    private String machine;

    @ApiModelProperty(value = "系统版本")
    private String systemVersion;

}
