package com.lhx.birthday.request.userinfo;

import com.lhx.birthday.enums.DefaultFlag;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> lhx
 * @date 2023/10/30 16:17
 */
@Getter
@Setter
@ApiModel(description = "推送策略修改请求参数")
public class StrategyUpdateRequest {

    /**
     * 推送震级阈值
     */
    @ApiModelProperty(value = "推送震级阈值")
    private Double pushZj;

    /**
     * 关注地区推送震级阈值
     */
    @ApiModelProperty(value = "关注地区推送震级阈值")
    private Double followProvinceZj;

    /**
     * 关注地址推送震级阈值
     */
    @ApiModelProperty(value = "关注地址推送震级阈值")
    private Double followAddressZj;

    /**
     * 关注地址与震中的距离阈值
     */
    @ApiModelProperty(value = "关注地址与震中的距离阈值")
    private Integer followAddressDistance;

    /**
     * 关注人推送震级阈值
     */
    @ApiModelProperty(value = "关注人推送震级阈值")
    private Double followPersonZj;

    /**
     * 关注人与震中的距离阈值
     */
    @ApiModelProperty(value = "关注人与震中的距离阈值")
    private Integer followPersonDistance;

    /**
     * 是否开启 0:否 1:是
     */
    @ApiModelProperty(value = "是否开启 0:否 1:是")
    private DefaultFlag isOpen;
}
