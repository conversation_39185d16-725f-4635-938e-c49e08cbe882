package com.lhx.birthday.request.userinfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> lhx
 * @date 2023/10/30 16:17
 */
@Getter
@Setter
@ApiModel(description = "用户登录请求参数")
public class UserLoginRequest {

    @ApiModelProperty(value = "手机号")
    private String phone;

//    @ApiModelProperty(value = "uuid")
//    private String uuid;

    @ApiModelProperty(value = "验证码")
    private String code;

    @ApiModelProperty(value = "区号")
    private String areaCode;

    @ApiModelProperty(value = "推送id")
    private String registrationId;

}
