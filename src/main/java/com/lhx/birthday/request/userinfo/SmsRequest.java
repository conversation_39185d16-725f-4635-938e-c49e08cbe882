package com.lhx.birthday.request.userinfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> lhx
 * @date 2023/10/30 16:17
 */
@Getter
@Setter
@ApiModel(description = "请求短信验证码")
public class SmsRequest {

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "区号")
    private String areaCode;

//    /**
//     * 0 登录；1 更换/绑定手机号
//     */
//    @ApiModelProperty(value = "0 登录；1 更换/绑定手机号")
//    private Integer type;


}
