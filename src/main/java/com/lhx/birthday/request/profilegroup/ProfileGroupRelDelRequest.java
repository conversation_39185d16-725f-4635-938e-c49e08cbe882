package com.lhx.birthday.request.profilegroup;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> lhx
 * @date 2023/7/28 14:48
 */
@Getter
@Setter
@ApiModel(description = "删除分组下档案请求参数")
public class ProfileGroupRelDelRequest {

    @ApiModelProperty(value = "档案分组id")
    private Long profileGroupId;

    @ApiModelProperty(value = "档案id")
    private Long profileId;

}
