package com.lhx.birthday.request.profilegroup;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "修改分组下档案成员请求参数")
public class ProfileGroupRelUpdateRequest {

    @ApiModelProperty(value = "档案id列表",example = "2,3,4,5")
    private String profileIds;

    @ApiModelProperty(value = "档案id")
    private Long profileGroupId;

}
