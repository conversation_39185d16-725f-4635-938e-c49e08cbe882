package com.lhx.birthday.request.profilegroup;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> lhx
 * @date 2023/7/28 14:48
 */
@Getter
@Setter
@ApiModel(description = "更新分组排序请求参数")
public class ProfileGroupUpdateSortRequest {

    @ApiModelProperty(value = "ids",example = "2,3,4,5")
    private String ids;

}
