package com.lhx.birthday.request.horoscope;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "未来运势请求参数")
public class WeilaiRequest {

    @ApiModelProperty(value = "档案id")
    private Long proFileId;

    @ApiModelProperty(value = "1：晚子时日柱算明天 2：晚子时日柱算当天")
    private Integer sect;

    @ApiModelProperty(value = "需要测未来哪个公历年 例: 2030 ")
    private Integer yunshiYear;

    @ApiModelProperty(value = "是否算每日运势 例： 1：是 2：否")
    private Integer computeDaily;

    @ApiModelProperty(value = "是否真太阳时 例：1：考虑真太阳时 2：不考虑真太阳时")
    private Integer zhen;

    @ApiModelProperty(value = "例：北京市")
    private String province;

    @ApiModelProperty(value = "市 例：北京")
    private String city;
}
