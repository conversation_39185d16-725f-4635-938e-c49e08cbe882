package com.lhx.birthday.request.horoscope;

import cn.hutool.core.date.Zodiac;
import com.lhx.birthday.enums.ZodiacSignType;
import com.lhx.birthday.enums.ZodiacType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "生肖请求参数")
public class ShengxiaoRequest {

    @ApiModelProperty(value = "男方生肖")
    private ZodiacType maleZodiacType;

    @ApiModelProperty(value = "女方生肖")
    private ZodiacType femaleZodiacType;

}
