package com.lhx.birthday.request.horoscope;

import com.lhx.birthday.enums.ZodiacSignType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "星座请求参数")
public class XingzuoRequest {

    @ApiModelProperty(value = "男方星座")
    private ZodiacSignType maleZodiacSignType;

    @ApiModelProperty(value = "女方星座")
    private ZodiacSignType femaleZodiacSignType;

}
