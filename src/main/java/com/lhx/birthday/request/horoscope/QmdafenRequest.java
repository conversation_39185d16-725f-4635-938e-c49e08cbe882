package com.lhx.birthday.request.horoscope;

import com.lhx.birthday.request.BaseSearchRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "打分-起名打分请求参数")
public class QmdafenRequest extends BaseSearchRequest {


    @ApiModelProperty(value = "姓 例：张")
    private String surname;

    @ApiModelProperty(value = "姓名字数 例：2 两位姓名(张三) 3 三位姓名(张三四)")
    private Integer namewords;

    @ApiModelProperty(value = "性别 例：0男 1女 2不限性别")
    private Integer sex;

}