package com.lhx.birthday.request.horoscope;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lhx.birthday.enums.LanguageType;
import com.lhx.birthday.util.LocalDateDeserializer;
import com.lhx.birthday.util.LocalDateSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "老黄历请求参数")
public class LaohuangliRequest {

    @ApiModelProperty(value = "年月日时 例：1988-02-20 格式：YYYY-MM-DD")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate time;

    @ApiModelProperty(value = "语言类型 0简体中文 1繁体中文 2英文")
    private LanguageType languageType;

}
