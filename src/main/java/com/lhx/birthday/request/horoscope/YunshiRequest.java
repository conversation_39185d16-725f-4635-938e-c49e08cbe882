package com.lhx.birthday.request.horoscope;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "每日运势请求参数")
public class YunshiRequest {

    @ApiModelProperty(value = "档案id")
    private Long proFileId;

    @ApiModelProperty(value = "查询类型：范围是0～1，0 代表查询星座 ，1 代表查询生肖")
    private Integer type;

    @ApiModelProperty(value = "运势ID：范围是0～11\n" +
            "0 代表查询白羊座或者属鼠运势；1 代表查询金牛座或者属牛运势；\n" +
            "2 代表查询双子座或者属虎运势；3 代表查询巨蟹座或者属兔运势；\n" +
            "4 代表查询狮子座或者属龙运势；5 代表查询处女座或者属蛇运势；\n" +
            "6 代表查询天秤座或者属马运势；7 代表查询天蝎座或者属羊运势；\n" +
            "8 代表查询射手座或者属猴运势；9 代表查询魔羯座或者属鸡运势；\n" +
            "10 代表查询水瓶座或者属狗运势；11 代表查询双鱼座或者属猪运势；")
    private String titleYunshi;

}
