package com.lhx.birthday.request.horoscope;

import com.lhx.birthday.enums.OracleDrawsType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "灵签请求参数")
public class LingqianRequest {

    @ApiModelProperty(value = "灵签类型 0:佛祖 1:吕祖 2:妈祖 3:月老 4:观音 5:诸葛")
    private OracleDrawsType oracleDrawsType;



}
