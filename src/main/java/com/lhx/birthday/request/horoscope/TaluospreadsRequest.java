package com.lhx.birthday.request.horoscope;

import com.lhx.birthday.enums.OracleDrawsType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "多牌阵占卜法请求参数")
public class TaluospreadsRequest {

    @ApiModelProperty(value = "用户已经选择的塔罗牌牌号列表，")
    private String taluoUserChecked;



}
