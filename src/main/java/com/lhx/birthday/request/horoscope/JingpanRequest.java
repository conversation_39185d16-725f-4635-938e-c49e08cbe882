package com.lhx.birthday.request.horoscope;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "九星命理请求参数")
public class JingpanRequest {

    @ApiModelProperty(value = "档案id")
    private Long proFileId;

    @ApiModelProperty(value = "1：晚子时日柱算明天 2：晚子时日柱算当天")
    private Integer sect;

    @ApiModelProperty(value = "是否真太阳时 例：1：考虑真太阳时 2：不考虑真太阳时")
    private Integer zhen;

    @ApiModelProperty(value = "例：北京市")
    private String province;

    @ApiModelProperty(value = "市 例：北京")
    private String city;
}
