package com.lhx.birthday.request.horoscope;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "六道轮回请求参数")
public class LiupanRequest {

    @ApiModelProperty(value = "档案id")
    private Long proFileId;

    @ApiModelProperty(value = "闰月分界 0：月中分界 1：做下月")
    private Integer leapBound;

    @ApiModelProperty(value = "长生顺逆 1：全部顺排 2：按年干阴阳 3：按照男女")
    private Integer prosCons;

    @ApiModelProperty(value = "盘类型 0：先天盘+大限盘 1：流年盘 2：流月盘 3：流日盘 4：流时盘")
    private Integer panModel;

    @ApiModelProperty(value = "大限顺逆 1：阳男阴女顺,阳女阴男逆 2：男顺女逆")
    private Integer dxModel;

    @ApiModelProperty(value = "流年、月、日时的时间戳 例: 1698805691")
    private String liuTime;

    @ApiModelProperty(value = "1：晚子时日柱算明天 2：晚子时日柱算当天")
    private Integer sect;

    @ApiModelProperty(value = "是否真太阳时 例：1：考虑真太阳时 2：不考虑真太阳时")
    private Integer zhen;

    @ApiModelProperty(value = "例：北京市")
    private String province;

    @ApiModelProperty(value = "市 例：北京")
    private String city;

}
