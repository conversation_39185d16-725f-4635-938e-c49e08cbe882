package com.lhx.birthday.request.horoscope;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lhx.birthday.util.LocalDateDeserializer;
import com.lhx.birthday.util.LocalDateSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "择吉日请求参数")
public class ZeshiRequest {

    @ApiModelProperty(value = "未来时间范围 例：0.未来7天 1.未来半个月 2.未来1个月 3.未来3个月")
    private Integer future;

    @ApiModelProperty(value = "择何事 例：0.嫁娶 1.纳采|订婚 2.入宅 3.修造|装修\n" +
            "4.搬家 5.求嗣 6.纳财 7.开市\n" +
            "8.交易 9.置产 10.动土 11.出行\n" +
            "13.祭祀 14.祈福 15.沐浴\n" +
            "16.订盟 17.纳婿 18.修坟 19.破土\n" +
            "20.安葬 21.立碑 22.开生坟 23.合寿木\n" +
            "24.入殓 25.移柩 26.伐木 27.掘井\n" +
            "28.挂匾 29.栽种 30.入学 31.理发\n" +
            "32.会亲友 33.赴任 34.求医 35.治病")
    private Integer incident;


}
