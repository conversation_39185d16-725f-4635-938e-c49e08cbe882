package com.lhx.birthday.request.horoscope;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "十二星座/生肖运势列表请求参数")
public class ShierYunshiRequest {


    @ApiModelProperty(value = "查询类型：范围是0～1，0 代表查询星座 ，1 代表查询生肖")
    private Integer type;

    @ApiModelProperty(value = "查询时间")
    private String time;

}
