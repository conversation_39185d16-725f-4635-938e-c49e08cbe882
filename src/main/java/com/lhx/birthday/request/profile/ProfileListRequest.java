package com.lhx.birthday.request.profile;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lhx.birthday.enums.DefaultFlag;
import com.lhx.birthday.enums.GenderType;
import com.lhx.birthday.enums.Relationship;
import com.lhx.birthday.util.LocalDateTimeDeserializer;
import com.lhx.birthday.util.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lhx
 * @date 2023/7/28 14:48
 */
@Getter
@Setter
@ApiModel(description = "人员档案请求参数")
public class ProfileListRequest {

    @ApiModelProperty(value = "性别，0男 1女")
    private GenderType gender;

    @ApiModelProperty(value = "分组id")
    private Long groupId;

    @ApiModelProperty(value = "是否分组内 0否1是")
    private DefaultFlag inGroup;

}
