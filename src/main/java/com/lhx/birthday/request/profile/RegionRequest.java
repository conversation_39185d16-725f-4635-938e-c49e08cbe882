package com.lhx.birthday.request.profile;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lhx.birthday.enums.GenderType;
import com.lhx.birthday.enums.Relationship;
import com.lhx.birthday.util.LocalDateTimeDeserializer;
import com.lhx.birthday.util.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "地区请求")
public class RegionRequest {

    @ApiModelProperty(value = "国家编码")
    private String isoCountryCode;

    @ApiModelProperty(value = "一级")
    private String level1Eng;

    @ApiModelProperty(value = "二级")
    private String level2Eng;

    @ApiModelProperty(value = "三级")
    private String level3Eng;

}
