package com.lhx.birthday.request.profile;

import com.lhx.birthday.enums.GenderType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> lhx
 * @date 2023/7/28 14:48
 */
@Getter
@Setter
@ApiModel(description = "人员档案详情请求参数")
public class ProfileDetailRequest {

    @ApiModelProperty(value = "档案id")
    private Long id;

}
