package com.lhx.birthday.request.profile;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lhx.birthday.enums.BirthdayType;
import com.lhx.birthday.enums.DefaultFlag;
import com.lhx.birthday.enums.GenderType;
import com.lhx.birthday.enums.Relationship;
import com.lhx.birthday.util.LocalDateTimeDeserializer;
import com.lhx.birthday.util.LocalDateTimeSerializer;
import com.lhx.birthday.util.LocalTimeDeserializer;
import com.lhx.birthday.util.LocalTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <AUTHOR> lhx
 * @date 2023/7/28 14:48
 */
@Getter
@Setter
@ApiModel(description = "人员档案请求参数")
public class ProfileBaseRequest {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "头像url")
    private String avatar;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "性别，0男 1女")
    private GenderType gender;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(name = "生日 格式：yyyy-MM-dd HH:mm:ss")
//    @JsonSerialize(using = LocalDateTimeSerializer.class)
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private String birthdayTime;

    @ApiModelProperty(value = "生日类型 0公历 1农历")
    private BirthdayType birthdayType;

    @ApiModelProperty(value = "分组id列表",example = "2,3,4,5")
    private String groupIds;

    @ApiModelProperty(value = "分组id")
    private Long groupId;

    /**
     * 关系 0:自己 1:其他
     */
    @ApiModelProperty(value = "关系 0:自己 1:其他")
    private Relationship relationship;

    @ApiModelProperty(value = "生日当天 0否 1是")
    private DefaultFlag onTheDay;
    @ApiModelProperty(value = "提前1天 0否 1是")
    private DefaultFlag oneDayBefore;
    @ApiModelProperty(value = "提前3天 0否 1是")
    private DefaultFlag threeDaysBefore;
    @ApiModelProperty(value = "提前7天 0否 1是")
    private DefaultFlag sevenDaysBefore;
    @ApiModelProperty(value = "提前15天 0否 1是")
    private DefaultFlag fifteenDaysBefore;
    @ApiModelProperty(value = "提前30天 0否 1是")
    private DefaultFlag thirtyDaysBefore;

    @ApiModelProperty(value = "提醒时间")
    @JsonSerialize(using = LocalTimeSerializer.class)
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    private LocalTime promptTime;

    @ApiModelProperty(value = "双历提醒 0否 1是")
    private DefaultFlag promptFlag;

    @ApiModelProperty(value = "时区")
    private String timeZone;

    @ApiModelProperty(value = "备注")
    private String remarkInfo;

    private String registrationId;

    private DefaultFlag inGroup;
}
