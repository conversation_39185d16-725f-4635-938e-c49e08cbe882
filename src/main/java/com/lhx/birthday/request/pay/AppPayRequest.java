package com.lhx.birthday.request.pay;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/7/28 14:48
 */
@Getter
@Setter
@ApiModel(description = "统一下单请求参数")
public class AppPayRequest {

    @ApiModelProperty(value = "商品信息 例：com.wykj.fortune_week")
    private String code;

}
