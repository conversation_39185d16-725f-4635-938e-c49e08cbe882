package com.lhx.birthday.request.astrolabe;

import com.lhx.birthday.enums.LanguageType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "行运盘请求参数")
public class TransitRequest {

    @ApiModelProperty(value = "星座档案id", required = true)
    private Long constellationId;

    @ApiModelProperty(value = "星体id", required = true)
    private List<Object> planets;

    @ApiModelProperty(value = "小行星id")
    private List<Integer> planetXs;

    @ApiModelProperty(value = "虚星id")
    private List<Object> virtual;

    @ApiModelProperty(value = "宫位系统 默认k")
    private String hSys;

    @ApiModelProperty(value = "行运日期 时间格式1999-10-17 21:00:00")
    private String transitday;

    @ApiModelProperty(value = "1星盘语料 0或不传无语料")
    private String isCorpus;

    @ApiModelProperty(value = "数组下标为度数，值为允许度 例如 phase[90]=2 表示刑相90 允许度为2度")
    private Map<Integer, Float> phase;

    @ApiModelProperty(value = "1普通盘 0或不传 高级盘 -1不显示盘")
    private String svgType;

    @ApiModelProperty(value = "语言类型 0简体中文 1繁体中文 2英语")
    private LanguageType languageType;

    // 获取适合HTTP请求的参数名称
    public String getPlanetXsName() {
        return "planet_xs";
    }
    
    public String getHSysName() {
        return "h_sys";
    }
    
    public String getSvgTypeName() {
        return "svg_type";
    }
    
    public String getIsCorpusName() {
        return "is_corpus";
    }

    public String getTransitdayName() {
        return "transitday";
    }

    public String getPhaseName() {
        return "phase";
    }
} 