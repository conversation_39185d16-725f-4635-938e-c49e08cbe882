package com.lhx.birthday.request.astrolabe;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("推算星座请求")
public class SignPushRequest {
    @ApiModelProperty(value = "星座id 0开始", required = true)
    private String sign;

    @ApiModelProperty(value = "时间 2015-12-15 12:15", required = true)
    private String birthday;

    @ApiModelProperty(value = "计算多少年内。默认120年", required = true)
    private String years = "120";

    @ApiModelProperty(value = "最小单位0年1月2天3小时 默认年", required = true)
    private String unit = "0";
} 