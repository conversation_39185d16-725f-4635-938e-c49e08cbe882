package com.lhx.birthday.request.astrolabe;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "星历表请求")
public class EphemerisRequest {

    @ApiModelProperty(value = "开始日期", example = "2023-05-01")
    private String startDate;

    @ApiModelProperty(value = "结束日期", example = "2023-07-01")
    private String endDate;
} 