package com.lhx.birthday.request.astrolabe;

import com.lhx.birthday.enums.LanguageType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("星体信息查询请求 - 根据ID")
public class StarInfoByIdRequest {

    @ApiModelProperty("事件ID")
    private Long id;

    @ApiModelProperty(value = "语言类型 0简体中文 1繁体中文 2英语")
    private LanguageType languageType;
}