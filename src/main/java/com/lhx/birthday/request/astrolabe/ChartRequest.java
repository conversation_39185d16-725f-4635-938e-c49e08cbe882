package com.lhx.birthday.request.astrolabe;

import com.lhx.birthday.enums.LanguageType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "星盘请求参数")
public class ChartRequest {

    @ApiModelProperty(value = "盘类型（1:本命盘 2:行运盘 3:组合盘 4:三限盘 5:次限盘 6:月返照 7:日返照 8:太阳弧 9:法达盘 10:小限盘 " +
            "11:十二分盘 12:十三分盘 13:天象盘 14:比较盘-a 15:比较盘-b 16:组合三限盘 17:组合次限盘 " +
            "18:马克思盘-a 19:马克思盘-b 20:马盘三限盘-a 21:马盘三限盘-b 22:马盘次限盘-a 23:马盘次限盘-b " +
            "24:时空盘 25:时空三限盘 26:时空次限盘）", required = true)
    private Integer chartType;

    @ApiModelProperty(value = "星座档案id", required = true)
    private Long constellationId;
    
    @ApiModelProperty(value = "星座档案id列表（组合盘必填，需要两个档案id）")
    private List<Long> constellationIds;

    @ApiModelProperty(value = "星体id", required = true)
    private List<Object> planets;

    @ApiModelProperty(value = "小行星id")
    private List<Integer> planetXs;

    @ApiModelProperty(value = "虚星id")
    private List<Object> virtual;

    @ApiModelProperty(value = "宫位系统 默认k")
    private String hSys;

    @ApiModelProperty(value = "1普通盘 0或不传 高级盘 -1不显示盘")
    private String svgType;

    @ApiModelProperty(value = "数组下标为度数，值为允许度 例如 phase[90]=2 表示刑相90 允许度为2度")
    private Map<Integer, Float> phase;

    @ApiModelProperty(value = "行运日期 时间格式1999-10-17 21:00:00（仅行运盘使用）")
    private String transitday;

    @ApiModelProperty(value = "类型（0:行星 1:宫位 2:相位）", required = true)
    private Integer type;

    @ApiModelProperty(value = "语言类型 0简体中文 1繁体中文 2英语")
    private LanguageType languageType;
    
    public String getPlanetXsName() {
        return "planet_xs";
    }
    
    public String getHSysName() {
        return "h_sys";
    }
    
    public String getSvgTypeName() {
        return "svg_type";
    }
    
    public String getIsCorpusName() {
        return "is_corpus";
    }

    public String getTransitdayName() {
        return "transitday";
    }
    
}