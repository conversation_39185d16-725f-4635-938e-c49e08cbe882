package com.lhx.birthday.request.astrolabe;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "星体信息请求")
public class StarInfoRequest {

    @ApiModelProperty(value = "类型 1-行星相位 2-行星星座")
    private Integer type;

    @ApiModelProperty(value = "允许度数")
    private String allow;

    @ApiModelProperty(value = "行星代码1")
    private String planetCode1;

    @ApiModelProperty(value = "行星代码2")
    private String planetCode2;

    @ApiModelProperty(value = "行星代码")
    private String planetCode;

    @ApiModelProperty(value = "星座")
    private String sign;
} 