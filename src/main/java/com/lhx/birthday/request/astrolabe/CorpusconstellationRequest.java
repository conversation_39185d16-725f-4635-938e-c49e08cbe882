package com.lhx.birthday.request.astrolabe;

import com.lhx.birthday.enums.LanguageType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "语料列表请求参数")
public class CorpusconstellationRequest {

    @ApiModelProperty(value = "依照描述结构")
    private String fallInto;

    @ApiModelProperty(value = "星盘类型")
    private String chartType;

    @ApiModelProperty(value = "语言类型 0简体中文 1繁体中文 2英语")
    private LanguageType languageType;

}
