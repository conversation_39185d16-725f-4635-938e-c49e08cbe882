package com.lhx.birthday.request.oss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "添加附件请求参数")
public class OssAddRequest {

    @ApiModelProperty(value = "图片链接")
    private String url;

    /**
     * 图片宽
     */
    @ApiModelProperty(value = "图片宽")
    private Integer width;

    /**
     * 图片高
     */
    @ApiModelProperty(value = "图片高")
    private Integer height;
}
