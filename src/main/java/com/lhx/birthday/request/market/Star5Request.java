package com.lhx.birthday.request.market;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> lhx
 * @date 2023/7/28 14:48
 */
@Getter
@Setter
@ApiModel(description = "5星好评请求")
public class Star5Request {

    @ApiModelProperty(value = "客户端 0: ios 1:android")
    private Integer device;

    //    @NotNull(message = "操作类型不能为空")
//    @Min(value = 0, message = "操作类型错误")
//    @Max(value = 1, message = "操作类型错误")
    @ApiModelProperty(value = "操作类型 0:取消 1:确认", required = true)
    private Integer opType;

    //    @NotNull(message = "事件类型不能为空")
    @ApiModelProperty(value = "事件类型", required = true)
    private Integer triggerType;
}
