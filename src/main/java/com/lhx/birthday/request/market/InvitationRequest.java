package com.lhx.birthday.request.market;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> lhx
 * @date 2023/7/28 14:48
 */
@Getter
@Setter
@ApiModel(description = "兑换邀请")
public class InvitationRequest {

    @ApiModelProperty(value = "邀请码")
    private String code;

    @ApiModelProperty(value = "客户端 0: ios 1:android")
    private Integer device;

}
