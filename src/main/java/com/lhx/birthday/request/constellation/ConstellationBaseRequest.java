package com.lhx.birthday.request.constellation;

import com.lhx.birthday.enums.BirthdayType;
import com.lhx.birthday.enums.ConstellationRelationship;
import com.lhx.birthday.enums.GenderType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "星座档案基础请求")
public class ConstellationBaseRequest {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "性别，0男 1女")
    private GenderType gender;

    @ApiModelProperty(name = "生日 格式：yyyy-MM-dd HH:mm:ss")
    private String birthdayTime;

    @ApiModelProperty(value = "生日类型 0公历")
    private BirthdayType birthdayType;

    @ApiModelProperty(value = "关系")
    private ConstellationRelationship relationship;

    @ApiModelProperty(value = "出生地点")
    private String birthPlaceId;

    @ApiModelProperty(value = "居住地点")
    private String livingPlaceId;

    @ApiModelProperty(value = "时区")
    private String timeZone;
} 