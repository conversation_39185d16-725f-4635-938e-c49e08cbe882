package com.lhx.birthday.request;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

@Getter
@Setter
public class BaseSearchRequest  {

    private Integer pageNum = 1;
    private Integer pageSize = 20;
    private String sortKey = "id";
    private String sortMethod="desc";

    public Pageable getPageInfo(){
        setPageNum(getPageNum() - 1);
        Sort sort = null;
        if("desc".equals(sortMethod)){
            sort = Sort.by(Sort.Order.desc(sortKey));
        }else if("asc".equals(sortMethod)){
            sort = Sort.by(Sort.Order.asc(sortKey));
        }
        Pageable page = PageRequest.of(getPageNum(),getPageSize(),sort);
        return page;
    }

    public Sort getSortInfo(){
        Sort sort = null;
        if("desc".equalsIgnoreCase(sortMethod)){
            sort = Sort.by(Sort.Order.desc(sortKey));
        }else if("asc".equalsIgnoreCase(sortMethod)){
            sort = Sort.by(Sort.Order.asc(sortKey));
        }
        return sort;
    }
}
