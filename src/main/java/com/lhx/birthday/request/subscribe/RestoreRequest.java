package com.lhx.birthday.request.subscribe;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> lhx
 * @date 2023/10/30 16:17
 */
@Getter
@Setter
@ApiModel(description = "回复订阅请求参数")
public class RestoreRequest {

    @ApiModelProperty(value = "购买凭据")
    private String receipt;

//    @ApiModelProperty(value = "交易ID")
//    private String transactionId;

}
