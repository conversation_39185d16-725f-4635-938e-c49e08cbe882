package com.lhx.birthday.request.subscribe;

import com.lhx.birthday.request.BaseSearchRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> lhx
 * @date 2023/7/28 14:48
 */
@Getter
@Setter
@ApiModel(description = "订阅列表请求参数")
public class TvMemberRechargeTypeMapperBaseRequest extends BaseSearchRequest {

    @ApiModelProperty(value = "产品id")
    private String productId;

    @ApiModelProperty(value = "客户端 0: ios 1:android")
    private Integer device;

    @ApiModelProperty(value = "用户中心 0:否 1:是")
    private Integer userCenter;

}
