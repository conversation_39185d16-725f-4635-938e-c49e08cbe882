package com.lhx.birthday.request.subscribe;

import com.lhx.birthday.request.BaseSearchRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> lhx
 * @date 2023/7/28 14:48
 */
@Getter
@Setter
@ApiModel(description = "订阅请求参数")
public class UserPaySubscribeBaseRequest extends BaseSearchRequest {

    @ApiModelProperty(value = "交易id")
    private String transactionId;


}
