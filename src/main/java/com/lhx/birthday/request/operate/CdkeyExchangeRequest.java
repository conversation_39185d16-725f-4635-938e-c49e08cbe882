package com.lhx.birthday.request.operate;

import com.lhx.birthday.enums.OperateUnit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(description = "兑换激活码")
public class CdkeyExchangeRequest {

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "兑换吗")
    private String cdkey;

}
