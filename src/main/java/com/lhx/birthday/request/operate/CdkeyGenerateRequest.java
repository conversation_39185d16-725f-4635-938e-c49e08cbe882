package com.lhx.birthday.request.operate;

import com.lhx.birthday.enums.OperateUnit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(description = "生成激活码")
public class CdkeyGenerateRequest {

    @ApiModelProperty(value = "生成数量")
    private Integer num;

    @ApiModelProperty(value = "密码")
    private String pwd;

    @ApiModelProperty(value = "会员期限 取值范围0-7 1天(Day), 1个周(Week), 1个月(Moth), 1个季(Season), 半年（HalfAYear）, 1年(Year), 10年(Decade),100年(Year100)")
    private OperateUnit operateUnit;

    @ApiModelProperty(value = "代理人姓名")
    private String agentName;

}
