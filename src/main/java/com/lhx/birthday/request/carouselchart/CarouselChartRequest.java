package com.lhx.birthday.request.carouselchart;

import com.lhx.birthday.enums.DefaultFlag;
import com.lhx.birthday.enums.OperateUnit;
import com.lhx.birthday.request.BaseSearchRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Enumerated;

/**
 * <AUTHOR> lhx
 * @date 2023/7/28 14:48
 */
@Getter
@Setter
@ApiModel(description = "轮播图列表请求参数")
public class CarouselChartRequest {

//    @ApiModelProperty(value = "是否需要vip 0否1是")
//    private DefaultFlag vipState;

    @ApiModelProperty(value = "是否需登录 0否1是")
    private DefaultFlag loginState;

    @ApiModelProperty(value = "ios端是否显示 0否1是")
    private DefaultFlag iosState;

    @ApiModelProperty(value = "安卓端是否显示 0否1是")
    private DefaultFlag androidState;

}
