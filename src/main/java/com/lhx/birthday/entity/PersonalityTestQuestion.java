package com.lhx.birthday.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 性格测试题目实体
 * <AUTHOR> lhx
 */
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "personality_test_questions")
public class PersonalityTestQuestion implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "question_id")
    private Integer questionId;

    @Column(name = "question_text")
    private String questionText;

    @Column(name = "option_letter")
    private Character optionLetter;

    @Column(name = "option_text")
    private String optionText;

    @Column(name = "E")
    private Integer e;

    @Column(name = "I")
    private Integer i;

    @Column(name = "S")
    private Integer s;

    @Column(name = "N")
    private Integer n;

    @Column(name = "T")
    private Integer t;

    @Column(name = "F")
    private Integer f;

    @Column(name = "J")
    private Integer j;

    @Column(name = "P")
    private Integer p;
} 