package com.lhx.birthday.entity;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lhx.birthday.enums.*;
import com.lhx.birthday.util.LocalDateTimeDeserializer;
import com.lhx.birthday.util.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> lhx
 * @date 2023/10/23 10:54
 */
@Data
@Builder
@Entity
@AllArgsConstructor
@Table(name = "profile_info")
public class Profile implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long Id;

    private Long userId;

    @Column(name = "avatar")
    private String avatar;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别 0男 1女
     */
    @Enumerated
    private GenderType gender;

    /**
     * 生日
     */
    private LocalDateTime birthdayTime;

    /**
     * 生日类型 0公历 1农历
     */
    @Enumerated
    private BirthdayType birthdayType;

    /**
     * 公历
     */
    private LocalDateTime solarTime;

    /**
     * 农历
     */
    private String lunarTime;

    private Integer sort;

    /**
     * 星座
     */
    @Enumerated
    private ZodiacSignType zodiacSignType;

    /**
     * 生肖
     */
    @Enumerated
    private ZodiacType zodiacType;

    /**
     * 档案关系
     */
    @Enumerated
    private Relationship relationship;

    private String timeZone;

    private String remarkInfo;

    private LocalDateTime createTime;

    @Column(name = "modify_time")
    private LocalDateTime modifyTime;

    public Profile() {

    }
}
