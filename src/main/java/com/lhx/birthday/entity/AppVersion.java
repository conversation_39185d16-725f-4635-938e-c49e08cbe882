package com.lhx.birthday.entity;

import com.lhx.birthday.enums.DeleteFlag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@Entity
@AllArgsConstructor
@Table(name = "app_version")
public class AppVersion implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String sourceChannel;

    private String targetChannel;

    private String version;

    private String url;

    private Integer sortNo;

    private Integer state;

    private LocalDateTime createDate;

    private LocalDateTime modifyDate;

    public AppVersion() {

    }
}
