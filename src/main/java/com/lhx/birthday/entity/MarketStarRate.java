package com.lhx.birthday.entity;

import com.lhx.birthday.enums.DeleteFlag;
import com.lhx.birthday.enums.RewardType;
import com.lhx.birthday.enums.RewardVipUnit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> lhx
 * @date 2023/10/23 10:54
 */
@Data
@Builder
@Entity
@AllArgsConstructor
@Table(name = "market_star_rate")
public class MarketStarRate implements Serializable {
    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "reward_type")
    @Enumerated
    private RewardType rewardType;

    @Column(name = "reward_unit")
    @Enumerated
    private RewardVipUnit rewardUnit;

    @Column(name = "reward_value")
    private Integer rewardValue;

    private LocalDateTime createTime;

    public MarketStarRate() {

    }
}
