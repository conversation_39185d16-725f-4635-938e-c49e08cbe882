package com.lhx.birthday.entity;

import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@Table(name = "corpus_astro_en")
public class CorpusAstroEn {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    private Integer type;

    private String title;

    private String sentence;

    @Lob
    @Column(name = "chart_content_json", columnDefinition = "text")
    private String chartContentJson;

    @Lob
    @Column(name = "chart_content_markdown", columnDefinition = "text")
    private String chartContentMarkdown;

    private String titleCn;

    private String sentenceCn;
}