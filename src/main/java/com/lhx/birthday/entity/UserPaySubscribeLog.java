package com.lhx.birthday.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> lhx
 * @date 2023/10/30 15:31
 */
@Data
@Builder
@Entity
@AllArgsConstructor
@Table(name = "user_pay_subscribe_log")
public class UserPaySubscribeLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订阅ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    private String notificationType;

    private String subtype;

    private Long userId;

    /**
     * 创建日期，默认为当前时间戳
     */
    private LocalDateTime createDate;

    public UserPaySubscribeLog() {

    }
}
