package com.lhx.birthday.entity;

import com.lhx.birthday.enums.RewardReason;
import com.lhx.birthday.enums.RewardType;
import com.lhx.birthday.enums.RewardVipUnit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> lhx
 * @date 2023/10/23 10:54
 */
@Data
@Builder
@Entity
@AllArgsConstructor
@Table(name = "market_reward_vip")
public class MarketRewardVip implements Serializable {
    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    @Enumerated
    @Column(name = "unit")
    private RewardVipUnit unit;

    @Column(name = "value")
    private Integer value;

    @Column(name = "before_value")
    private Integer beforeValue;

    @Column(name = "after_value")
    private Integer afterValue;

    @Enumerated
    @Column(name = "reason")
    private RewardReason reason;

    private LocalDateTime createTime;

    public MarketRewardVip() {

    }

    public Integer toHours() {
        return RewardVipUnit.toHours(unit, value);
    }
}
