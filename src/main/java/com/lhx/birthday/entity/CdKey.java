package com.lhx.birthday.entity;

import com.lhx.birthday.enums.DefaultFlag;
import com.lhx.birthday.enums.DeleteFlag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@Entity
@AllArgsConstructor
@Table(name = "cdkey")
public class CdKey implements Serializable {
    private static final long serialVersionUID = 1L;

    @javax.persistence.Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long Id;

    private String code;

    /**
     * 1天(Day), 1个周(Week), 1个月(Moth), 1个季(Season), 半年（HalfAYear）, 1年(Year), 10年(Decade),100年(Year100)
     */
    private Integer type;

    /**
     * 使用电话
     */
    private String usePhone;

    /**
     * 使用时间
     */
    private LocalDateTime useDate;

    /**
     * 状态,0:未使用 1:已使用
     */
    @Column(name = "status")
    private DefaultFlag status;

    @Enumerated
    @Column(name = "delete_flag")
    private DeleteFlag deleteFlag;

    /**
     * 代理人姓名
     */
    private String agentName;

    /**
     * 开始时间
     */
    private LocalDateTime startDate;

    /**
     * 到期时间
     */
    private LocalDateTime expiryDate;

    private LocalDateTime createTime;

    public CdKey() {

    }
}
