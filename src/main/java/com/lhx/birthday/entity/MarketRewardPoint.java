package com.lhx.birthday.entity;

import com.lhx.birthday.enums.RewardReason;
import com.lhx.birthday.enums.RewardVipUnit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> lhx
 * @date 2023/10/23 10:54
 */
@Data
@Builder
@Entity
@AllArgsConstructor
@Table(name = "market_reward_point")
public class MarketRewardPoint implements Serializable {
    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "init_value")
    private Integer initValue;
    @Column(name = "valid_value")
    private Integer validValue;
    @Column(name = "before_value")
    private Integer beforeValue;
    @Column(name = "after_value")
    private Integer afterValue;

    /**
     * 发放原因
     */
    @Enumerated
    @Column(name = "reason")
    private RewardReason reason;

    /**
     * 变动类型
     */
    @Enumerated
    @Column(name = "type")
    private Type type;

    private LocalDateTime createTime;

    public enum Type {
        ADD, // 积分发放(-)
        USE, // 积分使用（兑换商品）（-）
        EXPIRY; // 积分过期（-）
    }

    public MarketRewardPoint() {

    }
}
