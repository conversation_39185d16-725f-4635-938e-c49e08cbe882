package com.lhx.birthday.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

@Table(name = "country_code")
@Data
@Builder
@Entity
@AllArgsConstructor
public class CountryCode implements Serializable {
    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "district_id")
    private String districtId;

    @Column(name = "country_eng")
    private String countryEng;

    @Column(name = "level1_eng")
    private String level1Eng;

    @Column(name = "level2_eng")
    private String level2Eng;

    @Column(name = "level3_eng")
    private String level3Eng;

    @Column(name = "country_chn")
    private String countryChn;

    @Column(name = "level1_chn")
    private String level1Chn;

    @Column(name = "level2_chn")
    private String level2Chn;

    @Column(name = "level3_chn")
    private String level3Chn;

    @Column(name = "iso_country_code")
    private String isoCountryCode;

    @Column(name = "lon")
    private BigDecimal lon;

    @Column(name = "lat")
    private BigDecimal lat;

    public CountryCode() {

    }
}