package com.lhx.birthday.entity;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lhx.birthday.enums.*;
import com.lhx.birthday.util.LocalTimeDeserializer;
import com.lhx.birthday.util.LocalTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <AUTHOR> lhx
 * @date 2023/10/23 10:54
 */
@Data
@Builder
@Entity
@AllArgsConstructor
@Table(name = "profile_prompt")
public class ProfilePrompt implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long Id;

    private Long userId;

    private Long profileId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 生日当天 0否 1是
     */
    @Enumerated
    private DefaultFlag onTheDay;

    /**
     * 提前1天 0否 1是
     */
    @Enumerated
    private DefaultFlag oneDayBefore;

    /**
     * 提前3天 0否 1是
     */
    @Enumerated
    private DefaultFlag threeDaysBefore;

    /**
     * 提前7天 0否 1是
     */
    @Enumerated
    private DefaultFlag sevenDaysBefore;

    /**
     * 提前15天 0否 1是
     */
    @Enumerated
    private DefaultFlag fifteenDaysBefore;

    /**
     * 提前30天 0否 1是
     */
    @Enumerated
    private DefaultFlag thirtyDaysBefore;

    /**
     * 提醒时间
     */
    private LocalTime promptTime;

    /**
     * 双历提醒 0否 1是
     */
    @Enumerated
    private DefaultFlag promptFlag;

    /**
     * 公历
     */
    private LocalDateTime solarTime;

    /**
     * 农历
     */
    private String lunarTime;

    /**
     * 生日类型 0公历 1农历
     */
    @Enumerated
    private BirthdayType birthdayType;

    private LocalDateTime createTime;

    @Column(name = "modify_time")
    private LocalDateTime modifyTime;

    private String nextSolarTime;

    private String nextLunarTime;

    public ProfilePrompt() {

    }
}
