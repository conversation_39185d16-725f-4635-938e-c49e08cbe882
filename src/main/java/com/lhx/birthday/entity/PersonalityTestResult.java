package com.lhx.birthday.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 性格测试结果实体
 * <AUTHOR> lhx
 */
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "personality_test_result")
public class PersonalityTestResult implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "user_id")
    private Long userId;
    
    @Column(name = "type_code")
    private String typeCode;
    
    @Column(name = "e_score")
    private Integer eScore;
    
    @Column(name = "i_score")
    private Integer iScore;
    
    @Column(name = "s_score")
    private Integer sScore;
    
    @Column(name = "n_score")
    private Integer nScore;
    
    @Column(name = "t_score")
    private Integer tScore;
    
    @Column(name = "f_score")
    private Integer fScore;
    
    @Column(name = "j_score")
    private Integer jScore;
    
    @Column(name = "p_score")
    private Integer pScore;
    
    @Column(name = "test_time")
    @CreatedDate
    private LocalDateTime testTime;
} 