package com.lhx.birthday.entity;

import com.lhx.birthday.enums.RewardType;
import com.lhx.birthday.enums.RewardVipUnit;
import com.lhx.birthday.vo.systemConfig.RewardSettingVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> lhx
 * @date 2023/10/23 10:54
 */
@Data
@Builder
@Entity
@AllArgsConstructor
@Table(name = "market_invite")
public class MarketInvite implements Serializable {
    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "invite_user_id")
    private Long inviteUserId;

    @Column(name = "invited_user_id")
    private Long invitedUserId;

    @Column(name = "code")
    private String code;

    @Column(name = "invited_user_phone")
    private String invitedUserPhone;

    @Column(name = "invited_user_uuid")
    private String invitedUserUuid;

    @Column(name = "invited_user_name")
    private String invitedUserName;


    @Enumerated
    @Column(name = "invite_reward_type")
    private RewardType inviteRewardType;

    @Enumerated
    @Column(name = "invite_reward_unit")
    private RewardVipUnit inviteRewardUnit;

    @Column(name = "invite_reward_value")
    private Integer inviteRewardValue;

    @Enumerated
    @Column(name = "invited_reward_type")
    private RewardType invitedRewardType;

    @Enumerated
    @Column(name = "invited_reward_unit")
    private RewardVipUnit invitedRewardUnit;

    @Column(name = "invited_reward_value")
    private Integer invitedRewardValue;


    public RewardSettingVO buildInviteRewardSetting() {
        return RewardSettingVO.from(this.inviteRewardType, this.inviteRewardUnit, this.inviteRewardValue);
    }

    private LocalDateTime createTime;

    public MarketInvite() {

    }
}
