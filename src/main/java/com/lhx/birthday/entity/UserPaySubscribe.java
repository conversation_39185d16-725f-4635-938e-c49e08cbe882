package com.lhx.birthday.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> lhx
 * @date 2023/10/30 15:31
 */
@Data
@Builder
@Entity
@AllArgsConstructor
@Table(name = "user_pay_subscribe")
public class UserPaySubscribe implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订阅ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 产品ID
     */
    private String productId;

    /**
     * 交易ID
     */
    private String transactionId;

    /**
     * 原始交易ID
     */
    private String originalTransactionId;

    /**
     * 原始购买日期
     */
    private String originalPurchaseDate;

    /**
     * 原始购买日期（毫秒）
     */
    private String originalPurchaseDateMs;

    /**
     * 购买日期
     */
    private String purchaseDate;

    /**
     * 购买日期（毫秒）
     */
    private String purchaseDateMs;

    /**
     * 到期日期
     */
    private String expiresDate;

    /**
     * 到期日期（毫秒）
     */
    private String expiresDateMs;

    /**
     * 数量
     */
    private String quantity;

    /**
     * 是否为试用期
     */
    private String isTrialPeriod;

    /**
     * 应用内拥有类型
     */
    private String inAppOwnershipType;

    /**
     * 是否在介绍优惠期内
     */
    private String isInIntroOfferPeriod;

    /**
     * 订阅组标识符
     */
    private String subscriptionGroupIdentifier;

    /**
     * 网络订单行项目ID
     */
    private String webOrderLineItemId;

    /**
     * 状态：<br>
     * 1：有效<br>
     * 2：过期<br>
     * 3：账号扣费重试<br>
     * 4：账号宽限期(这个是开发者设置，比如到期扣费失败时，可以给用户延期多长时间。)<br>
     * 5：已退款
     */
    private Integer status;

//    private String signedTransactionInfo;

//    private String signedRenewalInfo;

    private Integer autoRenewStatus;

    /**
     * 取消日期
     */
    private LocalDateTime cancelDate;

    /**
     * 创建日期，默认为当前时间戳
     */
    private LocalDateTime createDate;

    /**
     * 订阅次数
     */
    private Integer subCount;

    /**
     * 支付次数
     */
    private Integer payCount;

    public UserPaySubscribe() {

    }
}
