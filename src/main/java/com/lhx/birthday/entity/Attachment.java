package com.lhx.birthday.entity;

import com.lhx.birthday.enums.DeleteFlag;
import io.swagger.models.auth.In;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Builder
@Entity
@AllArgsConstructor
@Table(name = "attachment")
public class Attachment implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 附件名称
     */
    private String fileName;

    /**
     * 链接
     */
    private String url;

    /**
     * 文件类型
     */
    private String type;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 图片宽
     */
    private Integer width;

    /**
     * 图片高
     */
    private Integer height;

    /**
     * 删除标识,0:未删除1:已删除
     */
    @Column(name = "del_flag")
    @Enumerated
    private DeleteFlag delFlag;

    public Attachment() {

    }
}
