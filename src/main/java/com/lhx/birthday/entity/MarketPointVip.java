package com.lhx.birthday.entity;

import com.lhx.birthday.enums.DeleteFlag;
import com.lhx.birthday.enums.RewardVipUnit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @desc 5星好评
 * @date 2023-11-25
 */
@Data
@Builder
@Entity
@AllArgsConstructor
@Table(name = "market_point_vip")
public class MarketPointVip implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "vip_unit")
    @Enumerated
    private RewardVipUnit vipUnit;

    @Column(name = "vip_value")
    private Integer vipValue;

    @Column(name = "point_value")
    private Integer pointValue;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    /**
     * 删除标识,0:未删除1:已删除
     */
    @Column(name = "del_flag")
    @Enumerated
    private DeleteFlag delFlag;

    private Integer device;

    public MarketPointVip() {

    }
}
