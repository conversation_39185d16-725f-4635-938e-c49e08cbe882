package com.lhx.birthday.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR> lhx
 * @date 2023/10/23 10:54
 */
@Data
@Builder
@Entity
@AllArgsConstructor
@Table(name = "solar_terms")
public class SolarTerms implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long Id;

    private Integer year;

    /**
     * 节气数据
     */
    private String dataInfo;

    public SolarTerms() {

    }
}
