package com.lhx.birthday.entity;

import com.lhx.birthday.enums.BirthdayType;
import com.lhx.birthday.enums.ConstellationRelationship;
import com.lhx.birthday.enums.GenderType;
import com.lhx.birthday.enums.ZodiacSignType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import javax.persistence.*;
import java.time.LocalDateTime;

@Data
@Builder
@Entity
@AllArgsConstructor
@Table(name = "constellation_profile")
public class ConstellationProfile {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    private Long userId;
    
    private String name;

    /**
     * 性别 0男 1女
     */
    @Enumerated
    private GenderType gender;
    
    @Enumerated
    private ConstellationRelationship relationship;

    /**
     * 生日
     */
    private String birthdayTime;

    /**
     * 生日类型 0公历 1农历
     */
    @Enumerated
    private BirthdayType birthdayType;

    private String birthPlaceId;
    
    private String livingPlaceId;
    
    private Integer sort;

    /**
     * 星座
     */
    @Enumerated
    private ZodiacSignType zodiacSignType;

    /**
     * 时区
     */
    private String timeZone;

    private LocalDateTime createTime;

    @Column(name = "modify_time")
    private LocalDateTime modifyTime;

    /**
     * 太阳星座
     */
    private String sunSign;

    /**
     * 月亮星座
     */
    private String moonSign;

    /**
     * 上升星座
     */
    private String ascendantSign;

    public ConstellationProfile() {

    }
}