package com.lhx.birthday.entity;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "astro_calendar_event")
public class AstroCalendarEvent implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "event_datetime")
    private LocalDateTime eventDatetime;

    @Column(name = "allow")
    private Integer allow;

    @Column(name = "planet_code1")
    private String planetCode1;

    @Column(name = "planet_code2")
    private String planetCode2;

    @Column(name = "type")
    private Integer type;

    @Column(name = "title")
    private String title;

    @Column(name = "title_short")
    private String titleShort;

    @Column(name = "planet_english1")
    private String planetEnglish1;

    @Column(name = "planet_chinese1")
    private String planetChinese1;

    @Column(name = "planet_english2")
    private String planetEnglish2;

    @Column(name = "planet_chinese2")
    private String planetChinese2;

    @Column(name = "allow_cha")
    private Double allowCha;

    @Column(name = "planet_font1")
    private String planetFont1;

    @Column(name = "planet_font2")
    private String planetFont2;

    @Column(name = "event_time")
    private String eventTime;

    @Column(name = "timestamp_val")
    private Long timestampVal;

    @Column(name = "longitude")
    private Double longitude;

    @Column(name = "end_date")
    private String endDate;

    @Column(name = "planet_code")
    private String planetCode;

    @Column(name = "sign")
    private String sign;

    @Column(name = "sign_english")
    private String signEnglish;

    @Column(name = "sign_font")
    private String signFont;

    @Column(name = "sign_chinese")
    private String signChinese;
} 