package com.lhx.birthday.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR> lhx
 * @date 2024/05/13 10:54
 */
@Data
@Builder
@Entity
@AllArgsConstructor
@Table(name = "corpus")
public class Corpus implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    private String planetId;

    private String planetChinese;

    private String houseId;

    private String houseChinese;

    private String signId;

    private String signChinese;

    private String content;

    // 本命盘-语料
    private String newContent;

    // 行运盘-语料
    private String transitChartContent;

    // 组合盘-语料
    private String combinationChartContent;

    // 三限盘-语料
    private String thirdprogressedChartContent;

    // 次限盘-语料
    private String secondarylimitChartContent;
    
    // 月返照-语料
    private String lunarreturnChartContent;
    
    // 日返照-语料
    private String solarreturnChartContent;
    
    // 太阳弧-语料
    private String solararcChartContent;
    
    // 法达盘-语料
    private String developedChartContent;
    
    // 小限盘-语料
    private String smalllimitChartContent;
    
    // 十二分盘-语料
    private String nataltwelvepointerChartContent;
    
    // 十三分盘-语料
    private String natalthirteenpointerChartContent;
    
    // 天象盘-语料
    private String currentChartContent;
    
    // 比较盘-a-语料
    @Column(name = "comparision_a_chart_content")
    private String comparisionAChartContent;

    // 比较盘-b-语料
    @Column(name = "comparision_b_chart_content")
    private String comparisionBChartContent;

    // 组合三限盘-语料
    private String compositethirprogrChartContent;

    // 组合次限盘-语料
    private String compositesecondaryChartContent;

    // 马克思盘-a-语料
    @Column(name = "marks_a_chart_content")
    private String marksAChartContent;

    // 马克思盘-b-语料
    @Column(name = "marks_b_chart_content")
    private String marksBChartContent;
    
    // 马盘三限盘-a-语料
    @Column(name = "marksthirprogr_a_chart_content")
    private String marksthirprogrAChartContent;

    // 马盘三限盘-b-语料
    @Column(name = "marksthirprogr_b_chart_content")
    private String marksthirprogrBChartContent;

    // 马盘次限盘-a-语料
    @Column(name = "markssecprogr_a_chart_content")
    private String markssecprogrAChartContent;

    // 马盘次限盘-b-语料
    @Column(name = "markssecprogr_b_chart_content")
    private String markssecprogrBChartContent;
    
    // 时空盘-语料
    private String timesmidpointChartContent;
    
    // 时空三限盘-语料
    private String timesmidpointthirprogrChartContent;
    
    // 时空次限盘-语料
    private String timesmidpointsecprogrChartContent;

    // markdown-语料
    private String chartContentMarkdown;

    private String title;

    private String planetAllowId;

    private String planetAllowChinese;

    private String allow;

    public Corpus() {

    }
}
