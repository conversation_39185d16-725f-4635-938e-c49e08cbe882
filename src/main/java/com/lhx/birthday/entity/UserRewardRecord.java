package com.lhx.birthday.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 用户好评记录表实体类
 */
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "user_reward_records")
public class UserRewardRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 操作类型：0-取消，1-确定
     */
    @Column(name = "op_type")
    private Integer opType;

    /**
     * 操作序号，从0开始
     */
    @Column(name = "op_idx")
    private Integer opIdx;

    /**
     * 触发类型
     */
    @Column(name = "trigger_type")
    private Integer triggerType;

    /**
     * 奖励类型
     */
    @Column(name = "reward_type")
    private String rewardType;

    /**
     * 奖励单位
     */
    @Column(name = "reward_unit")
    private String rewardUnit;

    /**
     * 奖励值
     */
    @Column(name = "reward_value")
    private Integer rewardValue;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;
}