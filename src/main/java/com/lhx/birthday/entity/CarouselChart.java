package com.lhx.birthday.entity;

import com.lhx.birthday.enums.DefaultFlag;
import com.lhx.birthday.enums.DeleteFlag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Builder
@Entity
@AllArgsConstructor
@Table(name = "carousel_chart")
public class CarouselChart implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long Id;

    private String type;

    private String link;

    private String bannerUrl;

    private String bannerUrlEn;

    private String bannerUrlHant;

    private String param;

    private String targeting;

    /**
     * 是否需要vip
     */
    @Enumerated
    private DefaultFlag vipState;

    /**
     * 是否需登录
     */
    @Enumerated
    private DefaultFlag loginState;

    /**
     * ios端是否显示
     */
    @Enumerated
    private DefaultFlag iosState;

    /**
     * 安卓端是否显示
     */
    @Enumerated
    private DefaultFlag androidState;

    /**
     * 删除标识,0:未删除1:已删除
     */
    @Column(name = "del_flag")
    @Enumerated
    private DeleteFlag delFlag;

    private Integer sort;

    public CarouselChart() {

    }
}
