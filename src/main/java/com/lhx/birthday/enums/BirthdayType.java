package com.lhx.birthday.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.lhx.birthday.annotation.ApiEnum;
import com.lhx.birthday.annotation.ApiEnumProperty;

/**
 * 生日类型 0公历 1农历
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/3/22.
 */
@ApiEnum
public enum BirthdayType {
    @ApiEnumProperty("公历")
    SOLAR,
    @ApiEnumProperty("农历")
    LUNAR;
    @JsonCreator
    public static BirthdayType fromValue(int value) {
        return values()[value];
    }

    @JsonValue
    public int toValue() {
        return this.ordinal();
    }
}
