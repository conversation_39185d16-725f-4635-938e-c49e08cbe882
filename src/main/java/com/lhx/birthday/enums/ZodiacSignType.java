package com.lhx.birthday.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.lhx.birthday.annotation.ApiEnum;
import com.lhx.birthday.annotation.ApiEnumProperty;

/**
 * 星座类型 0白羊座 1金牛座 2双子座 3巨蟹座 4狮子座 5处女座 6天秤座 7天蝎座 8射手座 9摩羯座 10水瓶座 11双鱼座
 * <AUTHOR>
 */
@ApiEnum
public enum ZodiacSignType {

    @ApiEnumProperty("白羊座")
    ARIES("白羊座"),

    @ApiEnumProperty("金牛座")
    TAURUS("金牛座"),

    @ApiEnumProperty("双子座")
    GEMINI("双子座"),

    @ApiEnumProperty("巨蟹座")
    CANCER("巨蟹座"),

    @ApiEnumProperty("狮子座")
    LEO("狮子座"),

    @ApiEnumProperty("处女座")
    VIRGO("处女座"),

    @ApiEnumProperty("天秤座")
    LIBRA("天秤座"),

    @ApiEnumProperty("天蝎座")
    SCORPIO("天蝎座"),

    @ApiEnumProperty("射手座")
    SAGITTARIUS("射手座"),

    @ApiEnumProperty("摩羯座")
    CAPRICORN("摩羯座"),

    @ApiEnumProperty("水瓶座")
    AQUARIUS("水瓶座"),

    @ApiEnumProperty("双鱼座")
    PISCES("双鱼座");

    private static final ZodiacSignType[] VALUES = values();

    private String value;

    ZodiacSignType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static ZodiacSignType fromValue(int value) {
        if (value < 0 || value >= VALUES.length) {
            throw new IllegalArgumentException("Invalid value for ZodiacSignType: " + value);
        }
        return VALUES[value];
    }

    @JsonValue
    public int toValue() {
        return this.ordinal();
    }
}