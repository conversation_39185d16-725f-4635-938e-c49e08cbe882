package com.lhx.birthday.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.lhx.birthday.annotation.ApiEnum;
import com.lhx.birthday.annotation.ApiEnumProperty;

/**
 * <AUTHOR>
 * 0:激活 1:注册 2:付费成单
 */
@ApiEnum
public enum WuyuanAdActionType {

    /**
     * 激活
     */
    @ApiEnumProperty("0：激活")
    ACTIVATE(0),
    /**
     * 注册
     */
    @ApiEnumProperty("1：注册")
    REGISTER(1),

    /**
     * 付费成单
     */
    @ApiEnumProperty("2：付费成单")
    ORDERS(2);


    private int type;

    WuyuanAdActionType(int type){
        this.type = type;
    }

    @JsonValue
    public int toValue() {
        return this.type;
    }

    public static WuyuanAdActionType fromValue(int value) {
        for (WuyuanAdActionType source : WuyuanAdActionType.values()) {
            if (source.type == value) {
                return source;
            }
        }
        throw new IllegalArgumentException("No enum constant with value " + value + " found.");
    }
}
