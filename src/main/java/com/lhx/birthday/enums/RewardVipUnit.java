package com.lhx.birthday.enums;


import com.lhx.birthday.annotation.ApiEnumProperty;
import com.lhx.birthday.util.SafeUtil;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/12/1
 */
public enum RewardVipUnit {
    @ApiEnumProperty("0:小时")
    HOUR,
    @ApiEnumProperty("1:天")
    DAY;

    public static final Integer toHours(RewardVipUnit unit, Integer value) {
        if (unit == null) {
            return 0;
        }
        value = SafeUtil.of(value);
        switch (unit) {
            case HOUR:
                return value;
            case DAY:
                return value * 24;
            default:
                return 0;
        }
    }
}
