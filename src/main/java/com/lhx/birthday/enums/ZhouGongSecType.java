package com.lhx.birthday.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.lhx.birthday.annotation.ApiEnum;
import com.lhx.birthday.annotation.ApiEnumProperty;

/**
 * <AUTHOR>
 */
@ApiEnum
public enum ZhouGongSecType {

    @ApiEnumProperty("Organs")
    ORGANS("身体器官"),

    @ApiEnumProperty("Titles for People")
    PEOPLE_TITLES("人物称谓"),

    @ApiEnumProperty("Mammals")
    MAMMALS("哺乳类"),

    @ApiEnumProperty("Cold-blooded Animals")
    COLD_BLOODED_ANIMALS("冷血类"),

    @ApiEnumProperty("Birds")
    BIRDS("鸟禽类"),

    @ApiEnumProperty("Insects")
    INSECTS("昆虫类"),

    @ApiEnumProperty("Other Animals")
    OTHER_ANIMALS("其它动物"),

    @ApiEnumProperty("Melons")
    MELONS("瓜类"),

    @ApiEnumProperty("Fruits")
    FRUITS("果实"),

    @ApiEnumProperty("Flowers and Plants")
    FLOWERS_AND_PLANTS("花草"),

    @ApiEnumProperty("Trees")
    TREES("树类"),

    @ApiEnumProperty("Legumes")
    LEGUMES("豆类"),

    @ApiEnumProperty("Vegetables")
    VEGETABLES("蔬菜"),

    @ApiEnumProperty("Grains")
    GRAINS("粮食"),

    @ApiEnumProperty("Other Plants")
    OTHER_PLANTS("其它植物"),

    @ApiEnumProperty("Transportation")
    TRANSPORTATION("交通运输"),

    @ApiEnumProperty("Food and Beverage")
    FOOD_AND_BEVERAGE("食品饮料"),

    @ApiEnumProperty("Daily Necessities")
    DAILY_NESSECITIES("生活用品"),

    @ApiEnumProperty("Cultural Items")
    CULTURAL_ITEMS("文化用品"),

    @ApiEnumProperty("Clothing and Accessories")
    CLOTHING_AND_ACCESSORIES("服装饰品"),

    @ApiEnumProperty("Valuables and Jewelry")
    VALUABLES_AND_JEWELRY("财物宝石"),

    @ApiEnumProperty("Music and Dance")
    MUSIC_AND_DANCE("音乐舞蹈"),

    @ApiEnumProperty("Machinery and Appliances")
    MACHINERY_AND_APPLIANCES("机械电器"),

    @ApiEnumProperty("Weapons and Chemicals")
    WEAPONS_AND_CHEMICALS("武器化学"),

    @ApiEnumProperty("Other")
    OTHER_CATEGORY("其它"),

    @ApiEnumProperty("Work and Study")
    WORK_AND_STUDY("工作学习"),

    @ApiEnumProperty("Labor")
    LABOR("劳动类"),

    @ApiEnumProperty("Daily Life")
    DAILY_LIFE("日常类"),

    @ApiEnumProperty("Sports")
    SPORTS("运动类"),

    @ApiEnumProperty("Entertainment")
    ENTERTAINMENT("娱乐类"),

    @ApiEnumProperty("Actions")
    ACTIONS("动作类"),

    @ApiEnumProperty("Other Activities")
    OTHER_ACTIVITIES("其它活动"),

    @ApiEnumProperty("Marriage and Emotions")
    MARRIAGE_AND_EMOTIONS("婚恋情感"),

    @ApiEnumProperty("Health - Feelings and Expressions")
    HEALTH_FEELINGS("感觉表情"),

    @ApiEnumProperty("Health - Clothing, Food, Shelter, Travel")
    HEALTH_LIFESTYLE("衣食住行"),

    @ApiEnumProperty("Health - Medical Treatment and Diseases")
    HEALTH_MEDICAL("医疗疾病"),

    @ApiEnumProperty("Health - Disasters and Evils")
    HEALTH_DISASTERS("灾难罪恶"),

    @ApiEnumProperty("Health - Other Aspects of Life")
    HEALTH_OTHER_LIFE("其它生活"),

    @ApiEnumProperty("Health - Geographical Environment")
    HEALTH_GEOGRAPHY("地理环境"),

    @ApiEnumProperty("Health - Natural Phenomena")
    HEALTH_NATURE("自然现象"),

    @ApiEnumProperty("Deities and Immortals")
    DEITIES_AND_IMMORTALS("神仙类"),

    @ApiEnumProperty("Ghosts and Monsters")
    GHOSTS_AND_MONSTERS("鬼怪类"),

    @ApiEnumProperty("Religion")
    RELIGION("宗教类"),

    @ApiEnumProperty("Other Supernatural Beings")
    OTHER_SUPERNATURAL("其它鬼神"),

    @ApiEnumProperty("Architecture")
    ARCHITECTURE("建筑类"),

    @ApiEnumProperty("Places")
    PLACES("场所类"),

    @ApiEnumProperty("Numbers and Shapes")
    NUMBERS_AND_SHAPES("数字形状"),

    @ApiEnumProperty("Colors and Scents")
    COLORS_AND_SCENTS("颜色气味"),

    @ApiEnumProperty("Time and Festivals")
    TIME_AND_FESTIVALS("时间节日"),

    @ApiEnumProperty("objects")
    OBJECTS("物品"),

    @ApiEnumProperty("animals")
    ANIMALS("动物"),

    @ApiEnumProperty("events")
    EVENTS("事件"),

    @ApiEnumProperty("plants")
    PLANTS("植物");

    private static final ZhouGongSecType[] VALUES = values();

    private String value;

    ZhouGongSecType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    // 此处的 fromValue 方法可能需要根据实际业务逻辑进行修改
    public static ZhouGongSecType fromValue(int value) {
        if (value < 0 || value >= VALUES.length) {
            throw new IllegalArgumentException("Invalid value for ZhouGongSecType: " + value);
        }
        return VALUES[value];
    }

    @JsonValue
    public int toValue() {
        return this.ordinal();
    }
}