package com.lhx.birthday.enums;

import com.fasterxml.jackson.annotation.JsonValue;

public enum ConstellationRelationship {
    SELF("自己"),
    LOVER("恋人"),
    FRIEND("朋友"),
    FAMILY("亲友"),
    WORK("工作"),
    CLIENT("客户"),
    CASE("案例"),
    OTHER("其他");

    private String value;

    ConstellationRelationship(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    @JsonValue
    public int toValue() {
        return this.ordinal();
    }
} 