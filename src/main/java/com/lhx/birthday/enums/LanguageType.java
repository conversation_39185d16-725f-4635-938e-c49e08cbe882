package com.lhx.birthday.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.lhx.birthday.annotation.ApiEnum;
import com.lhx.birthday.annotation.ApiEnumProperty;

/**
 * 语言类型 0简体中文 1繁体中文
 * <AUTHOR>
 */
@ApiEnum
public enum LanguageType {

    @ApiEnumProperty("简体中文")
    ZH_CN,

    @ApiEnumProperty("繁体中文")
    ZH_HANT,

    @ApiEnumProperty("英文")
    EN;

    private static final LanguageType[] VALUES = values();

    public static LanguageType fromValue(int value) {
        if (value < 0 || value >= VALUES.length) {
            throw new IllegalArgumentException("Invalid value for ZodiacType: " + value);
        }
        return VALUES[value];
    }
    @JsonValue
    public int toValue() {
        return this.ordinal();
    }
}
