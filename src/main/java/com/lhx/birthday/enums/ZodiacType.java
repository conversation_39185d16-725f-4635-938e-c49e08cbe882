package com.lhx.birthday.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.lhx.birthday.annotation.ApiEnum;
import com.lhx.birthday.annotation.ApiEnumProperty;

/**
 * 生肖类型 0鼠 1牛 2虎 3兔 4龙 5蛇 6马 7羊 8猴 9鸡 10狗 11猪
 * <AUTHOR>
 */
@ApiEnum
public enum ZodiacType {

    @ApiEnumProperty("鼠")
    RAT("鼠"),

    @ApiEnumProperty("牛")
    OX("牛"),

    @ApiEnumProperty("虎")
    TIGER("虎"),

    @ApiEnumProperty("兔")
    RABBIT("兔"),

    @ApiEnumProperty("龙")
    DRAGON("龙"),

    @ApiEnumProperty("蛇")
    SNAKE("蛇"),

    @ApiEnumProperty("马")
    HORSE("马"),

    @ApiEnumProperty("羊")
    GOAT("羊"),

    @ApiEnumProperty("猴")
    MONKEY("猴"),

    @ApiEnumProperty("鸡")
    ROOSTER("鸡"),

    @ApiEnumProperty("狗")
    DOG("狗"),

    @ApiEnumProperty("猪")
    PIG("猪");

    private static final ZodiacType[] VALUES = values();

    private String value;

    ZodiacType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static ZodiacType fromValue(int value) {
        if (value < 0 || value >= VALUES.length) {
            throw new IllegalArgumentException("Invalid value for ZodiacType: " + value);
        }
        return VALUES[value];
    }

    @JsonValue
    public int toValue() {
        return this.ordinal();
    }
}
