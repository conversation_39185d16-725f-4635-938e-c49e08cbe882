package com.lhx.birthday.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.lhx.birthday.annotation.ApiEnum;
import com.lhx.birthday.annotation.ApiEnumProperty;

import java.util.Random;

/**
 * 0:佛祖 1:吕祖 2:妈祖 3:月老 4:观音 5:诸葛
 * <AUTHOR>
 */
@ApiEnum
public enum OracleDrawsType {
    @ApiEnumProperty("0:佛祖")
    FOZU,

    @ApiEnumProperty("1:吕祖")
    LVZU,

    @ApiEnumProperty("2:妈祖")
    MAZU,

    @ApiEnumProperty("3:月老")
    YUELAO,

    @ApiEnumProperty("4:观音")
    GUANYIN,

    @ApiEnumProperty("5:诸葛")
    ZHUGE;

    public int getRandomNumber() {
        Random random = new Random();
        switch (this) {
            case FOZU:
                return random.nextInt(51) + 1;
            case MAZU:
                return random.nextInt(60) + 1;
            default:
                return random.nextInt(100) + 1;
        }
    }

    public OracleDrawsType fromValue(int value) {
        return values()[value];
    }

    @JsonValue
    public int toValue() {
        return this.ordinal();
    }
}
