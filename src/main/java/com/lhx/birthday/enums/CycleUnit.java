package com.lhx.birthday.enums;

import cn.hutool.core.date.DateUtil;
import com.lhx.birthday.annotation.ApiEnumProperty;
import com.lhx.birthday.util.SpringContextUtil;

import java.util.Date;

public enum CycleUnit {
    @ApiEnumProperty("0:非续订")
    NONE, // 非续订
    @ApiEnumProperty("1:周")
    WEEK,
    @ApiEnumProperty("2:月")
    MONTH,
    @ApiEnumProperty("3:季")
    QUARTER,
    @ApiEnumProperty("4:年")
    YEAR;

    private Date nextCycleSandbox(Date date) {
        if (date == null) {
            return null;
        }
        switch (this) {
            case WEEK:
                return DateUtil.offsetMinute(date, 3);
            case MONTH:
                return DateUtil.offsetMinute(date, 5);
            case QUARTER:
                return DateUtil.offsetMinute(date, 15);
            case YEAR:
                return DateUtil.offsetMinute(date, 60);
            default:
                return date;
        }
    }

    public Date nextCycle(Date date) {
        if (SpringContextUtil.isDev() || SpringContextUtil.isTest()) {
            return nextCycleSandbox(date);
        }
        if (date == null) {
            return null;
        }
        switch (this) {
            case WEEK:
                return DateUtil.offsetWeek(date, 1);
            case MONTH:
                return DateUtil.offsetMonth(date, 1);
            case QUARTER:
                return DateUtil.offsetMonth(date, 3);
            case YEAR:
                return DateUtil.offsetMonth(date, 12);
            default:
                return date;
        }
    }

}
