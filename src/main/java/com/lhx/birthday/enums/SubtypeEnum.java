package com.lhx.birthday.enums;

//苹果回调返回状态列表（不全，具体可以看官网，开头是地址）
public enum SubtypeEnum {
    //********************************notificationType****************************************************//
    CONSUMPTION_REQUEST("CONSUMPTION_REQUEST", "表示客户对消耗品内购发起退款申请，App Store 要求您提供消费数据。"),
    DID_CHANGE_RENEWAL_PREF("DID_CHANGE_RENEWAL_PREF","如果subtype是UPGRADE，则用户升级了他们的订阅。升级立即生效，开始一个新的计费周期，用户会收到前一周期未使用部分的按比例退款。如果subtype是DOWNGRADE，则用户将其订阅降级或交叉分级。降级在下次续订时生效。当前活动的计划不受影响。"),
    DID_CHANGE_RENEWAL_STATUS("DID_CHANGE_RENEWAL_STATUS","指示用户对订阅续订状态进行了更改。如果subtype是AUTO_RENEW_ENABLED，则用户重新启用订阅自动续订。如果是，则是用户关闭了订阅自动续订，或者用户申请退款后App Store禁用了订阅自动续订。"),
    DID_FAIL_TO_RENEW("DID_FAIL_TO_RENEW","由于计费问题而未能续订。订阅进入计费重试期。"),
    DID_RENEW("DID_RENEW","指示订阅成功续订,如果subtype是BILLING_RECOVERY则之前无法续订的过期订阅现在已成功续订。如果子状态为空，则活动订阅已成功自动续订新的交易周期。"),
    EXPIRED("EXPIRED","订阅已过期"),
    GRACE_PERIOD_EXPIRED("GRACE_PERIOD_EXPIRED","表示计费宽限期已结束，无需续订，因此您可以关闭对服务或内容的访问。"),
    OFFER_REDEEMED("OFFER_REDEEMED","一种通知类型，连同其subtype -INITIAL_BUY指示用户兑换了促销优惠或优惠代码。"),
    PRICE_INCREASE("PRICE_INCREASE","系统已通知用户订阅价格上涨,如果subtype是PENDING，则用户尚未响应价格上涨。如果subtype是ACCEPTED，则用户已接受价格上涨"),
    REFUND("REFUND","表示 App Store 成功为消耗性应用内购买、非消耗性应用内购买、自动续订订阅或非续订订阅的交易退款。"),
    REFUND_DECLINED("REFUND_DECLINED","表示 App Store 拒绝了应用开发者发起的退款请求。"),
    RENEWAL_EXTENDED("RENEWAL_EXTENDED","表示 App Store 延长了开发者要求的订阅续订日期。"),
    SUBSCRIBED("SUBSCRIBED","。如果subtype是INITIAL_BUY，则用户是首次购买或通过家庭共享获得了对订阅的访问权限，如果是RESUBSCRIBE，则用户重新订阅或通过家庭共享获得对同一订阅或同一订阅组中的另一个订阅的访问权限"),
    //********************************subtype****************************************************//
    INITIAL_BUY("INITIAL_BUY","notificationType SUBSCRIBED  带有此信息的通知表明用户是第一次购买订阅"),
    RESUBSCRIBE("RESUBSCRIBE","notificationType SUBSCRIBED 带有此信息的通知表示用户重新订阅或通过“家人共享”获得对同一订阅或同一订阅组中的另一个订阅的访问权限。"),
    DOWNGRADE("DOWNGRADE","notificationType DID_CHANGE_RENEWAL_PREF  带有此信息的通知表明用户降级了他们的订阅。降级在下次续订时生效。"),
    UPGRADE("UPGRADE","notificationType DID_CHANGE_RENEWAL_PREF  带有此信息的通知表明用户升级了他们的订阅。升级立即生效。"),
    AUTO_RENEW_ENABLED("AUTO_RENEW_ENABLED","notificationType DID_CHANGE_RENEWAL_STATUS  带有此信息的通知表示用户启用了订阅自动续订"),
    AUTO_RENEW_DISABLED("AUTO_RENEW_DISABLED","notificationType DID_CHANGE_RENEWAL_STATUS  带有此通知的通知表示用户禁用了订阅自动续订，或者在用户请求退款后 App Store 禁用了订阅自动续订。"),
    VOLUNTARY("VOLUNTARY","notificationTypesubtype EXPIRED  带有此通知的通知表示订阅在用户禁用订阅自动续订后过期"),
    BILLING_RETRY("BILLING_RETRY","带有此信息的通知表明订阅已过期，因为订阅未能在计费重试期结束前续订"),
    GRACE_PERIOD("GRACE_PERIOD","notificationTypesubtype  DID_FAIL_TO_RENEW  带有此信息的通知表明订阅由于计费问题而未能续订"),
    BILLING_RECOVERY("BILLING_RECOVERY","notificationTypesubtype DID_RENEW 带有此通知的通知表示之前未能续订的过期订阅现在已成功续订"),
    PENDING("PENDING","notificationTypesubtype PRICE_INCREASE 带有此通知的通知表示系统通知用户订阅价格上涨，但用户尚未接受"),
    ACCEPTED("ACCEPTED","notificationTypesubtype PRICE_INCREASE带有此通知的通知表示用户接受订阅价格上涨"),



    ;
    public String code;
    public String desc;
    SubtypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SubtypeEnum getByCode(String code) {
        for (SubtypeEnum value : values()) {
            if(value.code.equals(code)) return value;
        }
        return null;
    }

}
