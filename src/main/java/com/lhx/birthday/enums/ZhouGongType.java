package com.lhx.birthday.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.lhx.birthday.annotation.ApiEnum;
import com.lhx.birthday.annotation.ApiEnumProperty;

/**
 * <AUTHOR>
 */
@ApiEnum
public enum ZhouGongType {

    @ApiEnumProperty("Person")
    PEOPLE("人物"),

    @ApiEnumProperty("Animal")
    ANIMAL("动物"),

    @ApiEnumProperty("Plant")
    PLANT("植物"),

    @ApiEnumProperty("Item")
    ITEM("物品"),

    @ApiEnumProperty("Activity")
    ACTIVITY("活动"),

//    @ApiEnumProperty("Emotion")
//    EMOTION("情感"),

    @ApiEnumProperty("Life")
    LIFE("生活"),

    @ApiEnumProperty("Nature")
    NATURE("自然"),

    @ApiEnumProperty("Supernatural")
    SUPERNATURAL("鬼神"),

    @ApiEnumProperty("Building")
    BUILDING("建筑"),

    @ApiEnumProperty("Other")
    OTHER("其他"),

    @ApiEnumProperty("Pregnant")
    PREGNANT("孕妇解梦"),

    @ApiEnumProperty("Record")
    RECORD("记录梦境"),

    @ApiEnumProperty("Culture")
    CULTURE("解梦文化"),

    @ApiEnumProperty("Health")
    HEALTH("梦与健康"),

    @ApiEnumProperty("Zhougong")
    ZHOUGONG("原版周公解梦");

    private static final ZhouGongType[] VALUES = values();

    private String value;

    ZhouGongType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static ZhouGongType fromValue(int value) {
        if (value < 0 || value >= VALUES.length) {
            throw new IllegalArgumentException("Invalid value for ZodiacSignType: " + value);
        }
        return VALUES[value];
    }

    @JsonValue
    public int toValue() {
        return this.ordinal();
    }
}