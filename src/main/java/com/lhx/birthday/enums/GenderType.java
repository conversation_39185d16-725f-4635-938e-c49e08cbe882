package com.lhx.birthday.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.lhx.birthday.annotation.ApiEnum;
import com.lhx.birthday.annotation.ApiEnumProperty;

/**
 * 性别类型 0男 1女 2保密
 * <AUTHOR>
 */
@ApiEnum
public enum GenderType {

    @ApiEnumProperty("男")
    MALE,

    @ApiEnumProperty("女")
    FEMALE;

//    @ApiEnumProperty("保密")
//    SECRET;

    public GenderType fromValue(int value) {
        return values()[value];
    }

    @JsonValue
    public int toValue() {
        return this.ordinal();
    }
}
