package com.lhx.birthday.enums;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonValue;
import com.lhx.birthday.annotation.ApiEnumProperty;
import com.lhx.birthday.util.SpringContextUtil;

import java.util.Date;

/**
 * 1天(Day), 1个周(Week), 1个月(Moth), 1个季(Season), 半年（HalfAYear）, 1年(Year), 10年(Decade),100年(Year100)
 */
public enum OperateUnit {
    @ApiEnumProperty("0:天")
    DAY,
    @ApiEnumProperty("1:周")
    WEEK,
    @ApiEnumProperty("2:月")
    MONTH,
    @ApiEnumProperty("3:季")
    QUARTER,
    @ApiEnumProperty("4:半年")
    HALFYEAR,
    @ApiEnumProperty("5:年")
    YEAR,
    @ApiEnumProperty("6:10年")
    DECADE,
    @ApiEnumProperty("7:100年")
    YEAR100;

    public OperateUnit fromValue(int value) {
        return values()[value];
    }

    @JsonValue
    public int toValue() {
        return this.ordinal();
    }

    private Date nextCycleSandbox(Date date) {
        if (date == null) {
            return null;
        }
        switch (this) {
            case WEEK:
                return DateUtil.offsetMinute(date, 3);
            case MONTH:
                return DateUtil.offsetMinute(date, 5);
            case QUARTER:
                return DateUtil.offsetMinute(date, 15);
            case YEAR:
                return DateUtil.offsetMinute(date, 60);
            default:
                return date;
        }
    }

    public Date nextCycle(Date date) {
        if (SpringContextUtil.isDev() || SpringContextUtil.isTest()) {
            return nextCycleSandbox(date);
        }
        if (date == null) {
            return null;
        }
        switch (this) {
            case WEEK:
                return DateUtil.offsetWeek(date, 1);
            case MONTH:
                return DateUtil.offsetMonth(date, 1);
            case QUARTER:
                return DateUtil.offsetMonth(date, 3);
            case YEAR:
                return DateUtil.offsetMonth(date, 12);
            default:
                return date;
        }
    }

}
