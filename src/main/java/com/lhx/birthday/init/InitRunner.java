package com.lhx.birthday.init;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ijpay.alipay.AliPayApiConfig;
import com.ijpay.alipay.AliPayApiConfigKit;
import com.lhx.birthday.constant.RedisKeyConstant;
import com.lhx.birthday.entity.Corpus;
import com.lhx.birthday.entity.CorpusEn;
import com.lhx.birthday.entity.pay.AliPayBean;
import com.lhx.birthday.enums.OracleDrawsType;
import com.lhx.birthday.mapper.*;
import org.apache.commons.lang3.StringUtils;
import com.lhx.birthday.redis.RedisService;
import com.lhx.birthday.vo.profile.RegionLevel1VO;
import com.lhx.birthday.vo.profile.RegionLevel2VO;
import com.lhx.birthday.vo.profile.RegionLevel3VO;
import com.lhx.birthday.vo.profile.RegionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.lhx.birthday.constant.CommonConstant.ZODIAC_SIGN_TYPE;
import static com.lhx.birthday.constant.CommonConstant.ZODIAC_TYPE;

/**
 * 初始化数据
 * @version: 1.0
 */
@Slf4j
@Order(1)
@Component
public class InitRunner implements CommandLineRunner {

	@Autowired
	private RedisService redisService;

	@Autowired
	private CountryCodeMapper countryCodeMapper;

	@Value("${init}")
	private boolean init;

	@Resource
	private AliPayBean aliPayBean;

	@Autowired
	private CorpusMapper corpusMapper;

	@Autowired
	private CorpusEnMapper corpusEnMapper;

	@Override
	public void run(String... args) throws Exception {
		log.info("=======系统初始化======="+init);
		if(init){
			// 星盘语料
			initializeCorpusData();
			// 英文星盘语料
			initializeCorpusEnData();
		}
		log.info("======初始化阿里支付=======");
		AliPayApiConfig aliPayApiConfig = AliPayApiConfig.builder()
				.setAppId(aliPayBean.getAppId())
				.setAliPayPublicKey(aliPayBean.getPublicKey())
				.setAppCertPath(aliPayBean.getAppCertPath())
				.setAliPayCertPath(aliPayBean.getAliPayCertPath())
				.setAliPayRootCertPath(aliPayBean.getAliPayRootCertPath())
				.setCharset("UTF-8")
				.setPrivateKey(aliPayBean.getPrivateKey())
				.setServiceUrl(aliPayBean.getServerUrl())
				.setSignType("RSA2")
				// 普通公钥方式
				.build();
		// 证书模式
//				.buildByCert();
		AliPayApiConfigKit.putApiConfig(aliPayApiConfig);
	}

	/**
	 * 初始化星盘语料数据
	 */
	private void initializeCorpusData() {
		List<Corpus> corpusList = corpusMapper.findAll();
		for (Corpus corpus : corpusList) {
			// 基本星盘语料
			JSONObject jsonObject = createCorpusJsonObject(corpus);
			saveCorpusToRedis("", jsonObject, corpus);

			// 处理各种星盘类型的语料
			processChartContent(RedisKeyConstant.XINGPAN_TRANSIT_TYPE, corpus.getTransitChartContent(), corpus);
			processChartContent(RedisKeyConstant.XINGPAN_COMBINATION_TYPE, corpus.getCombinationChartContent(), corpus);
			processChartContent(RedisKeyConstant.XINGPAN_THIRDPROGRESSED_TYPE, corpus.getThirdprogressedChartContent(), corpus);
			processChartContent(RedisKeyConstant.XINGPAN_SECONDARYLIMIT_TYPE, corpus.getSecondarylimitChartContent(), corpus);
			
			// 新增语料类型
			processChartContent(RedisKeyConstant.XINGPAN_LUNARRETURN_TYPE, corpus.getLunarreturnChartContent(), corpus);
			processChartContent(RedisKeyConstant.XINGPAN_SOLARRETURN_TYPE, corpus.getSolarreturnChartContent(), corpus);
			processChartContent(RedisKeyConstant.XINGPAN_SOLARARC_TYPE, corpus.getSolararcChartContent(), corpus);
			processChartContent(RedisKeyConstant.XINGPAN_DEVELOPED_TYPE, corpus.getDevelopedChartContent(), corpus);
			processChartContent(RedisKeyConstant.XINGPAN_SMALLLIMIT_TYPE, corpus.getSmalllimitChartContent(), corpus);
			processChartContent(RedisKeyConstant.XINGPAN_NATALTWELVEPOINTER_TYPE, corpus.getNataltwelvepointerChartContent(), corpus);
			processChartContent(RedisKeyConstant.XINGPAN_NATALTHIRTEENPOINTER_TYPE, corpus.getNatalthirteenpointerChartContent(), corpus);
			processChartContent(RedisKeyConstant.XINGPAN_CURRENT_TYPE, corpus.getCurrentChartContent(), corpus);
			processChartContent(RedisKeyConstant.XINGPAN_COMPARISION_A_TYPE, corpus.getComparisionAChartContent(), corpus);
			processChartContent(RedisKeyConstant.XINGPAN_COMPARISION_B_TYPE, corpus.getComparisionBChartContent(), corpus);
			processChartContent(RedisKeyConstant.XINGPAN_COMPOSITETHIRPROGR_TYPE, corpus.getCompositethirprogrChartContent(), corpus);
			processChartContent(RedisKeyConstant.XINGPAN_COMPOSITESECONDARY_TYPE, corpus.getCompositesecondaryChartContent(), corpus);
			processChartContent(RedisKeyConstant.XINGPAN_MARKS_A_TYPE, corpus.getMarksAChartContent(), corpus);
			processChartContent(RedisKeyConstant.XINGPAN_MARKS_B_TYPE, corpus.getMarksBChartContent(), corpus);
			processChartContent(RedisKeyConstant.XINGPAN_MARKSTHIRPROGR_A_TYPE, corpus.getMarksthirprogrAChartContent(), corpus);
			processChartContent(RedisKeyConstant.XINGPAN_MARKSTHIRPROGR_B_TYPE, corpus.getMarksthirprogrBChartContent(), corpus);
			processChartContent(RedisKeyConstant.XINGPAN_MARKSSECPROGR_A_TYPE, corpus.getMarkssecprogrAChartContent(), corpus);
			processChartContent(RedisKeyConstant.XINGPAN_MARKSSECPROGR_B_TYPE, corpus.getMarkssecprogrBChartContent(), corpus);
			processChartContent(RedisKeyConstant.XINGPAN_TIMESMIDPOINT_TYPE, corpus.getTimesmidpointChartContent(), corpus);
			processChartContent(RedisKeyConstant.XINGPAN_TIMESMIDPOINTTHIRPROGR_TYPE, corpus.getTimesmidpointthirprogrChartContent(), corpus);
			processChartContent(RedisKeyConstant.XINGPAN_TIMESMIDPOINTSECPROGR_TYPE, corpus.getTimesmidpointsecprogrChartContent(), corpus);
			processChartContentMarkDown(RedisKeyConstant.XINGPAN_CHART_CONTENT_MARKDOWN_TYPE, corpus.getChartContentMarkdown(), corpus);
		}
	}

	/**
	 * 创建语料的 JSONObject
	 */
	private JSONObject createCorpusJsonObject(Corpus corpus) {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("title", corpus.getTitle());
		jsonObject.put("content", corpus.getContent());
		
		JSONObject newContentObj = JSONObject.parseObject(corpus.getNewContent());
		jsonObject.put("name", newContentObj.get("name"));
		jsonObject.put("keyword", newContentObj.get("keyword"));
		jsonObject.put("analysis", newContentObj.get("analysis"));
		jsonObject.put("explain", newContentObj.get("explain"));
		jsonObject.put("sentence", newContentObj.get("sentence"));
		jsonObject.put("suggest", newContentObj.get("suggest"));
		jsonObject.put("avoid", newContentObj.get("avoid"));
		
		return jsonObject;
	}

	/**
	 * 处理星盘语料内容并保存到Redis
	 */
	private void processChartContent(String chartType, String chartContent, Corpus corpus) {
		if (Objects.nonNull(chartContent)) {
			JSONObject chartContentObj = JSONObject.parseObject(chartContent);
			chartContentObj.put("title", corpus.getTitle());
			saveCorpusToRedis(chartType, chartContentObj, corpus);
		}
	}

	/**
	 * 将语料保存到Redis
	 */
	private void saveCorpusToRedis(String chartType, JSONObject contentObj, Corpus corpus) {
		if (Objects.nonNull(corpus.getHouseId()) && Objects.isNull(corpus.getPlanetAllowId())) {
			redisService.setString(RedisKeyConstant.XINGPAN_PLANT_ZODIADC_KEY + chartType + corpus.getPlanetId() + ":" + corpus.getHouseId(), contentObj.toJSONString());
		}
		
		if (Objects.nonNull(corpus.getSignId()) && Objects.isNull(corpus.getPlanetAllowId())) {
			redisService.setString(RedisKeyConstant.XINGPAN_PLANT_CONSTELLATION_KEY + chartType + corpus.getPlanetId() + ":" + corpus.getSignId(), contentObj.toJSONString());
		}
		
		if (Objects.nonNull(corpus.getPlanetAllowId())) {
			redisService.setString(RedisKeyConstant.XINGPAN_PLANT_ALLOW_KEY + chartType + corpus.getPlanetId() + ":" + corpus.getPlanetAllowId() + ":" + corpus.getAllow(), contentObj.toJSONString());
		}
	}

	/**
	 * 处理星盘语料内容并保存到Redis
	 */
	private void processChartContentMarkDown(String chartType, String chartContent, Corpus corpus) {
		if (Objects.nonNull(chartContent)) {
			saveCorpusMarkDownToRedis(chartType, chartContent, corpus);
		}
	}

	/**
	 * 将语料保存到Redis
	 */
	private void saveCorpusMarkDownToRedis(String chartType, String contentObj, Corpus corpus) {
//		if (Objects.nonNull(corpus.getHouseId()) && Objects.isNull(corpus.getPlanetAllowId())) {
//			redisService.setString(RedisKeyConstant.XINGPAN_PLANT_ZODIADC_KEY + chartType + corpus.getPlanetId() + ":" + corpus.getHouseId(), contentObj.toJSONString());
//		}

		if (Objects.nonNull(corpus.getSignId()) && Objects.isNull(corpus.getPlanetAllowId())) {
			redisService.setString(RedisKeyConstant.XINGPAN_PLANT_CONSTELLATION_KEY + chartType + corpus.getPlanetId() + ":" + corpus.getSignId(), contentObj);
		}

		if (Objects.nonNull(corpus.getPlanetAllowId())) {
			redisService.setString(RedisKeyConstant.XINGPAN_PLANT_ALLOW_KEY + chartType + corpus.getPlanetId() + ":" + corpus.getPlanetAllowId() + ":" + corpus.getAllow(), contentObj);
		}
	}

	/**
	 * 初始化英文星盘语料数据
	 */
	private void initializeCorpusEnData() {
		List<CorpusEn> corpusEnList = corpusEnMapper.findAll();
		for (CorpusEn corpusEn : corpusEnList) {
			// 基本星盘语料
			JSONObject jsonObject = createCorpusEnJsonObject(corpusEn);
			saveCorpusEnToRedis("", jsonObject, corpusEn);

			// 处理各种星盘类型的语料
			processChartContentEn(RedisKeyConstant.XINGPAN_TRANSIT_TYPE, corpusEn.getTransitChartContent(), corpusEn);
			processChartContentEn(RedisKeyConstant.XINGPAN_COMBINATION_TYPE, corpusEn.getCombinationChartContent(), corpusEn);
			processChartContentEn(RedisKeyConstant.XINGPAN_THIRDPROGRESSED_TYPE, corpusEn.getThirdprogressedChartContent(), corpusEn);
			processChartContentEn(RedisKeyConstant.XINGPAN_SECONDARYLIMIT_TYPE, corpusEn.getSecondarylimitChartContent(), corpusEn);

			// 新增语料类型
			processChartContentEn(RedisKeyConstant.XINGPAN_LUNARRETURN_TYPE, corpusEn.getLunarreturnChartContent(), corpusEn);
			processChartContentEn(RedisKeyConstant.XINGPAN_SOLARRETURN_TYPE, corpusEn.getSolarreturnChartContent(), corpusEn);
			processChartContentEn(RedisKeyConstant.XINGPAN_SOLARARC_TYPE, corpusEn.getSolararcChartContent(), corpusEn);
			processChartContentEn(RedisKeyConstant.XINGPAN_DEVELOPED_TYPE, corpusEn.getDevelopedChartContent(), corpusEn);
			processChartContentEn(RedisKeyConstant.XINGPAN_SMALLLIMIT_TYPE, corpusEn.getSmalllimitChartContent(), corpusEn);
			processChartContentEn(RedisKeyConstant.XINGPAN_NATALTWELVEPOINTER_TYPE, corpusEn.getNataltwelvepointerChartContent(), corpusEn);
			processChartContentEn(RedisKeyConstant.XINGPAN_NATALTHIRTEENPOINTER_TYPE, corpusEn.getNatalthirteenpointerChartContent(), corpusEn);
			processChartContentEn(RedisKeyConstant.XINGPAN_CURRENT_TYPE, corpusEn.getCurrentChartContent(), corpusEn);
			processChartContentEn(RedisKeyConstant.XINGPAN_COMPARISION_A_TYPE, corpusEn.getComparisionAChartContent(), corpusEn);
			processChartContentEn(RedisKeyConstant.XINGPAN_COMPARISION_B_TYPE, corpusEn.getComparisionBChartContent(), corpusEn);
			processChartContentEn(RedisKeyConstant.XINGPAN_COMPOSITETHIRPROGR_TYPE, corpusEn.getCompositethirprogrChartContent(), corpusEn);
			processChartContentEn(RedisKeyConstant.XINGPAN_COMPOSITESECONDARY_TYPE, corpusEn.getCompositesecondaryChartContent(), corpusEn);
			processChartContentEn(RedisKeyConstant.XINGPAN_MARKS_A_TYPE, corpusEn.getMarksAChartContent(), corpusEn);
			processChartContentEn(RedisKeyConstant.XINGPAN_MARKS_B_TYPE, corpusEn.getMarksBChartContent(), corpusEn);
			processChartContentEn(RedisKeyConstant.XINGPAN_MARKSTHIRPROGR_A_TYPE, corpusEn.getMarksthirprogrAChartContent(), corpusEn);
			processChartContentEn(RedisKeyConstant.XINGPAN_MARKSTHIRPROGR_B_TYPE, corpusEn.getMarksthirprogrBChartContent(), corpusEn);
			processChartContentEn(RedisKeyConstant.XINGPAN_MARKSSECPROGR_A_TYPE, corpusEn.getMarkssecprogrAChartContent(), corpusEn);
			processChartContentEn(RedisKeyConstant.XINGPAN_MARKSSECPROGR_B_TYPE, corpusEn.getMarkssecprogrBChartContent(), corpusEn);
			processChartContentEn(RedisKeyConstant.XINGPAN_TIMESMIDPOINT_TYPE, corpusEn.getTimesmidpointChartContent(), corpusEn);
			processChartContentEn(RedisKeyConstant.XINGPAN_TIMESMIDPOINTTHIRPROGR_TYPE, corpusEn.getTimesmidpointthirprogrChartContent(), corpusEn);
			processChartContentEn(RedisKeyConstant.XINGPAN_TIMESMIDPOINTSECPROGR_TYPE, corpusEn.getTimesmidpointsecprogrChartContent(), corpusEn);
			processChartContentMarkDownEn(RedisKeyConstant.XINGPAN_CHART_CONTENT_MARKDOWN_TYPE, corpusEn.getChartContentMarkdown(), corpusEn);
		}
	}

	/**
	 * 创建英文语料的 JSONObject
	 */
	private JSONObject createCorpusEnJsonObject(CorpusEn corpusEn) {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("title", corpusEn.getTitle());
		jsonObject.put("content", corpusEn.getContent());

		if (StringUtils.isNotEmpty(corpusEn.getNewContent())) {
			JSONObject newContentObj = JSONObject.parseObject(corpusEn.getNewContent());
			jsonObject.put("name", newContentObj.get("name"));
			jsonObject.put("keyword", newContentObj.get("keyword"));
			jsonObject.put("analysis", newContentObj.get("analysis"));
			jsonObject.put("explain", newContentObj.get("explain"));
			jsonObject.put("sentence", newContentObj.get("sentence"));
			jsonObject.put("suggest", newContentObj.get("suggest"));
			jsonObject.put("avoid", newContentObj.get("avoid"));
		}

		return jsonObject;
	}

	/**
	 * 处理英文星盘语料内容并保存到Redis
	 */
	private void processChartContentEn(String chartType, String chartContent, CorpusEn corpusEn) {
		if (Objects.nonNull(chartContent)) {
			System.out.println(chartContent);
			JSONObject chartContentObj = JSONObject.parseObject(chartContent);
			chartContentObj.put("title", corpusEn.getTitle());
			saveCorpusEnToRedis(chartType, chartContentObj, corpusEn);
		}
	}

	/**
	 * 将英文语料保存到Redis
	 */
	private void saveCorpusEnToRedis(String chartType, JSONObject contentObj, CorpusEn corpusEn) {
		if (Objects.nonNull(corpusEn.getHouseId()) && Objects.isNull(corpusEn.getPlanetAllowId())) {
			redisService.setString(RedisKeyConstant.XINGPAN_PLANT_ZODIADC_EN_KEY + chartType + corpusEn.getPlanetId() + ":" + corpusEn.getHouseId(), contentObj.toJSONString());
		}

		if (Objects.nonNull(corpusEn.getSignId()) && Objects.isNull(corpusEn.getPlanetAllowId())) {
			redisService.setString(RedisKeyConstant.XINGPAN_PLANT_CONSTELLATION_EN_KEY + chartType + corpusEn.getPlanetId() + ":" + corpusEn.getSignId(), contentObj.toJSONString());
		}

		if (Objects.nonNull(corpusEn.getPlanetAllowId())) {
			redisService.setString(RedisKeyConstant.XINGPAN_PLANT_ALLOW_EN_KEY + chartType + corpusEn.getPlanetId() + ":" + corpusEn.getPlanetAllowId() + ":" + corpusEn.getAllow(), contentObj.toJSONString());
		}
	}

	/**
	 * 处理英文星盘语料内容并保存到Redis
	 */
	private void processChartContentMarkDownEn(String chartType, String chartContent, CorpusEn corpusEn) {
		if (Objects.nonNull(chartContent)) {
			saveCorpusMarkDownEnToRedis(chartType, chartContent, corpusEn);
		}
	}

	/**
	 * 将英文语料保存到Redis
	 */
	private void saveCorpusMarkDownEnToRedis(String chartType, String contentObj, CorpusEn corpusEn) {
		// 处理宫位语料
		if (Objects.nonNull(corpusEn.getHouseId()) && Objects.isNull(corpusEn.getPlanetAllowId())) {
			redisService.setString(RedisKeyConstant.XINGPAN_PLANT_ZODIADC_EN_KEY + chartType + corpusEn.getPlanetId() + ":" + corpusEn.getHouseId(), contentObj);
		}

		// 处理星座语料
		if (Objects.nonNull(corpusEn.getSignId()) && Objects.isNull(corpusEn.getPlanetAllowId())) {
			redisService.setString(RedisKeyConstant.XINGPAN_PLANT_CONSTELLATION_EN_KEY + chartType + corpusEn.getPlanetId() + ":" + corpusEn.getSignId(), contentObj);
		}

		// 处理相位语料
		if (Objects.nonNull(corpusEn.getPlanetAllowId())) {
			redisService.setString(RedisKeyConstant.XINGPAN_PLANT_ALLOW_EN_KEY + chartType + corpusEn.getPlanetId() + ":" + corpusEn.getPlanetAllowId() + ":" + corpusEn.getAllow(), contentObj);
		}
	}
}
