package com.lhx.birthday.constant;

/**
 * @Description: 缘分居接口
 * @author: lhx
 * @date: 2024年01月24日 15:47
 */
public class ApiConstant {

    /**
     * 占卜 - 星座/生肖每日运势
     */
    public static final String API_DAYLI_HOROSCOPE = "https://api.yuanfenju.com/index.php/v1/Zhanbu/yunshi";

    /**
     * 占卜 - 星座/生肖每日运势
     */
    public static final String API_DAYLI_birthday = "https://api.yuanfenju.com/index.php/v1/Zhanbu/yunshi";


    /**
     * 八字 - 九星命理
     */
    public static final String API_BAZI_JIUXING = "https://api.yuanfenju.com/index.php/v1/Bazi/jiuxing";

    /**
     * 八字 - 八字合婚
     */
    public static final String API_BAZI_HEHUN = "https://api.yuanfenju.com/index.php/v1/Bazi/hehun";

    /**
     * 八字-八字合盘
     */
    public static final String API_BAZI_HEPAN = "https://api.yuanfenju.com/index.php/v1/Bazi/hepan";

    /**
     * 八字 - 八字排盘
     */
    public static final String API_BAZI_PAIPAN = "https://api.yuanfenju.com/index.php/v1/Bazi/paipan";

    /**
     * 八字-八字测算
     */
    public static final String API_BAZI_CESUAN = "https://api.yuanfenju.com/index.php/v1/Bazi/cesuan";

    /**
     * 八字-八字精盘
     */
    public static final String API_BAZI_JINGPAN = "https://api.yuanfenju.com/index.php/v1/Bazi/jingpan";

    /**
     * 八字-八字精算
     */
    public static final String API_BAZI_JINGSUAN = "https://api.yuanfenju.com/index.php/v1/Bazi/jingsuan";

    /**
     * 八字-未来运势
     */
    public static final String API_BAZI_WEILAI = "https://api.yuanfenju.com/index.php/v1/Bazi/weilai";

    /**
     * 八字-紫微排盘
     */
    public static final String API_BAZI_ZWPAN = "https://api.yuanfenju.com/index.php/v1/Bazi/zwpan";

    /**
     * 工具 - 老黄历
     */
    public static final String API_LAO_HANG_LI = "https://api.yuanfenju.com/index.php/v1/Gongju/laohuangli";

    /**
     * 工具 - 择吉日
     */
    public static final String API_ZESHI = "https://api.yuanfenju.com/index.php/v1/Gongju/zeshi";

    /**
     * 工具 - 周公解梦
     */
    public static final String API_ZHOUGONG = "https://api.yuanfenju.com/index.php/v1/Gongju/zhougong";

    /**
     * 工具 - 节气工具
     */
    public static final String API_JIEQI = "https://api.yuanfenju.com/index.php/v1/Gongju/jieqi";

    /**
     * 流盘-紫微流盘
     */
    public static final String API_LIUPAN_ZWLPAN = "https://api.yuanfenju.com/index.php/v1/Liupan/zwlpan";

    /**
     * 预测-六道轮回
     */
    public static final String API_YUCE_LIUDAOLUNHUI = "https://api.yuanfenju.com/index.php/v1/Yuce/liudaolunhui";

    /**
     * 预测-正缘画像
     */
    public static final String API_YUCE_ZHENGYUAN = "https://api.yuanfenju.com/index.php/v1/Yuce/zhengyuan";

    /**
     * 预测-结婚预测
     */
    public static final String API_YUCE_JIEHUN = "https://api.yuanfenju.com/index.php/v1/Yuce/jiehun";

    /**
     * 占卜-一张牌占卜法
     */
    public static final String API_ZHANBU_TALUOZHANBU = "https://api.yuanfenju.com/index.php/v1/Zhanbu/taluozhanbu";

    /**
     * 占卜-塔罗洗牌
     */
    public static final String API_ZHANBU_TALUOXIPAI = "https://api.yuanfenju.com/index.php/v1/Zhanbu/taluoxipai";

    /**
     * 占卜-多牌阵占卜法
     */
    public static final String API_ZHANBU_TALUOSPREADS = "https://api.yuanfenju.com/index.php/v1/Zhanbu/taluospreads";

    /**
     * 占卜-小六壬占卜
     */
    public static final String API_ZHANBU_XIAOLIUREN = "https://api.yuanfenju.com/index.php/v1/Zhanbu/xiaoliuren";

    /**
     * 占卜-摇卦占卜
     */
    public static final String API_ZHANBU_YAOGUA = "https://api.yuanfenju.com/index.php/v1/Zhanbu/yaogua";

    /**
     * 占卜-每日一占
     */
    public static final String API_ZHANBU_MEIRI = "https://api.yuanfenju.com/index.php/v1/Zhanbu/meiri";

    /**
     * 灵签
     */
    public static final String API_FOZU_LINGQIANG = "https://api.yuanfenju.com/index.php/v1/Lingqian/fozu";
    public static final String API_LVZU_LINGQIANG = "https://api.yuanfenju.com/index.php/v1/Lingqian/lvzu";
    public static final String API_MAZU_LINGQIANG = "https://api.yuanfenju.com/index.php/v1/Lingqian/mazu";
    public static final String API_YUELAO_LINGQIANG = "https://api.yuanfenju.com/index.php/v1/Lingqian/yuelao";
    public static final String API_GUANYIN_LINGQIANG = "https://api.yuanfenju.com/index.php/v1/Lingqian/guanyin";
    public static final String API_ZHUGE_LINGQIANG = "https://api.yuanfenju.com/index.php/v1/Lingqian/zhuge";

    /**
     * 配对
     */
    public static final String API_PEIDUI_XINGZUO = "https://api.yuanfenju.com/index.php/v1/Peidui/xingzuo";
    public static final String API_PEIDUI_SHENGXIAO = "https://api.yuanfenju.com/index.php/v1/Peidui/shengxiao";

    /**
     * 剩余次查询
     */
    public static final String API_REMAIN_COUNT = "https://api.yuanfenju.com/index.php/v1/Free/querymerchant";

    /**
     * 在线起名
     */
    public static final String API_QIMING = "https://api.yuanfenju.com/index.php/v1/Dafen/qiming";


    /**
     * 起名打分
     */
    public static final String API_QMDAFEN = "https://api.yuanfenju.com/index.php/v1/Dafen/qmdafen";


    /**
     * 姓名打分
     */
    public static final String API_XINGMING = "https://api.yuanfenju.com/index.php/v1/Dafen/xingming";

    /**
     * ==========星盘api==========
     */


    /**
     * 新闻内容
     */
    public static final String API_XINGPAN_ARTICLE = "/article/details";

    /**
     * 星盘 - 本命盘
     */
    public static final String API_XINGPAN_CHART_NATAL = "/chart/natal";

    /**
     * 星盘 - 行运盘
     */
    public static final String API_XINGPAN_CHART_TRANSIT = "/chart/transit";

    /**
     * 星盘 - 年运报告
     */
    public static final String API_XINGPAN_YEAR_REPORT = "http://adminxg.robustcn.com/appraise/activitytransportuserk/activity";

    /**
     * 星盘 - 语料列表
     */
    public static final String API_XINGPAN_CORPUSCONSTELLATION_LIST = "/corpusconstellation/getlist";

    /**
     * 星盘语料-语料
     */
    public static final String API_XINGPAN_LUCK_DAY = "/luck/day";
    public static final String API_XINGPAN_LUCK_WEEK = "/luck/weeks";
    public static final String API_XINGPAN_LUCK_MOON = "/luck/moon";
    public static final String API_XINGPAN_LUCK_YEAR = "/luck/year";

    /**
     * 星盘语料-缘分合盘
     */
    public static final String API_XINGPAN_EVALUATIONCOMBINATION = "/evaluationcombination/add";

    /**
     * 星盘 - 组合盘
     */
    public static final String API_XINGPAN_CHART_COMPOSITE = "/chart/composite";

    /**
     * 星盘 - 三限盘
     */
    public static final String API_XINGPAN_CHART_THIRDPROGRESSED = "/chart/thirdprogressed";

    /**
     * 星盘 - 次限盘
     */
    public static final String API_XINGPAN_CHART_SECONDPROGRESSED = "/chart/secondarylimit";
    
    /**
     * 星盘 - 月返照
     */
    public static final String API_XINGPAN_CHART_LUNARRETURN = "/chart/lunarreturndouble";
    
    /**
     * 星盘 - 日返照
     */
    public static final String API_XINGPAN_CHART_SOLARRETURN = "/chart/solarreturndouble";
    
    /**
     * 星盘 - 太阳弧
     */
    public static final String API_XINGPAN_CHART_SOLARARC = "/chart/solararc";
    
    /**
     * 星盘 - 法达盘
     */
    public static final String API_XINGPAN_CHART_DEVELOPED = "/chart/developed";
    
    /**
     * 星盘 - 小限盘
     */
    public static final String API_XINGPAN_CHART_SMALLLIMIT = "/chart/smalllimit";
    
    /**
     * 星盘 - 十二分盘
     */
    public static final String API_XINGPAN_CHART_NATALTWELVEPOINTER = "/chart/nataltwelvepointer";
    
    /**
     * 星盘 - 十三分盘
     */
    public static final String API_XINGPAN_CHART_NATALTHIRTEENPOINTER = "/chart/natalthirteenpointer";
    
    /**
     * 星盘 - 天象盘
     */
    public static final String API_XINGPAN_CHART_CURRENT = "/chart/current";
    
    /**
     * 星盘 - 比较盘-a
     */
    public static final String API_XINGPAN_CHART_COMPARISION_A = "/chart/comparision";
    
    /**
     * 星盘 - 比较盘-b
     */
    public static final String API_XINGPAN_CHART_COMPARISION_B = "/chart/comparision";
    
    /**
     * 星盘 - 组合三限盘
     */
    public static final String API_XINGPAN_CHART_COMPOSITETHIRPROGR = "/chart/compositeThirprogr";
    
    /**
     * 星盘 - 组合次限盘
     */
    public static final String API_XINGPAN_CHART_COMPOSITESECONDARY = "/chart/compositeSecondary";
    
    /**
     * 星盘 - 马克思盘-a
     */
    public static final String API_XINGPAN_CHART_MARKS_A = "/chart/marks";
    
    /**
     * 星盘 - 马克思盘-b
     */
    public static final String API_XINGPAN_CHART_MARKS_B = "/chart/marks";
    
    /**
     * 星盘 - 马盘三限盘-a
     */
    public static final String API_XINGPAN_CHART_MARKSTHIRPROGR_A = "/chart/marksThirprogr";
    
    /**
     * 星盘 - 马盘三限盘-b
     */
    public static final String API_XINGPAN_CHART_MARKSTHIRPROGR_B = "/chart/marksThirprogr";
    
    /**
     * 星盘 - 马盘次限盘-a
     */
    public static final String API_XINGPAN_CHART_MARKSSECPROGR_A = "/chart/marksSecprogr";
    
    /**
     * 星盘 - 马盘次限盘-b
     */
    public static final String API_XINGPAN_CHART_MARKSSECPROGR_B = "/chart/marksSecprogr";
    
    /**
     * 星盘 - 时空盘
     */
    public static final String API_XINGPAN_CHART_TIMESMIDPOINT = "/chart/timesmidpoint";
    
    /**
     * 星盘 - 时空三限盘
     */
    public static final String API_XINGPAN_CHART_TIMESMIDPOINTTHIRPROGR = "/chart/timesmidpointThirprogr";
    
    /**
     * 星盘 - 时空次限盘
     */
    public static final String API_XINGPAN_CHART_TIMESMIDPOINTSECPROGR = "/chart/timesmidpointSecprogr";

    /**
     * 星历表
     */
    public static final String API_XINGPAN_EPHEMERIS = "/astrology/sign/ephemeris";

    /**
     * 星相日历
     */
    public static final String API_XINGPAN_ASTROCALENDAR = "/astrology/sign/astrocalendar";
}