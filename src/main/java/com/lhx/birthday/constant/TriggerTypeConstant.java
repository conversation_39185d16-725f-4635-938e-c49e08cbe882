package com.lhx.birthday.constant;

/**
 * 用户好评事件类型常量
 */
public class TriggerTypeConstant {

    /**
     * 使用天数触发
     */
    public static final Integer TRIGGER_TYPE_USE_DAYS = 2;

    /**
     * 使用次数触发
     */
    public static final Integer TRIGGER_TYPE_USE_CNT = 3;

    /**
     * 订阅次数触发
     */
    public static final Integer TRIGGER_TYPE_SUB_NTH = 4;

    /**
     * 分享触发
     */
    public static final Integer TRIGGER_TYPE_SHARE = 6;

    /**
     * 会员功能使用次数
     */
    public static final Integer TRIGGER_TYPE_VIP_FUNC_USE_CNT = 7;

    /**
     * 非会员功能使用次数
     */
    public static final Integer TRIGGER_TYPE_NOVIP_FUNC_USE_CNT = 8;


    /**
     * 检查触发类型是否合法
     * @param triggerType 触发类型
     * @return 是否合法
     */
    public static boolean isValidTriggerType(Integer triggerType) {
        return triggerType != null && (
                triggerType.equals(TRIGGER_TYPE_USE_DAYS) ||
                        triggerType.equals(TRIGGER_TYPE_USE_CNT) ||
                        triggerType.equals(TRIGGER_TYPE_SUB_NTH) ||
                        triggerType.equals(TRIGGER_TYPE_SHARE) ||
                        triggerType.equals(TRIGGER_TYPE_VIP_FUNC_USE_CNT) ||
                        triggerType.equals(TRIGGER_TYPE_NOVIP_FUNC_USE_CNT)
        );
    }
}