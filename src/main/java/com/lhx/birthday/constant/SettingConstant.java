package com.lhx.birthday.constant;

import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR> lhx
 * @date 2023/7/28 11:17
 */
public class SettingConstant {

    /**
     * 是否为会员标识
     */
    public static final Integer IS_VIP = 1;

    /**
     * 普通用户标识
     */
    public static final Integer NOT_VIP = 0;

    /**
     * 用户正常状态标识
     */
    public static final Integer USER_NORMAL = 0; // 表示用户正常

    /**
     * 用户注销状态标识
     */
    public static final Integer USER_CANCELED = 1; // 表示用户已注销

    /**
     * 用户默认头像
     */
    public static final String DEFAULT_AVATAR = "/avatar/user_default_icon.png";

    /**
     * 档案默认头像
     */
    public static final String DEFAULT_PROFILE_AVATAR = "/avatar/user_default_icon.png";

    /**
     * 生肖图片Url
      */
    public static final String DEFAULT_ZODIAC_PIC_URL = "/profile/animals/";

    /**
     * 星座图片Url
     */
    public static final String DEFAULT_ZODIAC_SIGN_PIC_URL = "/profile/constellation/";

    /**
     * 会员最多档案个数
     */
    public static final Integer VIP_MAX_PROFILE_CONTACT = 200;

    /**
     * 普通用户最多档案个数
     */
    public static final Integer USER_MAX_PROFILE_CONTACT = 5;

    /**
     * 会员最多档案分组个数
     */
    public static final Integer VIP_MAX_PROFILE_GROUP_CONTACT = 200;

    /**
     * 普通用户最多档案分组个数
     */
    public static final Integer USER_MAX_PROFILE_GROUP_CONTACT = 200;

}
