<?xml version="1.0" encoding="UTF-8"?>
<configuration  scan="true" scanPeriod="10 seconds">
    <contextName>logback</contextName>
    <property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread][%X{traceId}] %-5level %c{2}:%L - %msg%n" />
    <property name="LOG_PATH" value="/data/logs" />
<!--    <property name="LOG_PATH" value="/Users/<USER>/Downloads/" />-->

    <!--0. 日志格式和颜色渲染 -->
    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter" />
    <conversionRule conversionWord="wex" converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter" />
    <conversionRule conversionWord="wEx" converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter" />
    <!-- 彩色日志格式 -->
    <property name="CONSOLE_LOG_PATTERN" value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <!--此日志appender是为开发使用，只配置最底级别，控制台输出的日志级别是大于或等于此级别的日志信息-->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>debug</level>
        </filter>
        <encoder>
            <Pattern>${CONSOLE_LOG_PATTERN}</Pattern>
            <!-- 设置字符集 -->
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <!-- 文件appender配置，按年月日存储日志文件，超过30天清理 -->
<!--    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">-->
<!--        <file>${LOG_PATH}/api.ln.log</file>-->
<!--        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">-->
<!--            &lt;!&ndash; 日志文件输出的位置以及文件名称的构成 &ndash;&gt;-->
<!--            <fileNamePattern>${LOG_PATH}/log.%d{yyyy-MM-dd_HH}.log</fileNamePattern>-->
<!--            &lt;!&ndash; 清理日志文件的保留策略 &ndash;&gt;-->
<!--            <maxHistory>30</maxHistory>-->
<!--        </rollingPolicy>-->
<!--        <encoder>-->
<!--            <Pattern>${CONSOLE_LOG_PATTERN}</Pattern>-->
<!--            &lt;!&ndash; 设置字符集 &ndash;&gt;-->
<!--            <charset>UTF-8</charset>-->
<!--        </encoder>-->
<!--    </appender>-->


    <root level="info">
        <appender-ref ref="CONSOLE" />
<!--        <appender-ref ref="FILE" />-->
    </root>


</configuration>