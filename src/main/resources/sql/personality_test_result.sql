CREATE TABLE `personality_test_result` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `type_code` varchar(10) DEFAULT NULL COMMENT '类型代码',
  `e_score` int(11) DEFAULT 0 COMMENT 'E维度分数',
  `i_score` int(11) DEFAULT 0 COMMENT 'I维度分数',
  `s_score` int(11) DEFAULT 0 COMMENT 'S维度分数',
  `n_score` int(11) DEFAULT 0 COMMENT 'N维度分数',
  `t_score` int(11) DEFAULT 0 COMMENT 'T维度分数',
  `f_score` int(11) DEFAULT 0 COMMENT 'F维度分数',
  `j_score` int(11) DEFAULT 0 COMMENT 'J维度分数',
  `p_score` int(11) DEFAULT 0 COMMENT 'P维度分数',
  `test_time` datetime DEFAULT NULL COMMENT '测试时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  <PERSON>EY `idx_type_code` (`type_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='性格测试结果表'; 